<?php
/**
 * Setup test session for vital signs testing
 */
define('AIVORA_SECURITY', true);

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database
require_once __DIR__ . '/config/database.php';

echo "<!DOCTYPE html>\n<html><head><meta charset='UTF-8'><title>Setup Test Session</title></head><body>";
echo "<h2>🔧 Setup Test Session for Vital Signs</h2>";

try {
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception('Database connection failed');
    }
    
    // Get first available user
    $user_result = $conn->query("SELECT id, username, name, role, facility_id FROM users LIMIT 1");
    
    if (!$user_result || $user_result->num_rows === 0) {
        echo "<p style='color: red;'>❌ No users found in database. Please create a user first.</p>";
        echo "<p>You can create a test user with this SQL:</p>";
        echo "<pre>INSERT INTO users (username, password, name, role, facility_id) VALUES ('testuser', 'password', 'Test User', 'admin', 1);</pre>";
    } else {
        $user = $user_result->fetch_assoc();
        
        // Setup session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['facility_id'] = $user['facility_id'];
        $_SESSION['facility_name'] = 'Test Facility';
        $_SESSION['last_activity'] = time();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p style='color: #155724; margin: 0;'>✅ <strong>Test session setup successful!</strong></p>";
        echo "<p style='margin: 10px 0 0 0;'><strong>Session Data:</strong></p>";
        echo "<ul style='margin: 5px 0;'>";
        echo "<li>User ID: {$user['id']}</li>";
        echo "<li>Username: {$user['username']}</li>";
        echo "<li>Name: {$user['name']}</li>";
        echo "<li>Role: {$user['role']}</li>";
        echo "<li>Facility ID: {$user['facility_id']}</li>";
        echo "<li>Session ID: " . session_id() . "</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p><strong>🎯 Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li><a href='test_vital_signs_direct.php' style='color: #007bff;'>Go to Vital Signs Test Page</a></li>";
        echo "<li><a href='debug_vital_signs_system.php' style='color: #007bff;'>Run System Debug</a></li>";
        echo "<li>Test the original form in the elderly detail page</li>";
        echo "</ol>";
        
        // Check elderly data too
        $elderly_result = $conn->query("SELECT COUNT(*) as count FROM elderly");
        if ($elderly_result) {
            $elderly_count = $elderly_result->fetch_assoc()['count'];
            if ($elderly_count > 0) {
                echo "<p style='color: #28a745;'>✅ Found {$elderly_count} elderly records in database</p>";
            } else {
                echo "<p style='color: #dc3545;'>⚠️ No elderly records found. You may need to add some elderly data first.</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>