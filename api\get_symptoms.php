<?php
// get_symptoms.php - API สำหรับดึงข้อมูลอาการที่บันทึกไว้

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

header('Content-Type: application/json; charset=utf-8');

// เริ่ม session และโหลด auth system
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// โหลด auth functions
require_once __DIR__ . '/../includes/auth.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์การเข้าถึง
if (!SessionManager::hasPermission('elderly')) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'คุณไม่มีสิทธิ์ในการดูข้อมูลอาการ'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

try {
    // รับพารามิเตอร์
    $elderly_id = filter_input(INPUT_GET, 'elderly_id', FILTER_VALIDATE_INT);
    $limit = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT) ?: 10;
    $date_from = filter_input(INPUT_GET, 'date_from', FILTER_SANITIZE_STRING);
    $date_to = filter_input(INPUT_GET, 'date_to', FILTER_SANITIZE_STRING);
    
    if (!$elderly_id) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณาระบุ elderly_id'
        ]);
        exit;
    }
    
    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและอยู่ในสถานพยาบาลเดียวกัน
    $elderly_check_sql = "SELECT id, first_name, last_name, facility_id FROM elderly WHERE id = ?";
    $elderly_check_params = [$elderly_id];
    
    if ($_SESSION['user_role'] !== 'admin') {
        $elderly_check_sql .= " AND facility_id = ?";
        $elderly_check_params[] = $_SESSION['facility_id'];
    }
    
    $elderly_check_stmt = $conn->prepare($elderly_check_sql);
    $elderly_check_stmt->bind_param(str_repeat('i', count($elderly_check_params)), ...$elderly_check_params);
    $elderly_check_stmt->execute();
    $elderly_result = $elderly_check_stmt->get_result();
    
    if ($elderly_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือคุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้'
        ]);
        exit;
    }
    
    $elderly_info = $elderly_result->fetch_assoc();
    
    // ตรวจสอบว่ามีตาราง symptoms_records หรือไม่
    $check_table = $conn->query("SHOW TABLES LIKE 'symptoms_records'");
    if (!$check_table || $check_table->num_rows === 0) {
        echo json_encode([
            'success' => true,
            'data' => [
                'elderly_info' => $elderly_info,
                'symptoms' => [],
                'total_count' => 0,
                'summary' => [
                    'total_records' => 0,
                    'mild' => 0,
                    'moderate' => 0,
                    'severe' => 0,
                    'critical' => 0,
                    'latest_date' => null
                ]
            ],
            'message' => 'ยังไม่มีข้อมูลการบันทึกอาการ'
        ]);
        exit;
    }
    
    // สร้าง WHERE clause สำหรับการค้นหา
    $where_conditions = ["elderly_id = ?"];
    $params = [$elderly_id];
    $param_types = "i";
    
    if ($date_from) {
        $where_conditions[] = "record_date >= ?";
        $params[] = $date_from;
        $param_types .= "s";
    }
    
    if ($date_to) {
        $where_conditions[] = "record_date <= ?";
        $params[] = $date_to;
        $param_types .= "s";
    }
    
    $where_clause = implode(" AND ", $where_conditions);
    
    // ดึงข้อมูลอาการ
    $symptoms_sql = "
    SELECT 
        id,
        record_date,
        record_time,
        symptoms,
        severity,
        temperature,
        notes,
        image_path,
        recorded_by,
        recorded_by_name,
        recorded_datetime
    FROM symptoms_records 
    WHERE {$where_clause}
    ORDER BY record_date DESC, record_time DESC, recorded_datetime DESC
    LIMIT ?
    ";
    
    $symptoms_stmt = $conn->prepare($symptoms_sql);
    $params[] = $limit;
    $param_types .= "i";
    $symptoms_stmt->bind_param($param_types, ...$params);
    $symptoms_stmt->execute();
    $symptoms_result = $symptoms_stmt->get_result();
    
    $symptoms = [];
    while ($row = $symptoms_result->fetch_assoc()) {
        // แยกรูปภาพ
        $images = [];
        if (!empty($row['image_path'])) {
            $image_paths = explode(',', $row['image_path']);
            foreach ($image_paths as $path) {
                $images[] = trim($path);
            }
        }
        
        $symptoms[] = [
            'id' => $row['id'],
            'record_date' => $row['record_date'],
            'record_time' => $row['record_time'],
            'symptoms' => $row['symptoms'],
            'severity' => $row['severity'],
            'severity_thai' => getSeverityThai($row['severity']),
            'temperature' => $row['temperature'],
            'notes' => $row['notes'],
            'images' => $images,
            'recorded_by' => $row['recorded_by'],
            'recorded_by_name' => $row['recorded_by_name'],
            'recorded_datetime' => $row['recorded_datetime'],
            'date_thai' => formatDateThai($row['record_date']),
            'time_thai' => $row['record_time'] ? formatTimeThai($row['record_time']) : null
        ];
    }
    
    // ดึงสถิติ
    $summary_sql = "
    SELECT 
        COUNT(*) as total_records,
        SUM(CASE WHEN severity = 'mild' THEN 1 ELSE 0 END) as mild,
        SUM(CASE WHEN severity = 'moderate' THEN 1 ELSE 0 END) as moderate,
        SUM(CASE WHEN severity = 'severe' THEN 1 ELSE 0 END) as severe,
        SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical,
        MAX(record_date) as latest_date
    FROM symptoms_records 
    WHERE {$where_clause}
    ";
    
    // ลบ limit parameter สำหรับ summary
    $summary_params = array_slice($params, 0, -1);
    $summary_param_types = substr($param_types, 0, -1);
    
    $summary_stmt = $conn->prepare($summary_sql);
    if (!empty($summary_params)) {
        $summary_stmt->bind_param($summary_param_types, ...$summary_params);
    }
    $summary_stmt->execute();
    $summary_result = $summary_stmt->get_result();
    $summary = $summary_result->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'elderly_info' => $elderly_info,
            'symptoms' => $symptoms,
            'total_count' => count($symptoms),
            'summary' => [
                'total_records' => (int)$summary['total_records'],
                'mild' => (int)$summary['mild'],
                'moderate' => (int)$summary['moderate'],
                'severe' => (int)$summary['severe'],
                'critical' => (int)$summary['critical'],
                'latest_date' => $summary['latest_date'] ? formatDateThai($summary['latest_date']) : null
            ]
        ],
        'message' => $summary['total_records'] > 0 ? 
            "พบข้อมูลการบันทึกอาการ {$summary['total_records']} รายการ" : 
            'ยังไม่มีการบันทึกอาการ'
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_symptoms.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในการดึงข้อมูล'
    ]);
}

// Helper functions
function getSeverityThai($severity) {
    $severities = [
        'mild' => 'เบา',
        'moderate' => 'ปานกลาง',
        'severe' => 'รุนแรง',
        'critical' => 'วิกฤต'
    ];
    return $severities[$severity] ?? $severity;
}

function formatDateThai($date) {
    $months = [
        '01' => 'มกราคม', '02' => 'กุมภาพันธ์', '03' => 'มีนาคม',
        '04' => 'เมษายน', '05' => 'พฤษภาคม', '06' => 'มิถุนายน',
        '07' => 'กรกฎาคม', '08' => 'สิงหาคม', '09' => 'กันยายน',
        '10' => 'ตุลาคม', '11' => 'พฤศจิกายน', '12' => 'ธันวาคม'
    ];
    
    $parts = explode('-', $date);
    if (count($parts) === 3) {
        $day = (int)$parts[2];
        $month = $months[$parts[1]] ?? $parts[1];
        $year = (int)$parts[0] + 543; // Convert to Buddhist year
        return "$day $month $year";
    }
    
    return $date;
}

function formatTimeThai($time) {
    if (!$time) return null;
    
    $parts = explode(':', $time);
    if (count($parts) >= 2) {
        $hour = (int)$parts[0];
        $minute = (int)$parts[1];
        return sprintf('%02d:%02d น.', $hour, $minute);
    }
    
    return $time;
}

$conn->close();
?>