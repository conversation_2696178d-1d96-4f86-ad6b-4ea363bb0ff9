<?php
define('AIVORA_SECURITY', true);
session_start();

// Simulate a logged-in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Test user ID
    $_SESSION['user_role'] = 'facility_admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['username'] = 'test_user';
}

require_once __DIR__ . '/config/database.php';

echo "<h2>🧪 Vital Signs API Test</h2>";

// Test data
$test_data = [
    'care_type' => 'vital_signs',
    'elderly_id' => 7,
    'temperature' => 36.5,
    'heart_rate' => 72,
    'bp_systolic' => 120,
    'bp_diastolic' => 80,
    'respiratory_rate' => 16,
    'oxygen_saturation' => 98,
    'notes' => 'Test data from automated test'
];

echo "<h3>Test Data:</h3>";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>";

echo "<h3>Simulating API call...</h3>";

// Simulate POST request
$_POST = $test_data;
$_SERVER['REQUEST_METHOD'] = 'POST';

// Capture output
ob_start();

try {
    include __DIR__ . '/api/save_care_record.php';
    $api_output = ob_get_clean();
    
    echo "<h3>API Response:</h3>";
    echo "<pre>" . htmlspecialchars($api_output) . "</pre>";
    
    // Try to parse as JSON
    $json_response = json_decode($api_output, true);
    if ($json_response !== null) {
        echo "<h3>Parsed Response:</h3>";
        echo "<pre>" . json_encode($json_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        
        if ($json_response['success']) {
            echo "<div style='background: #d4edda; padding: 10px; color: #155724; border: 1px solid #c3e6cb;'>✅ Test passed! Record saved successfully.</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; color: #721c24; border: 1px solid #f5c6cb;'>❌ Test failed: " . $json_response['message'] . "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; color: #856404; border: 1px solid #ffeaa7;'>⚠️ Response is not valid JSON</div>";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<div style='background: #f8d7da; padding: 10px; color: #721c24; border: 1px solid #f5c6cb;'>";
    echo "❌ Exception occurred: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "</div>";
}

// Check if record was actually inserted
echo "<h3>Database Verification:</h3>";
try {
    $verify_sql = "SELECT * FROM care_vital_signs WHERE elderly_id = ? ORDER BY recorded_at DESC LIMIT 1";
    $stmt = $conn->prepare($verify_sql);
    $stmt->bind_param('i', $test_data['elderly_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $record = $result->fetch_assoc();
        echo "<div style='background: #d4edda; padding: 10px; color: #155724; border: 1px solid #c3e6cb;'>";
        echo "✅ Record found in database:<br>";
        echo "<pre>" . json_encode($record, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; color: #721c24; border: 1px solid #f5c6cb;'>❌ No record found in database</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; color: #721c24; border: 1px solid #f5c6cb;'>❌ Database verification failed: " . $e->getMessage() . "</div>";
}

// Test the modal JavaScript functionality
?>

<h3>🧪 Frontend Test</h3>
<div style="background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6;">
    <button onclick="testVitalSignsModal()" style="background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer;">
        Test Vital Signs Modal
    </button>
    <button onclick="testDirectAPI()" style="background: #28a745; color: white; padding: 10px 20px; border: none; cursor: pointer; margin-left: 10px;">
        Test Direct API Call
    </button>
    
    <div id="test-results" style="margin-top: 20px; padding: 15px; background: white; border: 1px solid #dee2e6; min-height: 100px;">
        <em>Test results will appear here...</em>
    </div>
</div>

<script>
function testVitalSignsModal() {
    const resultsDiv = document.getElementById('test-results');
    resultsDiv.innerHTML = '<strong>Testing Vital Signs Modal...</strong><br>';
    
    // Test if the modal functions exist
    if (typeof openVitalSignsModal === 'function') {
        resultsDiv.innerHTML += '✅ openVitalSignsModal function exists<br>';
    } else {
        resultsDiv.innerHTML += '❌ openVitalSignsModal function not found<br>';
    }
    
    if (typeof closeVitalSignsModal === 'function') {
        resultsDiv.innerHTML += '✅ closeVitalSignsModal function exists<br>';
    } else {
        resultsDiv.innerHTML += '❌ closeVitalSignsModal function not found<br>';
    }
    
    // Test if modal elements exist
    const modal = document.getElementById('vitalSignsModal');
    if (modal) {
        resultsDiv.innerHTML += '✅ Vital signs modal element found<br>';
    } else {
        resultsDiv.innerHTML += '❌ Vital signs modal element not found<br>';
    }
    
    const form = document.getElementById('vitalSignsForm');
    if (form) {
        resultsDiv.innerHTML += '✅ Vital signs form found<br>';
    } else {
        resultsDiv.innerHTML += '❌ Vital signs form not found<br>';
    }
}

function testDirectAPI() {
    const resultsDiv = document.getElementById('test-results');
    resultsDiv.innerHTML = '<strong>Testing Direct API Call...</strong><br>';
    
    const formData = new FormData();
    formData.append('care_type', 'vital_signs');
    formData.append('elderly_id', '7');
    formData.append('temperature', '36.8');
    formData.append('heart_rate', '75');
    formData.append('bp_systolic', '125');
    formData.append('bp_diastolic', '85');
    formData.append('notes', 'Test from frontend API call');
    
    fetch('api/save_care_record.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => {
        resultsDiv.innerHTML += `Status: ${response.status} ${response.statusText}<br>`;
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(data => {
        resultsDiv.innerHTML += '<strong>Raw Response:</strong><br>';
        resultsDiv.innerHTML += `<pre style="background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;">${data}</pre>`;
        
        try {
            const jsonData = JSON.parse(data);
            resultsDiv.innerHTML += '<strong>Parsed JSON:</strong><br>';
            resultsDiv.innerHTML += `<pre style="background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;">${JSON.stringify(jsonData, null, 2)}</pre>`;
            
            if (jsonData.success) {
                resultsDiv.innerHTML += '<div style="background: #d4edda; padding: 10px; color: #155724; border: 1px solid #c3e6cb;">✅ API call successful!</div>';
            } else {
                resultsDiv.innerHTML += '<div style="background: #f8d7da; padding: 10px; color: #721c24; border: 1px solid #f5c6cb;">❌ API call failed: ' + jsonData.message + '</div>';
            }
        } catch (e) {
            resultsDiv.innerHTML += '<div style="background: #fff3cd; padding: 10px; color: #856404; border: 1px solid #ffeaa7;">⚠️ JSON Parse Error: ' + e.message + '</div>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML += '<div style="background: #f8d7da; padding: 10px; color: #721c24; border: 1px solid #f5c6cb;">❌ Fetch Error: ' + error.message + '</div>';
    });
}
</script>

<style>
button:hover {
    opacity: 0.8;
}
</style>