<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Pressure Sore Modal -->
<div id="pressureSoreModal" class="modal" style="display: none;">
    <div class="modal-content pressure-sore-modal">
        <div class="modal-header">
            <h3 id="pressure-sore-modal-title">แผลกดทับ</h3>
            <span class="close" onclick="closePressureSoreModal()">&times;</span>
        </div>
        
        <form id="pressureSoreForm" class="pressure-sore-form">
            <input type="hidden" name="elderly_id" value="<?php echo isset($elderly['id']) ? $elderly['id'] : ''; ?>">
            <input type="hidden" id="pressure_sore_record_id" name="record_id" value="">
            <input type="hidden" id="pressure_sore_form_mode" name="form_mode" value="create">
            
            <div class="form-row">
                <div class="form-group date-group">
                    <label for="pressure_sore_recorded_date">
                        <i class="calendar-icon">📅</i>
                        วันที่บันทึก <span class="required">*</span>
                    </label>
                    <input type="date" id="pressure_sore_recorded_date" name="recorded_date" 
                           value="<?php echo date('Y-m-d'); ?>" required>
                    <input type="time" id="pressure_sore_recorded_time" name="recorded_time" 
                           value="<?php echo date('H:i'); ?>" required>
                </div>
            </div>

            <div class="form-notice">
                <p><span class="required">*</span> วันที่ เวลา ตำแหน่งแผล และระยะแผลจำเป็นต้องกรอก</p>
                <p>🔴 การติดตามแผลกดทับเพื่อการรักษาที่เหมาะสม</p>
            </div>

            <!-- แสดงข้อมูลการบันทึกเมื่อเป็นโหมดแก้ไข -->
            <div id="pressure-sore-edit-info" class="edit-info" style="display: none;">
                <div class="info-card">
                    <h4>📝 ข้อมูลการบันทึก</h4>
                    <p><strong>ผู้บันทึก:</strong> <span id="pressure_sore_recorded_by_display"></span></p>
                    <p><strong>เวลาที่บันทึก:</strong> <span id="pressure_sore_created_at_display"></span></p>
                </div>
            </div>

            <div class="pressure-sore-grid">
                <div class="pressure-sore-item">
                    <label for="sore_location">
                        <i class="pressure-sore-icon">📍</i>
                        <span class="pressure-sore-label">ตำแหน่งแผล <span class="required">*</span></span>
                    </label>
                    <select id="sore_location" name="sore_location" required>
                        <option value="">เลือกตำแหน่งแผล</option>
                        <option value="ก้นกบ">ก้นกบ (Sacrum)</option>
                        <option value="ข้อเท้า">ข้อเท้า (Heel)</option>
                        <option value="กระดูกสะโพก">กระดูกสะโพก (Hip)</option>
                        <option value="หัวไหล่">หัวไหล่ (Shoulder)</option>
                        <option value="ข้อศอก">ข้อศอก (Elbow)</option>
                        <option value="ใบหู">ใบหู (Ear)</option>
                        <option value="ท้ายทอย">ท้ายทอย (Coccyx)</option>
                        <option value="เข่า">เข่า (Knee)</option>
                        <option value="อื่นๆ">อื่นๆ</option>
                    </select>
                </div>

                <div class="pressure-sore-item">
                    <label for="sore_stage">
                        <i class="pressure-sore-icon">🎯</i>
                        <span class="pressure-sore-label">ระยะของแผล <span class="required">*</span></span>
                    </label>
                    <select id="sore_stage" name="sore_stage" required>
                        <option value="">เลือกระยะแผล</option>
                        <option value="Stage 1">Stage 1 - ผิวหนังแดง ไม่หลุด</option>
                        <option value="Stage 2">Stage 2 - ผิวหนังหลุด บาดแผลตื้น</option>
                        <option value="Stage 3">Stage 3 - แผลลึกถึงชั้นไขมัน</option>
                        <option value="Stage 4">Stage 4 - แผลลึกถึงกระดูก เอ็น กล้ามเนื้อ</option>
                        <option value="Unstageable">Unstageable - ไม่สามารถระบุระยะได้</option>
                        <option value="Deep Tissue Injury">Deep Tissue Injury - เนื้อเยื่อลึกเสียหาย</option>
                    </select>
                </div>

                <div class="pressure-sore-item size-item">
                    <label>
                        <i class="pressure-sore-icon">📏</i>
                        <span class="pressure-sore-label">ขนาดแผล (ซม.)</span>
                    </label>
                    <div class="size-inputs">
                        <div class="size-input-group">
                            <input type="number" id="sore_size_length" name="sore_size_length" 
                                   step="0.1" min="0" max="50" placeholder="ยาว">
                            <small>ยาว</small>
                        </div>
                        <span class="size-separator">×</span>
                        <div class="size-input-group">
                            <input type="number" id="sore_size_width" name="sore_size_width" 
                                   step="0.1" min="0" max="50" placeholder="กว้าง">
                            <small>กว้าง</small>
                        </div>
                        <span class="size-separator">×</span>
                        <div class="size-input-group">
                            <input type="number" id="sore_size_depth" name="sore_size_depth" 
                                   step="0.1" min="0" max="10" placeholder="ลึก">
                            <small>ลึก</small>
                        </div>
                    </div>
                </div>

                <div class="pressure-sore-item">
                    <label for="wound_condition">
                        <i class="pressure-sore-icon">🩹</i>
                        <span class="pressure-sore-label">สภาพแผล</span>
                    </label>
                    <select id="wound_condition" name="wound_condition">
                        <option value="">เลือกสภาพแผล</option>
                        <option value="สะอาด">สะอาด</option>
                        <option value="มีสิ่งปลอมปน">มีสิ่งปลอมปน</option>
                        <option value="ติดเชื้อ">ติดเชื้อ</option>
                        <option value="มีเนื้อตาย">มีเนื้อตาย</option>
                        <option value="กำลังหาย">กำลังหาย</option>
                        <option value="แย่ลง">แย่ลง</option>
                    </select>
                </div>

                <div class="pressure-sore-item">
                    <label for="surrounding_skin">
                        <i class="pressure-sore-icon">🎨</i>
                        <span class="pressure-sore-label">ผิวหนังรอบแผล</span>
                    </label>
                    <select id="surrounding_skin" name="surrounding_skin">
                        <option value="">เลือกสภาพผิวหนัง</option>
                        <option value="ปกติ">ปกติ</option>
                        <option value="แดง">แดง</option>
                        <option value="บวม">บวม</option>
                        <option value="ร้อน">ร้อน</option>
                        <option value="แข็ง">แข็ง</option>
                        <option value="เปลี่ยนสี">เปลี่ยนสี</option>
                    </select>
                </div>

                <div class="pressure-sore-item">
                    <label for="drainage_type">
                        <i class="pressure-sore-icon">💧</i>
                        <span class="pressure-sore-label">ประเภทสารคัดหลั่ง</span>
                    </label>
                    <select id="drainage_type" name="drainage_type">
                        <option value="">เลือกประเภท</option>
                        <option value="ไม่มี">ไม่มี</option>
                        <option value="น้ำใส">น้ำใส</option>
                        <option value="เลือด">เลือด</option>
                        <option value="หนอง">หนอง</option>
                        <option value="น้ำเหลือง">น้ำเหลือง</option>
                        <option value="เขียว">เขียว</option>
                    </select>
                </div>

                <div class="pressure-sore-item">
                    <label for="drainage_amount">
                        <i class="pressure-sore-icon">🥤</i>
                        <span class="pressure-sore-label">ปริมาณสารคัดหลั่ง</span>
                    </label>
                    <select id="drainage_amount" name="drainage_amount">
                        <option value="">เลือกปริมาณ</option>
                        <option value="ไม่มี">ไม่มี</option>
                        <option value="น้อย">น้อย</option>
                        <option value="ปานกลาง">ปานกลาง</option>
                        <option value="มาก">มาก</option>
                        <option value="มากมาย">มากมาย</option>
                    </select>
                </div>

                <div class="pressure-sore-item">
                    <label for="pain_level">
                        <i class="pressure-sore-icon">😖</i>
                        <span class="pressure-sore-label">ระดับความเจ็บปวด (0-10)</span>
                    </label>
                    <select id="pain_level" name="pain_level">
                        <option value="">เลือกระดับ</option>
                        <option value="0">0 - ไม่เจ็บ</option>
                        <option value="1">1 - เจ็บน้อยมาก</option>
                        <option value="2">2 - เจ็บน้อย</option>
                        <option value="3">3 - เจ็บน้อย</option>
                        <option value="4">4 - เจ็บปานกลาง</option>
                        <option value="5">5 - เจ็บปานกลาง</option>
                        <option value="6">6 - เจ็บปานกลาง</option>
                        <option value="7">7 - เจ็บมาก</option>
                        <option value="8">8 - เจ็บมาก</option>
                        <option value="9">9 - เจ็บมากมาย</option>
                        <option value="10">10 - เจ็บสุดขีด</option>
                    </select>
                </div>

                <div class="pressure-sore-item">
                    <label for="treatment_applied">
                        <i class="pressure-sore-icon">🏥</i>
                        <span class="pressure-sore-label">การรักษาที่ให้</span>
                    </label>
                    <select id="treatment_applied" name="treatment_applied">
                        <option value="">เลือกการรักษา</option>
                        <option value="ทำความสะอาด">ทำความสะอาด</option>
                        <option value="ใส่ยาปฏิชีวนะ">ใส่ยาปฏิชีวนะ</option>
                        <option value="ใส่ยาทาแผล">ใส่ยาทาแผล</option>
                        <option value="เปลี่ยนผ้าพันแผล">เปลี่ยนผ้าพันแผล</option>
                        <option value="ล้างแผล">ล้างแผล</option>
                        <option value="ให้ยาแก้ปวด">ให้ยาแก้ปวด</option>
                    </select>
                </div>

                <div class="pressure-sore-item">
                    <label for="dressing_type">
                        <i class="pressure-sore-icon">🩹</i>
                        <span class="pressure-sore-label">ประเภทผ้าพันแผล</span>
                    </label>
                    <select id="dressing_type" name="dressing_type">
                        <option value="">เลือกผ้าพันแผล</option>
                        <option value="ผ้าธรรมดา">ผ้าธรรมดา</option>
                        <option value="ผ้าก๊อซ">ผ้าก๊อซ</option>
                        <option value="ฟิล์มใส">ฟิล์มใส</option>
                        <option value="โฟมแผ่น">โฟมแผ่น</option>
                        <option value="ไฮโดรคอลลอยด์">ไฮโดรคอลลอยด์</option>
                        <option value="อัลจิเนต">อัลจิเนต</option>
                        <option value="ไฮโดรเจล">ไฮโดรเจล</option>
                    </select>
                </div>
            </div>

            <div class="form-group notes-group">
                <label for="pressure_sore_notes">
                    <i class="notes-icon">📝</i>
                    หมายเหตุเพิ่มเติม
                </label>
                <textarea id="pressure_sore_notes" name="notes" 
                         rows="4" placeholder="บันทึกรายละเอียดเพิ่มเติมเกี่ยวกับแผลกดทับ..."></textarea>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closePressureSoreModal()">
                    ยกเลิก
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="save-icon">💾</i>
                    บันทึก
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Pressure Sore Modal Styles */
.pressure-sore-modal {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.pressure-sore-form {
    padding: 25px;
}

.pressure-sore-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.pressure-sore-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.pressure-sore-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-green);
}

.pressure-sore-item.has-data {
    border-color: var(--primary-green);
    background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
}

.pressure-sore-item.has-data .pressure-sore-icon {
    color: var(--primary-green);
}

.pressure-sore-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.pressure-sore-icon {
    font-size: 1.3rem;
    width: 30px;
    text-align: center;
}

.pressure-sore-label {
    font-size: 0.95rem;
}

.pressure-sore-item select,
.pressure-sore-item input[type="number"] {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    transition: border-color 0.3s ease;
    background: white;
}

.pressure-sore-item select:focus,
.pressure-sore-item input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(41, 163, 126, 0.1);
}

/* Size inputs styling */
.size-item {
    grid-column: span 2;
}

.size-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.size-input-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 80px;
}

.size-input-group input {
    width: 100%;
    margin-bottom: 4px;
}

.size-input-group small {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.size-separator {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--text-secondary);
    margin: 0 4px;
}

/* Responsive */
@media (max-width: 768px) {
    .pressure-sore-modal {
        width: 95%;
        margin: 10px;
    }
    
    .pressure-sore-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .size-item {
        grid-column: span 1;
    }
    
    .size-inputs {
        flex-direction: column;
        gap: 12px;
    }
    
    .size-input-group {
        min-width: 100%;
    }
    
    .size-separator {
        display: none;
    }
}
</style>

<script>
function openPressureSoreModal() {
    // เปิด modal แผลกดทับ
    const elderlyId = document.querySelector('input[name="elderly_id"]').value;
    
    if (!elderlyId) {
        alert('ไม่พบรหัสผู้สูงอายุ');
        return;
    }

    // แสดง loading
    document.getElementById('pressure-sore-modal-title').textContent = 'กำลังตรวจสอบข้อมูล...';
    document.getElementById('pressureSoreModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // ตรวจสอบว่าวันนี้มีข้อมูลแล้วหรือไม่
    checkTodayPressureSoreRecord(elderlyId);
}

function checkTodayPressureSoreRecord(elderlyId) {
    // ใช้ API ง่ายๆ ที่ไม่ต้องเช็ค session ซับซ้อน
    fetch(`api/check_today_pressure_sore_simple.php?elderly_id=${elderlyId}`, {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            console.log(`HTTP error! status: ${response.status}, using create mode`);
            setupPressureSoreCreateMode();
            return null;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return; // ถ้า response error แล้ว skip
        
        console.log('Check today pressure sore response:', data);
        if (data.success) {
            if (data.has_today_record) {
                // โหมดแก้ไข - มีข้อมูลวันนี้แล้ว
                setupPressureSoreEditMode(data.data);
            } else {
                // โหมดสร้างใหม่ - ยังไม่มีข้อมูลวันนี้
                setupPressureSoreCreateMode();
            }
        } else {
            console.log('API returned error, using create mode:', data.message);
            // ถ้า API error ให้ใช้โหมดสร้างใหม่
            setupPressureSoreCreateMode();
        }
    })
    .catch(error => {
        console.log('Error checking today pressure sore data, using create mode:', error.message);
        // ถ้าเกิด error ให้ใช้โหมดสร้างใหม่
        setupPressureSoreCreateMode();
    });
}

function setupPressureSoreCreateMode() {
    document.getElementById('pressure-sore-modal-title').textContent = '🔴 บันทึกแผลกดทับใหม่';
    document.getElementById('pressure_sore_form_mode').value = 'create';
    document.getElementById('pressure_sore_record_id').value = '';
    document.getElementById('pressure-sore-edit-info').style.display = 'none';
    
    // รีเซ็ตฟอร์มเป็นค่าเริ่มต้น
    document.getElementById('pressureSoreForm').reset();
    document.getElementById('pressure_sore_recorded_date').value = new Date().toISOString().split('T')[0];
    document.getElementById('pressure_sore_recorded_time').value = new Date().toTimeString().substr(0,5);
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#pressureSoreForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">💾</i> บันทึกใหม่';
    
    // แสดงข้อความบอกว่ายังไม่มีข้อมูลวันนี้
    showPressureSoreCreateModeMessage();
    
    console.log('Setup pressure sore create mode completed');
}

function setupPressureSoreEditMode(data) {
    document.getElementById('pressure-sore-modal-title').textContent = '✏️ แก้ไขแผลกดทับวันนี้';
    document.getElementById('pressure_sore_form_mode').value = 'edit';
    document.getElementById('pressure_sore_record_id').value = data.id;
    
    // แสดงข้อมูลผู้บันทึก
    document.getElementById('pressure_sore_recorded_by_display').textContent = data.recorded_by_name;
    document.getElementById('pressure_sore_created_at_display').textContent = 
        new Date(data.created_at).toLocaleString('th-TH');
    document.getElementById('pressure-sore-edit-info').style.display = 'block';
    
    // เติมข้อมูลในฟอร์ม
    document.getElementById('pressure_sore_recorded_date').value = data.recorded_date;
    document.getElementById('pressure_sore_recorded_time').value = data.recorded_time;
    document.getElementById('sore_location').value = data.sore_location || '';
    document.getElementById('sore_stage').value = data.sore_stage || '';
    document.getElementById('sore_size_length').value = data.sore_size_length || '';
    document.getElementById('sore_size_width').value = data.sore_size_width || '';
    document.getElementById('sore_size_depth').value = data.sore_size_depth || '';
    document.getElementById('wound_condition').value = data.wound_condition || '';
    document.getElementById('surrounding_skin').value = data.surrounding_skin || '';
    document.getElementById('drainage_type').value = data.drainage_type || '';
    document.getElementById('drainage_amount').value = data.drainage_amount || '';
    document.getElementById('pain_level').value = data.pain_level || '';
    document.getElementById('treatment_applied').value = data.treatment_applied || '';
    document.getElementById('dressing_type').value = data.dressing_type || '';
    document.getElementById('pressure_sore_notes').value = data.notes || '';
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#pressureSoreForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">✏️</i> อัปเดต';
    
    // แสดงข้อความแจ้งว่ามีข้อมูลอยู่แล้วและสามารถแก้ไขได้
    showPressureSoreEditModeMessage(data);
    
    // อัปเดต visual feedback
    setTimeout(updatePressureSoreItemVisual, 100);
    
    console.log('Setup pressure sore edit mode completed for record ID:', data.id);
}

function showPressureSoreCreateModeMessage() {
    const formNotice = document.querySelector('#pressureSoreModal .form-notice');
    if (formNotice) {
        const today = new Date().toLocaleDateString('th-TH');
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #fff3cd 0%, #fdeaa7 100%); border: 1px solid #f0c674; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #856404; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #856404;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #856404;"><strong>สถานะ:</strong> ❌ ยังไม่มีการบันทึกแผลกดทับ</p>
                <p style="margin: 5px 0; color: #856404;"><strong>การดำเนินการ:</strong> 📝 ต้องการบันทึกข้อมูลใหม่หรือไม่?</p>
            </div>
            <p><span class="required">*</span> วันที่ เวลา ตำแหน่งแผล และระยะแผลจำเป็นต้องกรอก</p>
            <p>🔴 การติดตามแผลกดทับเพื่อการรักษาที่เหมาะสม</p>
        `;
    }
}

function showPressureSoreEditModeMessage(data) {
    const formNotice = document.querySelector('#pressureSoreModal .form-notice');
    if (formNotice) {
        const recordTime = new Date(data.recorded_date + 'T' + data.recorded_time).toLocaleString('th-TH');
        const today = new Date().toLocaleDateString('th-TH');
        const soreSize = data.sore_size_length && data.sore_size_width ? 
            data.sore_size_length + '×' + data.sore_size_width + (data.sore_size_depth ? '×' + data.sore_size_depth : '') + ' ซม.' : 'ไม่ระบุ';
        
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #155724; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #155724;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>สถานะ:</strong> ✅ มีการบันทึกแผลกดทับแล้ว</p>
                <p style="margin: 5px 0; color: #155724;"><strong>เวลาที่บันทึก:</strong> ${recordTime}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>การดำเนินการ:</strong> ✏️ ต้องการแก้ไขข้อมูลหรือไม่?</p>
            </div>
            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #495057;">📊 รายงานแผลกดทับปัจจุบัน</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                    ${data.sore_location ? `<p style="margin: 2px 0; color: #495057;"><strong>📍 ตำแหน่ง:</strong> ${data.sore_location}</p>` : ''}
                    ${data.sore_stage ? `<p style="margin: 2px 0; color: #495057;"><strong>🎯 ระยะแผล:</strong> ${data.sore_stage}</p>` : ''}
                    <p style="margin: 2px 0; color: #495057;"><strong>📏 ขนาดแผล:</strong> ${soreSize}</p>
                    ${data.wound_condition ? `<p style="margin: 2px 0; color: #495057;"><strong>🩹 สภาพแผล:</strong> ${data.wound_condition}</p>` : ''}
                    ${data.drainage_type ? `<p style="margin: 2px 0; color: #495057;"><strong>💧 สารคัดหลั่ง:</strong> ${data.drainage_type}</p>` : ''}
                    ${data.pain_level ? `<p style="margin: 2px 0; color: #495057;"><strong>😖 ความเจ็บปวด:</strong> ${data.pain_level}/10</p>` : ''}
                    ${data.treatment_applied ? `<p style="margin: 2px 0; color: #495057;"><strong>🏥 การรักษา:</strong> ${data.treatment_applied}</p>` : ''}
                    ${data.dressing_type ? `<p style="margin: 2px 0; color: #495057;"><strong>🩹 ผ้าพันแผล:</strong> ${data.dressing_type}</p>` : ''}
                </div>
                ${data.notes ? `<p style="margin: 10px 0 5px 0; color: #495057;"><strong>📝 หมายเหตุ:</strong> ${data.notes}</p>` : ''}
            </div>
            <p><span class="required">*</span> วันที่ เวลา ตำแหน่งแผล และระยะแผลจำเป็นต้องกรอก</p>
            <p>🔴 การติดตามแผลกดทับเพื่อการรักษาที่เหมาะสม</p>
        `;
    }
}

function closePressureSoreModal() {
    document.getElementById('pressureSoreModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('pressureSoreForm').reset();
}

// Close modal when clicking outside
document.getElementById('pressureSoreModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePressureSoreModal();
    }
});

// Form validation
function validatePressureSoreForm() {
    const requiredFields = ['recorded_date', 'recorded_time', 'sore_location', 'sore_stage'];
    
    // Check required fields
    for (const fieldName of requiredFields) {
        const field = document.getElementById('pressure_sore_' + fieldName) || document.getElementById(fieldName);
        if (!field || !field.value.trim()) {
            let fieldLabel = 'ฟิลด์ที่จำเป็น';
            if (fieldName === 'recorded_date') fieldLabel = 'วันที่บันทึก';
            else if (fieldName === 'recorded_time') fieldLabel = 'เวลาบันทึก';
            else if (fieldName === 'sore_location') fieldLabel = 'ตำแหน่งแผล';
            else if (fieldName === 'sore_stage') fieldLabel = 'ระยะของแผล';
            
            alert(`กรุณากรอก${fieldLabel}`);
            if (field) field.focus();
            return false;
        }
    }
    
    return true;
}

// Form submission
document.getElementById('pressureSoreForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate form first
    if (!validatePressureSoreForm()) {
        return;
    }
    
    const formData = new FormData(this);
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="save-icon">⏳</i> กำลังบันทึก...';
    submitBtn.disabled = true;
    
    // เรียก API สำหรับบันทึกแผลกดทับ
    fetch('api/save_pressure_sore.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
            'Cache-Control': 'no-cache'
        }
    })
    .then(response => {
        console.log('Save pressure sore response status:', response.status);
        
        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Session expired. Please refresh the page and login again.');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Save pressure sore response data:', data);
        
        if (data.success) {
            alert('บันทึกข้อมูลแผลกดทับเรียบร้อยแล้ว');
            closePressureSoreModal();
            // Update display with new data
            loadLatestPressureSoreRecord();
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
            console.error('API Error:', data);
        }
    })
    .catch(error => {
        console.error('Fetch Error:', error);
        if (error.message.includes('Session expired') || error.message.includes('status: 401')) {
            alert('Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่');
            window.location.href = 'index.php';
        } else if (error.message.includes('status: 500')) {
            alert('เกิดข้อผิดพลาดภายในระบบ กรุณาตรวจสอบ:\n1. ว่าได้เข้าสู่ระบบแล้ว\n2. มีสิทธิ์ในการบันทึกข้อมูล\n3. ข้อมูลที่กรอกถูกต้อง');
        } else {
            alert('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' + error.message);
        }
    })
    .finally(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Real-time visual feedback for form inputs
function updatePressureSoreItemVisual() {
    const pressureSoreFields = ['sore_location', 'sore_stage', 'wound_condition', 'surrounding_skin', 
                               'drainage_type', 'drainage_amount', 'pain_level', 'treatment_applied', 'dressing_type'];
    
    pressureSoreFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            const pressureSoreItem = field.closest('.pressure-sore-item');
            if (field.value.trim()) {
                pressureSoreItem.classList.add('has-data');
            } else {
                pressureSoreItem.classList.remove('has-data');
            }
        }
    });
    
    // Check size inputs
    const sizeInputs = ['sore_size_length', 'sore_size_width', 'sore_size_depth'];
    const sizeItem = document.querySelector('.size-item');
    let hasSizeData = false;
    
    sizeInputs.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field && field.value.trim()) {
            hasSizeData = true;
        }
    });
    
    if (sizeItem) {
        if (hasSizeData) {
            sizeItem.classList.add('has-data');
        } else {
            sizeItem.classList.remove('has-data');
        }
    }
}

// Add input event listeners for real-time feedback
function addPressureSoreInputListeners() {
    const pressureSoreFields = ['sore_location', 'sore_stage', 'sore_size_length', 'sore_size_width', 
                               'sore_size_depth', 'wound_condition', 'surrounding_skin', 'drainage_type', 
                               'drainage_amount', 'pain_level', 'treatment_applied', 'dressing_type'];
    
    pressureSoreFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('change', updatePressureSoreItemVisual);
            field.addEventListener('input', updatePressureSoreItemVisual);
        }
    });
}

// Function to load latest pressure sore record (placeholder)
function loadLatestPressureSoreRecord() {
    console.log('Loading latest pressure sore record...');
    // TODO: Implement loading latest pressure sore record display
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    addPressureSoreInputListeners();
});
</script>