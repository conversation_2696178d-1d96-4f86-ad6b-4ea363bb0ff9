<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Sputum Recording Modal -->
<div id="sputumModal" class="modal" style="display: none;">
    <div class="modal-content sputum-modal-content">
        <div class="modal-header">
            <div>
                <h2>🫁 บันทึกเสมหะ</h2>
                <div id="sputumSummary" class="sputum-summary">
                    <span id="sputumSummaryText">กำลังโหลด...</span>
                </div>
            </div>
            <span class="close" onclick="closeSputumModal()">&times;</span>
        </div>
        
        <!-- Previous Sputum Records Section -->
        <div id="previousSputumSection" class="previous-sputum-section" style="display: none;">
            <h3>📝 การบันทึกเสมหะก่อนหน้า</h3>
            <div id="previousSputumList" class="previous-sputum-list">
                <!-- จะถูกโหลดด้วย JavaScript -->
            </div>
        </div>
        
        <form id="sputumForm" method="POST" enctype="multipart/form-data" onsubmit="return false;">
            <input type="hidden" id="sputum_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="sputum_date">วันที่บันทึก:</label>
                <input type="date" id="sputum_date" name="record_date" required>
            </div>

            <div class="form-group">
                <label for="sputum_time">เวลา (ไม่บังคับ):</label>
                <input type="time" id="sputum_time" name="record_time">
            </div>

            <!-- สถานะเสมหะ -->
            <div class="form-section">
                <h4>🔍 สถานะเสมหะ</h4>
                
                <div class="form-group">
                    <label for="has_sputum">มีเสมหะหรือไม่:</label>
                    <div class="sputum-presence-grid">
                        <div class="presence-card">
                            <input type="radio" id="has_sputum_yes" name="has_sputum" value="มีเสมหะ" onchange="toggleSputumDetails(true)">
                            <label for="has_sputum_yes" class="presence-label">
                                <div class="presence-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">💧</span>
                                </div>
                                <div class="presence-text">มีเสมหะ</div>
                            </label>
                        </div>
                        
                        <div class="presence-card">
                            <input type="radio" id="has_sputum_no" name="has_sputum" value="ไม่มีเสมหะ" checked onchange="toggleSputumDetails(false)">
                            <label for="has_sputum_no" class="presence-label">
                                <div class="presence-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">✅</span>
                                </div>
                                <div class="presence-text">ไม่มีเสมหะ</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- รายละเอียดเสมหะ (แสดงเมื่อมีเสมหะ) -->
            <div id="sputumDetails" class="form-section" style="display: none;">
                <h4>🩺 รายละเอียดเสมหะ</h4>
                
                <!-- วิธีการขับเสมหะ -->
                <div class="form-group">
                    <label for="expulsion_method">วิธีการขับเสมหะ:</label>
                    <div class="expulsion-grid">
                        <div class="method-card">
                            <input type="radio" id="method_self" name="expulsion_method" value="ขับเสมหะได้ด้วยตัวเอง">
                            <label for="method_self" class="method-label">
                                <div class="method-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">👤</span>
                                </div>
                                <div class="method-text">ขับเสมหะได้ด้วยตัวเอง</div>
                            </label>
                        </div>
                        
                        <div class="method-card">
                            <input type="radio" id="method_sputum_suction" name="expulsion_method" value="ดูดเสมหะ">
                            <label for="method_sputum_suction" class="method-label">
                                <div class="method-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">🧪</span>
                                </div>
                                <div class="method-text">ดูดเสมหะ</div>
                            </label>
                        </div>
                        
                        <div class="method-card">
                            <input type="radio" id="method_saliva_suction" name="expulsion_method" value="ดูดน้ำลาย">
                            <label for="method_saliva_suction" class="method-label">
                                <div class="method-icon" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);">
                                    <span class="icon">💊</span>
                                </div>
                                <div class="method-text">ดูดน้ำลาย</div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- กลิ่น -->
                <div class="form-group">
                    <label for="odor">กลิ่น:</label>
                    <div class="odor-grid">
                        <div class="odor-card">
                            <input type="radio" id="odor_normal" name="odor" value="ปกติ" checked>
                            <label for="odor_normal" class="odor-label">
                                <div class="odor-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">👃</span>
                                </div>
                                <div class="odor-text">ปกติ</div>
                            </label>
                        </div>
                        
                        <div class="odor-card">
                            <input type="radio" id="odor_bad" name="odor" value="เหม็น">
                            <label for="odor_bad" class="odor-label">
                                <div class="odor-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">🤢</span>
                                </div>
                                <div class="odor-text">เหม็น</div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- สี -->
                <div class="form-group">
                    <label for="color">สี:</label>
                    <div class="color-grid">
                        <div class="color-card">
                            <input type="radio" id="color_clear" name="color" value="สีใส" checked>
                            <label for="color_clear" class="color-label">
                                <div class="color-icon" style="background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);">
                                    <span class="icon">💧</span>
                                </div>
                                <div class="color-text">สีใส</div>
                            </label>
                        </div>
                        
                        <div class="color-card">
                            <input type="radio" id="color_white_gray" name="color" value="สีขาวออกเทา">
                            <label for="color_white_gray" class="color-label">
                                <div class="color-icon" style="background: linear-gradient(135deg, #F5F5F5 0%, #EEEEEE 100%);">
                                    <span class="icon">⚪</span>
                                </div>
                                <div class="color-text">สีขาวออกเทา</div>
                            </label>
                        </div>
                        
                        <div class="color-card">
                            <input type="radio" id="color_yellow" name="color" value="สีเหลือง">
                            <label for="color_yellow" class="color-label">
                                <div class="color-icon" style="background: linear-gradient(135deg, #FFF9C4 0%, #FFF176 100%);">
                                    <span class="icon">🟡</span>
                                </div>
                                <div class="color-text">สีเหลือง</div>
                            </label>
                        </div>
                        
                        <div class="color-card">
                            <input type="radio" id="color_green" name="color" value="สีเขียว">
                            <label for="color_green" class="color-label">
                                <div class="color-icon" style="background: linear-gradient(135deg, #C8E6C9 0%, #A5D6A7 100%);">
                                    <span class="icon">🟢</span>
                                </div>
                                <div class="color-text">สีเขียว</div>
                            </label>
                        </div>
                        
                        <div class="color-card">
                            <input type="radio" id="color_red" name="color" value="สีแดง">
                            <label for="color_red" class="color-label">
                                <div class="color-icon" style="background: linear-gradient(135deg, #FFCDD2 0%, #EF9A9A 100%);">
                                    <span class="icon">🔴</span>
                                </div>
                                <div class="color-text">สีแดง</div>
                            </label>
                        </div>
                        
                        <div class="color-card">
                            <input type="radio" id="color_brown" name="color" value="สีน้ำตาล">
                            <label for="color_brown" class="color-label">
                                <div class="color-icon" style="background: linear-gradient(135deg, #EFEBE9 0%, #D7CCC8 100%);">
                                    <span class="icon">🟤</span>
                                </div>
                                <div class="color-text">สีน้ำตาล</div>
                            </label>
                        </div>
                        
                        <div class="color-card">
                            <input type="radio" id="color_black" name="color" value="สีดำ">
                            <label for="color_black" class="color-label">
                                <div class="color-icon" style="background: linear-gradient(135deg, #616161 0%, #424242 100%);">
                                    <span class="icon">⚫</span>
                                </div>
                                <div class="color-text">สีดำ</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- สถานะการสำลัก -->
            <div class="form-section">
                <h4>⚠️ สถานะการสำลัก</h4>
                
                <div class="form-group">
                    <label for="choking_status">การสำลัก:</label>
                    <div class="choking-grid">
                        <div class="choking-card">
                            <input type="radio" id="choking_no" name="choking_status" value="ไม่สำลัก" checked>
                            <label for="choking_no" class="choking-label">
                                <div class="choking-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">😌</span>
                                </div>
                                <div class="choking-text">ไม่สำลัก</div>
                            </label>
                        </div>
                        
                        <div class="choking-card">
                            <input type="radio" id="choking_yes" name="choking_status" value="สำลัก">
                            <label for="choking_yes" class="choking-label">
                                <div class="choking-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">😰</span>
                                </div>
                                <div class="choking-text">สำลัก</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="sputum_notes">หมายเหตุเพิ่มเติม:</label>
                <textarea id="sputum_notes" name="notes" rows="3" placeholder="รายละเอียดเพิ่มเติม หรือหมายเหตุอื่นๆ"></textarea>
            </div>

            <div class="form-group">
                <label for="sputum_images">รูปภาพประกอบ (ไม่บังคับ):</label>
                <input type="file" id="sputum_images" name="sputum_images[]" multiple accept="image/*" class="file-input">
                <div class="file-upload-area" onclick="document.getElementById('sputum_images').click()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">
                        <strong>คลิกเพื่อเลือกรูปภาพ</strong><br>
                        <small>รองรับ JPG, PNG, GIF (สูงสุด 5 รูป)</small>
                    </div>
                </div>
                <div id="sputumImagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label for="sputum_recorded_by">ผู้บันทึก:</label>
                <input type="text" id="sputum_recorded_by" name="recorded_by_name" value="<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>" readonly>
            </div>

            <div class="modal-actions">
                <button type="button" onclick="closeSputumModal()" class="btn-cancel">ยกเลิก</button>
                <button type="submit" class="btn-save">บันทึกเสมหะ</button>
            </div>
        </form>
    </div>
</div>

<style>
/* Compact modal for sputum */
.sputum-modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 85vh;
    overflow-y: auto;
}

.sputum-modal-content .modal-header h2 {
    font-size: 1.3rem;
    margin: 0;
}

.sputum-summary {
    font-size: 0.85rem;
    margin-top: 5px;
    opacity: 0.9;
}

.previous-sputum-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
}

.previous-sputum-section h3 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

.previous-sputum-list {
    display: grid;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.sputum-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.sputum-item:hover {
    border-color: #FF9800;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
}

.sputum-presence-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 8px;
}

.expulsion-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.odor-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
    margin-top: 8px;
}

.choking-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 8px;
}

.presence-card, .method-card, .odor-card, .color-card, .choking-card {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.presence-card:hover, .method-card:hover, .odor-card:hover, .color-card:hover, .choking-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.presence-card input[type="radio"], 
.method-card input[type="radio"],
.odor-card input[type="radio"],
.color-card input[type="radio"],
.choking-card input[type="radio"] {
    display: none;
}

.presence-card input[type="radio"]:checked + .presence-label,
.method-card input[type="radio"]:checked + .method-label,
.odor-card input[type="radio"]:checked + .odor-label,
.color-card input[type="radio"]:checked + .color-label,
.choking-card input[type="radio"]:checked + .choking-label {
    background: rgba(255, 152, 0, 0.1);
}

.presence-card input[type="radio"]:checked + .presence-label .presence-icon,
.method-card input[type="radio"]:checked + .method-label .method-icon,
.odor-card input[type="radio"]:checked + .odor-label .odor-icon,
.color-card input[type="radio"]:checked + .color-label .color-icon,
.choking-card input[type="radio"]:checked + .choking-label .choking-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.presence-label, .method-label, .odor-label, .color-label, .choking-label {
    display: block;
    text-align: center;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.presence-icon, .method-icon, .odor-icon, .color-icon, .choking-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 4px;
    transition: all 0.3s ease;
}

.presence-icon .icon, .method-icon .icon, .odor-icon .icon, .color-icon .icon, .choking-icon .icon {
    font-size: 1rem;
    filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
}

.presence-text, .method-text, .odor-text, .color-text, .choking-text {
    font-weight: 500;
    font-size: 0.75rem;
    color: var(--text-primary);
    line-height: 1.1;
}

.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 6px;
}

.file-upload-area:hover {
    border-color: #FF9800;
    background: rgba(255, 152, 0, 0.05);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 6px;
    color: #6c757d;
}

.upload-text {
    color: #6c757d;
}

.upload-text strong {
    color: #FF9800;
}

.file-input {
    display: none;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.preview-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-remove:hover {
    background: #c82333;
}

/* Form sections styling */
.form-section {
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h4 {
    margin: 0 0 8px 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--dark-green);
}

.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .sputum-modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .sputum-presence-grid, .choking-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .expulsion-grid {
        grid-template-columns: 1fr;
        gap: 6px;
    }
    
    .odor-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }
    
    .color-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
    }
    
    .presence-icon, .method-icon, .odor-icon, .color-icon, .choking-icon {
        width: 24px;
        height: 24px;
    }
    
    .presence-text, .method-text, .odor-text, .color-text, .choking-text {
        font-size: 0.7rem;
    }
}
</style>

<script>
function openSputumModal() {
    const elderlyId = document.querySelector('input[name="elderly_id"]').value;
    
    if (!elderlyId) {
        alert('ไม่พบรหัสผู้สูงอายุ');
        return;
    }

    // Set elderly ID in the form
    document.getElementById('sputum_elderly_id').value = elderlyId;
    
    // Reset form
    document.getElementById('sputumForm').reset();
    
    // Set current date and time
    const now = new Date();
    document.getElementById('sputum_date').value = now.toISOString().split('T')[0];
    document.getElementById('sputum_time').value = now.toTimeString().substr(0,5);
    
    // Set default values
    document.getElementById('has_sputum_no').checked = true;
    document.getElementById('choking_no').checked = true;
    document.getElementById('sputum_recorded_by').value = '<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>';
    
    // Hide sputum details initially
    toggleSputumDetails(false);
    
    // Show modal
    document.getElementById('sputumModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Load previous records
    loadPreviousSputumRecords(elderlyId);
}

function closeSputumModal() {
    document.getElementById('sputumModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('sputumForm').reset();
    clearImagePreview();
}

function toggleSputumDetails(show) {
    const detailsSection = document.getElementById('sputumDetails');
    if (show) {
        detailsSection.style.display = 'block';
        // Set default values for sputum details
        document.getElementById('method_self').checked = true;
        document.getElementById('odor_normal').checked = true;
        document.getElementById('color_clear').checked = true;
    } else {
        detailsSection.style.display = 'none';
        // Clear all sputum detail selections
        const detailInputs = detailsSection.querySelectorAll('input[type="radio"]');
        detailInputs.forEach(input => input.checked = false);
    }
}

function loadPreviousSputumRecords(elderlyId) {
    fetch(`api/get_sputum.php?elderly_id=${elderlyId}&limit=5`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                displayPreviousSputumRecords(data.data);
                document.getElementById('previousSputumSection').style.display = 'block';
            } else {
                document.getElementById('previousSputumSection').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading previous sputum records:', error);
            document.getElementById('previousSputumSection').style.display = 'none';
        });
}

function displayPreviousSputumRecords(records) {
    const container = document.getElementById('previousSputumList');
    container.innerHTML = '';
    
    records.forEach(record => {
        const item = document.createElement('div');
        item.className = 'sputum-item';
        
        const recordDate = new Date(record.record_date + (record.record_time ? 'T' + record.record_time : '')).toLocaleString('th-TH');
        
        let sputumDetails = '';
        if (record.has_sputum === 'มีเสมหะ') {
            sputumDetails = `
                <p><strong>วิธีการขับ:</strong> ${record.expulsion_method || '-'}</p>
                <p><strong>กลิ่น:</strong> ${record.odor || '-'} | <strong>สี:</strong> ${record.color || '-'}</p>
            `;
        }
        
        item.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <strong>${recordDate}</strong>
                <span style="padding: 2px 8px; border-radius: 10px; font-size: 0.8rem; background: ${record.has_sputum === 'มีเสมหะ' ? '#fff3cd' : '#d4edda'}; color: ${record.has_sputum === 'มีเสมหะ' ? '#856404' : '#155724'};">
                    ${record.has_sputum}
                </span>
            </div>
            ${sputumDetails}
            <p><strong>การสำลัก:</strong> ${record.choking_status}</p>
            ${record.notes ? `<p><strong>หมายเหตุ:</strong> ${record.notes}</p>` : ''}
            <p style="font-size: 0.8rem; color: #6c757d;"><strong>ผู้บันทึก:</strong> ${record.recorded_by_name}</p>
        `;
        
        container.appendChild(item);
    });
}

// Image preview functionality
document.getElementById('sputum_images').addEventListener('change', function(e) {
    const files = e.target.files;
    const preview = document.getElementById('sputumImagePreview');
    
    // Clear previous previews
    clearImagePreview();
    
    // Limit to 5 files
    const maxFiles = Math.min(files.length, 5);
    
    for (let i = 0; i < maxFiles; i++) {
        const file = files[i];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="Preview">
                    <button type="button" class="preview-remove" onclick="removeImagePreview(this, ${i})">&times;</button>
                `;
                preview.appendChild(previewItem);
            };
            
            reader.readAsDataURL(file);
        }
    }
});

function clearImagePreview() {
    document.getElementById('sputumImagePreview').innerHTML = '';
}

function removeImagePreview(button, index) {
    const previewItem = button.parentElement;
    previewItem.remove();
    
    // Update file input by creating new FileList without the removed file
    const fileInput = document.getElementById('sputum_images');
    const dt = new DataTransfer();
    const files = fileInput.files;
    
    for (let i = 0; i < files.length; i++) {
        if (i !== index) {
            dt.items.add(files[i]);
        }
    }
    
    fileInput.files = dt.files;
}

// Form submission
document.getElementById('sputumForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Validate form
    const elderlyId = formData.get('elderly_id');
    const recordDate = formData.get('record_date');
    const hasSputum = formData.get('has_sputum');
    const chokingStatus = formData.get('choking_status');
    
    if (!elderlyId || !recordDate || !hasSputum || !chokingStatus) {
        alert('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
        return;
    }
    
    // If has sputum, check required sputum details
    if (hasSputum === 'มีเสมหะ') {
        const expulsionMethod = formData.get('expulsion_method');
        const odor = formData.get('odor');
        const color = formData.get('color');
        
        if (!expulsionMethod || !odor || !color) {
            alert('กรุณากรอกรายละเอียดเสมหะให้ครบถ้วน');
            return;
        }
    }
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '⏳ กำลังบันทึก...';
    submitBtn.disabled = true;
    
    // Submit to API
    fetch('api/save_sputum.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('บันทึกข้อมูลเสมหะเรียบร้อยแล้ว');
            closeSputumModal();
            // Reload page or update display
            if (typeof loadSputumSummary === 'function') {
                loadSputumSummary();
            }
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('เกิดข้อผิดพลาดในการบันทึกข้อมูล');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Close modal when clicking outside
document.getElementById('sputumModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSputumModal();
    }
});
</script>