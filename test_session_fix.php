<?php
/**
 * Test script to verify unified session system
 */

define('AIVORA_SECURITY', true);

// Test main app session
require_once __DIR__ . '/includes/security.php';
SecurityManager::secureSession();

echo "<h2>Session Test Results</h2>\n";
echo "<p>Session ID: " . session_id() . "</p>\n";
echo "<p>Session Name: " . session_name() . "</p>\n";
echo "<p>Session Status: " . session_status() . " (1=disabled, 2=active)</p>\n";

// Simulate user login for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'test_user';
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['facility_name'] = 'Test Facility';
    $_SESSION['last_activity'] = time();
    echo "<p style='color: green;'>✓ Test session created</p>\n";
}

// Test API session helper
require_once __DIR__ . '/api/session_helper.php';

echo "<h3>API Session Test</h3>\n";
try {
    initializeAPISession();
    echo "<p style='color: green;'>✓ API session initialization successful</p>\n";
    
    checkAuthentication();
    echo "<p style='color: green;'>✓ API authentication check passed</p>\n";
    
    $currentUser = getCurrentUser();
    echo "<p style='color: green;'>✓ Current user retrieved: " . ($currentUser['user_name'] ?? 'Unknown') . "</p>\n";
    
    echo "<p style='color: green;'><strong>Session Fix: SUCCESS!</strong></p>\n";
    echo "<p>The vital signs form should now work correctly.</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Session Data:</strong></p>\n";
echo "<pre>" . print_r($_SESSION, true) . "</pre>\n";

echo "<p><a href='?page=elderly_detail&id=1'>Test Elderly Detail Page</a></p>\n";
echo "<p><strong>Instructions:</strong> If this test shows all green checkmarks, your vital signs form should now work!</p>\n";
?>