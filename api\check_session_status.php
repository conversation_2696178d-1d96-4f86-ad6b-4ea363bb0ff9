<?php
// Simple session status check for debugging
header('Content-Type: application/json; charset=utf-8');

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Return session status
echo json_encode([
    'success' => true,
    'session_status' => session_status(),
    'session_id' => session_id(),
    'has_user_id' => isset($_SESSION['user_id']),
    'has_user_role' => isset($_SESSION['user_role']),
    'user_id' => $_SESSION['user_id'] ?? null,
    'user_role' => $_SESSION['user_role'] ?? null,
    'username' => $_SESSION['username'] ?? null,
    'facility_id' => $_SESSION['facility_id'] ?? null,
    'session_data' => [
        'count' => count($_SESSION),
        'keys' => array_keys($_SESSION)
    ]
]);
?>