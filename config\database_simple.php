<?php
// Simple database configuration without table creation
if (!defined('DB_HOST')) define('DB_HOST', '************');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '5545zz**');
if (!defined('DB_NAME')) define('DB_NAME', 'aivora_db');
if (!defined('DB_PORT')) define('DB_PORT', 3306);

// กำหนดค่าคงที่สำหรับความปลอดภัย
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เชื่อมต่อ MySQL โดยใช้ global variable
global $conn;
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME, DB_PORT);

// ตรวจสอบการเชื่อมต่อ
if ($conn->connect_error) {
    error_log("Connection failed: " . $conn->connect_error);
    exit('Database connection failed');
}

// ตั้งค่า charset เป็น utf8mb4
$conn->set_charset("utf8mb4");
?>