<?php
require_once 'config/database.php';

echo "Current database tables:\n";
echo "========================\n";

$result = $conn->query('SHOW TABLES');
if ($result) {
    while($row = $result->fetch_row()) {
        echo $row[0] . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

echo "\nChecking for vital_signs table specifically:\n";
$check = $conn->query("SHOW TABLES LIKE 'vital_signs'");
if ($check && $check->num_rows > 0) {
    echo "vital_signs table EXISTS\n";
    
    // Check structure
    echo "\nvital_signs table structure:\n";
    $structure = $conn->query("DESCRIBE vital_signs");
    while($row = $structure->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
} else {
    echo "vital_signs table MISSING\n";
}

echo "\nChecking for care_records table:\n";
$check2 = $conn->query("SHOW TABLES LIKE 'care_records'");
if ($check2 && $check2->num_rows > 0) {
    echo "care_records table EXISTS\n";
    
    // Check structure
    echo "\ncare_records table structure:\n";
    $structure = $conn->query("DESCRIBE care_records");
    while($row = $structure->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
} else {
    echo "care_records table MISSING\n";
}
?>