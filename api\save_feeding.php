<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// เริ่ม session และตรวจสอบการเข้าสู่ระบบ
session_start();

// ตรวจสอบว่าผู้ใช้เข้าสู่ระบบแล้วหรือไม่
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'กรุณาเข้าสู่ระบบ']);
    exit;
}

try {
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../includes/functions.php';

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // ตรวจสอบข้อมูลที่จำเป็น
    $elderly_id = isset($_POST['elderly_id']) ? (int)$_POST['elderly_id'] : 0;
    $feeding_date = $_POST['feeding_date'] ?? '';
    $feeding_time = $_POST['feeding_time'] ?? '';

    if (!$elderly_id || !$feeding_date || !$feeding_time) {
        throw new Exception('ข้อมูลไม่ครบถ้วน');
    }

    // ตรวจสอบสิทธิ์การเข้าถึงผู้สูงอายุ
    $user_role = $_SESSION['user_role'];
    if ($user_role !== 'admin') {
        // ตรวจสอบว่าผู้สูงอายุอยู่ในสถานพยาบาลเดียวกันหรือไม่
        $check_sql = "SELECT id FROM elderly WHERE id = ? AND facility_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("ii", $elderly_id, $_SESSION['facility_id']);
        $check_stmt->execute();
        
        if ($check_stmt->get_result()->num_rows === 0) {
            throw new Exception('ไม่มีสิทธิ์เข้าถึงข้อมูลผู้สูงอายุนี้');
        }
    }

    // รับข้อมูลจากฟอร์ม (checkbox arrays)
    $oral_food_amount = isset($_POST['oral_food_amount']) && is_array($_POST['oral_food_amount']) 
        ? json_encode($_POST['oral_food_amount']) : null;
    $choking = isset($_POST['choking']) && is_array($_POST['choking']) 
        ? json_encode($_POST['choking']) : null;
    $tube_food_type = isset($_POST['tube_food_type']) && is_array($_POST['tube_food_type']) 
        ? json_encode($_POST['tube_food_type']) : null;
    $tube_food_tolerance = isset($_POST['tube_food_tolerance']) && is_array($_POST['tube_food_tolerance']) 
        ? json_encode($_POST['tube_food_tolerance']) : null;
    $gastric_residue = isset($_POST['gastric_residue']) && is_array($_POST['gastric_residue']) 
        ? json_encode($_POST['gastric_residue']) : null;
    $gastric_residue_notes = $_POST['gastric_residue_notes'] ?? null;
    $feeding_status = isset($_POST['feeding_status']) && is_array($_POST['feeding_status']) 
        ? json_encode($_POST['feeding_status']) : null;
    $notes = $_POST['notes'] ?? null;

    // จัดการอัพโหลดรูปภาพ
    $uploaded_images = [];
    if (isset($_FILES['images']) && is_array($_FILES['images']['name'])) {
        $upload_dir = __DIR__ . '/../assets/img/elderly/' . $elderly_id . '/feeding/';
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $max_file_size = 5 * 1024 * 1024; // 5MB

        for ($i = 0; $i < count($_FILES['images']['name']) && $i < 5; $i++) {
            if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                $file_type = $_FILES['images']['type'][$i];
                $file_size = $_FILES['images']['size'][$i];

                if (!in_array($file_type, $allowed_types)) {
                    continue; // ข้ามไฟล์ที่ไม่ใช่รูปภาพ
                }

                if ($file_size > $max_file_size) {
                    continue; // ข้ามไฟล์ที่ใหญ่เกินไป
                }

                $file_extension = pathinfo($_FILES['images']['name'][$i], PATHINFO_EXTENSION);
                $filename = 'feeding_' . date('Y-m-d_H-i-s') . '_' . ($i + 1) . '.' . $file_extension;
                $file_path = $upload_dir . $filename;

                if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $file_path)) {
                    $uploaded_images[] = $filename;
                }
            }
        }
    }

    // เตรียมข้อมูลสำหรับบันทึก
    $images_json = !empty($uploaded_images) ? json_encode($uploaded_images) : null;

    // บันทึกข้อมูลลงฐานข้อมูล
    $sql = "INSERT INTO feeding_detailed_records (
        elderly_id, feeding_date, feeding_time,
        oral_food_amount, choking,
        tube_food_type, tube_food_tolerance, gastric_residue, gastric_residue_notes, feeding_status,
        notes, images, recorded_by, recorded_datetime
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error);
    }

    $stmt->bind_param(
        "issssssssssis",
        $elderly_id,
        $feeding_date,
        $feeding_time,
        $oral_food_amount,
        $choking,
        $tube_food_type,
        $tube_food_tolerance,
        $gastric_residue,
        $gastric_residue_notes,
        $feeding_status,
        $notes,
        $images_json,
        $_SESSION['user_id']
    );

    if (!$stmt->execute()) {
        throw new Exception('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' . $stmt->error);
    }

    $feeding_id = $conn->insert_id;

    // ส่งผลลัพธ์กลับ
    echo json_encode([
        'success' => true,
        'message' => 'บันทึกข้อมูลการทาน/ฟีดอาหารเรียบร้อยแล้ว',
        'data' => [
            'feeding_id' => $feeding_id,
            'elderly_id' => $elderly_id,
            'feeding_date' => $feeding_date,
            'feeding_time' => $feeding_time,
            'uploaded_images' => $uploaded_images
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>