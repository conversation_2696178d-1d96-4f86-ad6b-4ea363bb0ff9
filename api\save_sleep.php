<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role'])) {
    http_response_code(401);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit();
}

// ตรวจสอบสิทธิ์การเข้าถึง
$allowed_roles = ['admin', 'facility_admin', 'staff'];
if (!in_array($_SESSION['user_role'], $allowed_roles)) {
    http_response_code(403);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'ไม่มีสิทธิ์ในการบันทึกข้อมูลการนอนหลับ'
    ]);
    exit();
}

// ตรวจสอบ Content-Type (ยกเว้นสำหรับการทดสอบ)
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
if (!empty($contentType) && strpos($contentType, 'multipart/form-data') === false && strpos($contentType, 'application/x-www-form-urlencoded') === false) {
    http_response_code(400);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'รูปแบบข้อมูลไม่ถูกต้อง'
    ]);
    exit();
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

header('Content-Type: application/json');

try {
    // รับและตรวจสอบข้อมูลจากฟอร์ม
    $elderly_id = (int)($_POST['elderly_id'] ?? 0);
    $record_date = trim($_POST['record_date'] ?? '');
    $record_time = trim($_POST['record_time'] ?? '');
    $sleep_quality = trim($_POST['sleep_quality'] ?? '');
    $additional_notes = trim($_POST['additional_notes'] ?? '');
    $recorded_by_name = trim($_POST['recorded_by_name'] ?? '');
    
    // ตรวจสอบข้อมูลที่จำเป็น
    if (!$elderly_id || !$record_date || !$sleep_quality) {
        throw new Exception('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
    }
    
    // ตรวจสอบว่า elderly_id มีอยู่จริงในระบบ
    $check_elderly = $conn->prepare("SELECT id, facility_id FROM elderly WHERE id = ?");
    $check_elderly->bind_param("i", $elderly_id);
    $check_elderly->execute();
    $elderly_result = $check_elderly->get_result();
    
    if ($elderly_result->num_rows == 0) {
        throw new Exception('ไม่พบข้อมูลผู้สูงอายุในระบบ');
    }
    
    $elderly_data = $elderly_result->fetch_assoc();
    
    // ตรวจสอบสิทธิ์การเข้าถึงผู้สูงอายุ (สำหรับ facility_admin และ staff)
    if ($_SESSION['user_role'] !== 'admin') {
        if (!isset($_SESSION['facility_id']) || $_SESSION['facility_id'] != $elderly_data['facility_id']) {
            throw new Exception('ไม่มีสิทธิ์ในการบันทึกข้อมูลผู้สูงอายุนี้');
        }
    }
    
    // ตรวจสอบค่าที่อนุญาตสำหรับ sleep_quality
    $valid_sleep_qualities = [
        'นอนหลับสนิทดี',
        'นอนไม่ค่อยหลับ',
        'นอนไม่หลับ',
        'หลับๆตื่นๆ',
        'อื่นๆ'
    ];
    
    if (!in_array($sleep_quality, $valid_sleep_qualities, true)) {
        throw new Exception('กรุณาเลือกคุณภาพการนอนที่ถูกต้อง');
    }
    
    // ตรวจสอบและทำความสะอาดวันที่และเวลา
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $record_date)) {
        throw new Exception('รูปแบบวันที่ไม่ถูกต้อง');
    }
    
    if (!empty($record_time) && !preg_match('/^\d{2}:\d{2}$/', $record_time)) {
        throw new Exception('รูปแบบเวลาไม่ถูกต้อง');
    }
    
    // ตรวจสอบว่ามีการบันทึกในวันเดียวกันแล้วหรือไม่
    $check_existing = $conn->prepare("SELECT id FROM care_sleep WHERE elderly_id = ? AND record_date = ?");
    $check_existing->bind_param("is", $elderly_id, $record_date);
    $check_existing->execute();
    $existing_result = $check_existing->get_result();
    
    $is_update = false;
    $existing_id = null;
    
    if ($existing_result->num_rows > 0) {
        $is_update = true;
        $existing_row = $existing_result->fetch_assoc();
        $existing_id = $existing_row['id'];
    }
    
    // จัดการการอัพโหลดรูปภาพ
    $image_paths = [];
    if (isset($_FILES['sleep_images']) && !empty($_FILES['sleep_images']['name'][0])) {
        $upload_dir = __DIR__ . '/../assets/img/sleep/' . $elderly_id . '/';
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_files = 5;
        
        $file_count = min(count($_FILES['sleep_images']['name']), $max_files);
        
        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['sleep_images']['error'][$i] === UPLOAD_ERR_OK) {
                $file_type = $_FILES['sleep_images']['type'][$i];
                $file_size = $_FILES['sleep_images']['size'][$i];
                
                // ตรวจสอบประเภทไฟล์
                if (!in_array($file_type, $allowed_types)) {
                    continue; // ข้ามไฟล์ที่ไม่รองรับ
                }
                
                // ตรวจสอบขนาดไฟล์ (5MB)
                if ($file_size > 5 * 1024 * 1024) {
                    continue; // ข้ามไฟล์ที่ใหญ่เกินไป
                }
                
                $file_extension = pathinfo($_FILES['sleep_images']['name'][$i], PATHINFO_EXTENSION);
                $new_filename = 'sleep_' . date('YmdHis') . '_' . ($i + 1) . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;
                
                if (move_uploaded_file($_FILES['sleep_images']['tmp_name'][$i], $upload_path)) {
                    $image_paths[] = 'assets/img/sleep/' . $elderly_id . '/' . $new_filename;
                }
            }
        }
    }
    
    // เตรียมข้อมูลสำหรับบันทึกลงฐานข้อมูล
    $recorded_by = $_SESSION['user_id'];
    $recorded_by_name = $recorded_by_name ?: $_SESSION['username'];
    $recorded_datetime = date('Y-m-d H:i:s');
    
    $sleep_id = null;
    
    if ($is_update) {
        // อัพเดทข้อมูลเดิม
        $sql = "UPDATE care_sleep SET 
                    record_time = ?, sleep_quality = ?, additional_notes = ?,
                    recorded_by = ?, recorded_by_name = ?, updated_at = ?
                WHERE id = ?";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error);
        }
        
        $stmt->bind_param(
            "ssssssi",
            $record_time,
            $sleep_quality,
            $additional_notes,
            $recorded_by,
            $recorded_by_name,
            $recorded_datetime,
            $existing_id
        );
        
        if (!$stmt->execute()) {
            throw new Exception('เกิดข้อผิดพลาดในการอัพเดทข้อมูล: ' . $stmt->error);
        }
        
        $sleep_id = $existing_id;
        $action = 'อัพเดท';
        
    } else {
        // บันทึกข้อมูลใหม่
        $sql = "INSERT INTO care_sleep (
                    elderly_id, record_date, record_time, sleep_quality,
                    additional_notes, recorded_by, recorded_by_name, recorded_datetime
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error);
        }
        
        $stmt->bind_param(
            "isssssss",
            $elderly_id,
            $record_date,
            $record_time,
            $sleep_quality,
            $additional_notes,
            $recorded_by,
            $recorded_by_name,
            $recorded_datetime
        );
        
        if (!$stmt->execute()) {
            throw new Exception('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' . $stmt->error);
        }
        
        $sleep_id = $conn->insert_id;
        $action = 'บันทึก';
    }
    
    // บันทึกรูปภาพ (ถ้ามี) ลงในตารางแยก
    if (!empty($image_paths)) {
        // ลบรูปภาพเก่า (ถ้าเป็นการอัพเดท)
        if ($is_update) {
            $delete_images = $conn->prepare("DELETE FROM care_sleep_images WHERE sleep_id = ?");
            $delete_images->bind_param("i", $sleep_id);
            $delete_images->execute();
        }
        
        $image_sql = "INSERT INTO care_sleep_images (sleep_id, image_path, uploaded_at) VALUES (?, ?, ?)";
        $image_stmt = $conn->prepare($image_sql);
        
        if ($image_stmt) {
            foreach ($image_paths as $image_path) {
                $image_stmt->bind_param("iss", $sleep_id, $image_path, $recorded_datetime);
                $image_stmt->execute();
            }
        }
    }
    
    // บันทึก log การทำงาน
    $log_message = "{$action}การนอนหลับ - ผู้สูงอายุ ID: {$elderly_id}, คุณภาพ: {$sleep_quality}";
    error_log("[" . date('Y-m-d H:i:s') . "] {$log_message} - ผู้บันทึก: {$recorded_by_name}");
    
    // ส่งผลลัพธ์กลับ
    echo json_encode([
        'success' => true,
        'message' => $action . 'ข้อมูลการนอนหลับเรียบร้อยแล้ว',
        'data' => [
            'id' => $sleep_id,
            'elderly_id' => $elderly_id,
            'record_date' => $record_date,
            'record_time' => $record_time,
            'sleep_quality' => $sleep_quality,
            'additional_notes' => $additional_notes,
            'image_count' => count($image_paths),
            'recorded_by' => $recorded_by_name,
            'recorded_datetime' => $recorded_datetime,
            'is_update' => $is_update
        ]
    ]);
    
} catch (Exception $e) {
    // บันทึก error log
    error_log("[" . date('Y-m-d H:i:s') . "] Error in save_sleep.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>