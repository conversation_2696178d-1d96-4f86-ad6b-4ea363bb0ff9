[2025-08-14 22:08:48] DEBUG: Debug tool started
[2025-08-14 22:08:48] DEBUG: Database connection successful | Data: {"host":"localhost via TCP\/IP","server_version":100432}
[2025-08-14 22:08:48] DEBUG: Session data | Data: {"session_id":"31521l9kg6m34kniolp5qla8ne","user_role":"not set","user_id":"not set","facility_id":"not set","username":"not set"}
[2025-08-14 22:08:48] DEBUG: Table status | Data: {"existing":["users","elderly","facilities","schedules","care_line_notifications","care_vital_signs","care_turning_records","care_pressure_ulcers","care_weight_height","care_incident_reports","care_medication_records","care_symptom_records","care_excretion_records","care_hygiene_records","care_feeding_records","care_activity_records","care_sputum_records","care_mental_state_records","care_sleep_records"],"missing":["events"]}
[2025-08-14 22:08:48] DEBUG: API file exists: api/save_care_record.php
[2025-08-14 22:08:48] DEBUG: API file exists: api/save_vital_signs.php
[2025-08-14 22:08:48] DEBUG: API file exists: api/get_vital_signs.php
[2025-08-14 22:08:48] DEBUG: API file exists: api/check_today_vital_signs.php
[2025-08-14 22:08:48] DEBUG: Debug tool completed
[2025-08-14 22:10:04] DEBUG: Debug tool started
[2025-08-14 22:10:04] DEBUG: Database connection successful | Data: {"host":"localhost via TCP\/IP","server_version":100432}
[2025-08-14 22:10:04] DEBUG: Session data | Data: {"session_id":"31521l9kg6m34kniolp5qla8ne","user_role":"not set","user_id":"not set","facility_id":"not set","username":"not set"}
[2025-08-14 22:10:05] DEBUG: Table status | Data: {"existing":["users","elderly","facilities","schedules","care_line_notifications","care_vital_signs","care_turning_records","care_pressure_ulcers","care_weight_height","care_incident_reports","care_medication_records","care_symptom_records","care_excretion_records","care_hygiene_records","care_feeding_records","care_activity_records","care_sputum_records","care_mental_state_records","care_sleep_records"],"missing":["events"]}
[2025-08-14 22:10:05] DEBUG: API file exists: api/save_care_record.php
[2025-08-14 22:10:05] DEBUG: API file exists: api/save_vital_signs.php
[2025-08-14 22:10:05] DEBUG: API file exists: api/get_vital_signs.php
[2025-08-14 22:10:05] DEBUG: API file exists: api/check_today_vital_signs.php
[2025-08-14 22:10:05] DEBUG: Debug tool completed
