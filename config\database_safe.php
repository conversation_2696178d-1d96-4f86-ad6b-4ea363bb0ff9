<?php
// Safe database configuration
if (!defined('DB_HOST')) define('DB_HOST', '************');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '5545zz**');
if (!defined('DB_NAME')) define('DB_NAME', 'aivora_db');
if (!defined('DB_PORT')) define('DB_PORT', 3306);

// กำหนดค่าคงที่สำหรับความปลอดภัย
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เชื่อมต่อ MySQL โดยใช้ global variable
global $conn;
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME, DB_PORT);

// ตรวจสอบการเชื่อมต่อ
if ($conn->connect_error) {
    error_log("Connection failed: " . $conn->connect_error);
    exit('Database connection failed');
}

// ตั้งค่า charset เป็น utf8mb4
$conn->set_charset("utf8mb4");

// สร้างตารางโดยไม่มี Foreign Key ก่อน
$tables = [
    "CREATE TABLE IF NOT EXISTS facilities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        facility_code VARCHAR(50) UNIQUE NOT NULL,
        facility_name VARCHAR(255) NOT NULL,
        facility_type VARCHAR(100) DEFAULT 'elderly_care_center',
        bed_capacity INT DEFAULT 0,
        license_number VARCHAR(50),
        tax_id VARCHAR(13),
        address TEXT NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(100),
        website VARCHAR(255),
        contact_person VARCHAR(100),
        contact_phone VARCHAR(20),
        contact_email VARCHAR(100),
        capacity INT DEFAULT 0,
        current_occupancy INT DEFAULT 0,
        status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'active',
        registration_date DATE,
        expiry_date DATE,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        role ENUM('admin', 'staff', 'facility_admin') NOT NULL,
        facility_id INT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        force_password_change TINYINT(1) DEFAULT 0,
        last_login DATETIME,
        failed_attempts INT DEFAULT 0,
        account_locked TINYINT(1) DEFAULT 0,
        password_reset_token VARCHAR(255) NULL,
        password_reset_expires DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS elderly (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title ENUM('นาย', 'นาง', 'นางสาว') NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        nickname VARCHAR(50),
        birth_date DATE NOT NULL,
        id_card VARCHAR(13) UNIQUE NOT NULL,
        blood_type VARCHAR(5) NULL,
        allergies TEXT,
        medical_conditions TEXT,
        emergency_contact TEXT NOT NULL,
        facility_id INT,
        room_id INT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS rooms (
        id INT AUTO_INCREMENT PRIMARY KEY,
        facility_id INT NOT NULL,
        room_number VARCHAR(20) NOT NULL,
        type ENUM('single', 'double') DEFAULT 'single',
        floor INT DEFAULT 1,
        capacity INT DEFAULT 1,
        price DECIMAL(10,2) DEFAULT 0.00,
        description TEXT,
        amenities TEXT,
        status ENUM('available', 'occupied', 'maintenance') DEFAULT 'available',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_room_per_facility (facility_id, room_number)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

// สร้างตาราง
foreach ($tables as $sql) {
    if ($conn->query($sql) !== TRUE) {
        error_log("Error creating table: " . $conn->error);
    }
}

// เพิ่มตารางสำหรับระบบการดูแลผู้สูงอายุ
$care_tables = [
    "CREATE TABLE IF NOT EXISTS care_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        care_type ENUM('vital_signs', 'symptoms', 'medication', 'incident', 'feeding', 'excretion', 'sleep') NOT NULL,
        recorded_by INT NOT NULL,
        record_datetime DATETIME NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_elderly_care (elderly_id, care_type),
        INDEX idx_record_date (record_datetime)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS vital_signs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        care_record_id INT NOT NULL,
        temperature DECIMAL(4,1),
        blood_pressure_systolic INT,
        blood_pressure_diastolic INT,
        heart_rate INT,
        respiratory_rate INT,
        oxygen_saturation DECIMAL(5,2),
        weight DECIMAL(5,2),
        height DECIMAL(5,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS symptom_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        care_record_id INT NOT NULL,
        symptoms TEXT,
        pain_level INT DEFAULT 0,
        consciousness_level ENUM('alert', 'drowsy', 'unconscious', 'confused') DEFAULT 'alert',
        mobility_status ENUM('independent', 'assisted', 'bedridden') DEFAULT 'independent',
        appetite ENUM('good', 'fair', 'poor', 'none') DEFAULT 'good',
        mood ENUM('happy', 'normal', 'sad', 'anxious', 'agitated') DEFAULT 'normal',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS medication_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        care_record_id INT NOT NULL,
        medication_name VARCHAR(255) NOT NULL,
        dosage VARCHAR(100),
        route ENUM('oral', 'injection', 'topical', 'inhalation', 'other') DEFAULT 'oral',
        frequency VARCHAR(100),
        given_time DATETIME NOT NULL,
        reaction TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS incident_reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        care_record_id INT NOT NULL,
        incident_type ENUM('fall', 'injury', 'behavioral', 'medical_emergency', 'medication_error', 'other') NOT NULL,
        severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        description TEXT NOT NULL,
        action_taken TEXT,
        follow_up_required BOOLEAN DEFAULT FALSE,
        reported_to VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS feeding_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        care_record_id INT NOT NULL,
        meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack') NOT NULL,
        food_amount ENUM('full', 'three_quarters', 'half', 'quarter', 'none') DEFAULT 'full',
        appetite ENUM('excellent', 'good', 'fair', 'poor', 'none') DEFAULT 'good',
        assistance_needed BOOLEAN DEFAULT FALSE,
        difficulty_swallowing BOOLEAN DEFAULT FALSE,
        fluid_intake_ml INT DEFAULT 0,
        special_diet TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS excretion_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        care_record_id INT NOT NULL,
        urine_frequency INT DEFAULT 0,
        urine_color ENUM('pale_yellow', 'yellow', 'dark_yellow', 'brown', 'red', 'other') DEFAULT 'pale_yellow',
        urine_amount ENUM('normal', 'increased', 'decreased', 'none') DEFAULT 'normal',
        bowel_movement BOOLEAN DEFAULT FALSE,
        stool_consistency ENUM('hard', 'normal', 'soft', 'loose', 'watery') DEFAULT 'normal',
        stool_color ENUM('brown', 'black', 'green', 'yellow', 'red', 'other') DEFAULT 'brown',
        incontinence BOOLEAN DEFAULT FALSE,
        assistance_needed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS sleep_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        care_record_id INT NOT NULL,
        sleep_time TIME,
        wake_time TIME,
        sleep_duration_hours DECIMAL(4,2),
        sleep_quality ENUM('excellent', 'good', 'fair', 'poor', 'very_poor') DEFAULT 'good',
        disturbances TEXT,
        sleep_aids_used VARCHAR(255),
        nap_during_day BOOLEAN DEFAULT FALSE,
        nap_duration_minutes INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

// สร้างตารางการดูแล
foreach ($care_tables as $sql) {
    if ($conn->query($sql) !== TRUE) {
        error_log("Error creating care table: " . $conn->error);
    }
}

// สร้าง admin user ถ้ายังไม่มี (เฉพาะเมื่อตาราง users มีอยู่)
$check_users_table = $conn->query("SHOW TABLES LIKE 'users'");
if ($check_users_table && $check_users_table->num_rows > 0) {
    $default_username = 'admin';
    $default_password = password_hash('admin123', PASSWORD_DEFAULT);
    $check_admin = $conn->query("SELECT id FROM users WHERE username = 'admin'");
    if ($check_admin && $check_admin->num_rows == 0) {
        $sql = "INSERT INTO users (username, password, name, role, facility_id) VALUES ('admin', '$default_password', 'Administrator', 'admin', NULL)";
        if ($conn->query($sql) !== TRUE) {
            error_log("Error creating default admin user: " . $conn->error);
        }
    }
}
?>