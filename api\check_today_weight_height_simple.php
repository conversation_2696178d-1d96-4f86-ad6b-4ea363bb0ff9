<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
define('AIVORA_SECURITY', true);

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Include required files
require_once __DIR__ . '/../config/database_simple.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// Start session using the proper session manager
SessionManager::start();

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit();
}

try {
    // Get current user
    $currentUser = getCurrentUser();
    
    // รับพารามิเตอร์
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;

    if ($elderly_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบรหัสผู้สูงอายุ'
        ]);
        exit();
    }

    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและผู้ใช้มีสิทธิ์เข้าถึง
    $isAdmin = $currentUser['role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin && !empty($currentUser['facility_id'])) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode([
            'success' => false,
            'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล'
        ]);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง'
        ]);
        exit();
    }

    // ตรวจสอบว่าวันนี้มีการบันทึกน้ำหนักส่วนสูงแล้วหรือยัง
    $today = date('Y-m-d');
    $sql = "SELECT cw.id, cw.weight, cw.height, cw.bmi, cw.measurement_method, cw.measurement_date, cw.notes, cw.recorded_at,
                   COALESCE(u.name, u.username, 'ระบบ') as recorded_by_name, cw.recorded_at as created_at
            FROM care_weight_height cw 
            LEFT JOIN users u ON cw.user_id = u.id 
            WHERE cw.elderly_id = ? AND DATE(cw.measurement_date) = ? 
            ORDER BY cw.recorded_at DESC 
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode([
            'success' => false,
            'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL'
        ]);
        exit();
    }

    $stmt->bind_param("is", $elderly_id, $today);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // มีข้อมูลวันนี้แล้ว
        $data = $result->fetch_assoc();
        echo json_encode([
            'success' => true,
            'has_today_record' => true,
            'data' => $data
        ]);
    } else {
        // ยังไม่มีข้อมูลวันนี้
        echo json_encode([
            'success' => true,
            'has_today_record' => false
        ]);
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in check_today_weight_height_simple.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดระบบ'
    ]);
}

$conn->close();
?>