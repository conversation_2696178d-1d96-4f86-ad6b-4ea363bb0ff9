<?php
define('AIVORA_SECURITY', true);
require_once __DIR__ . '/config/database.php';

echo "<h2>Care Tables Status Check</h2>\n";

$care_tables = [
    'care_line_notifications' => "CREATE TABLE IF NOT EXISTS care_line_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        notification_type VARCHAR(50) DEFAULT 'daily_report',
        message TEXT,
        recipient_info JSON,
        delivery_status VARCHAR(20) DEFAULT 'pending',
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_vital_signs' => "CREATE TABLE IF NOT EXISTS care_vital_signs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        temperature DECIMAL(4,1),
        blood_pressure_systolic INT,
        blood_pressure_diastolic INT,
        heart_rate INT,
        respiratory_rate INT,
        oxygen_saturation DECIMAL(5,2),
        weight DECIMAL(5,2),
        height DECIMAL(5,2),
        pain_scale INT,
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_turning_records' => "CREATE TABLE IF NOT EXISTS care_turning_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        turn_time TIME,
        position_from VARCHAR(50),
        position_to VARCHAR(50),
        skin_condition VARCHAR(50),
        comfort_level VARCHAR(50),
        assistance_needed VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_pressure_ulcers' => "CREATE TABLE IF NOT EXISTS care_pressure_ulcers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        ulcer_location VARCHAR(100),
        ulcer_stage VARCHAR(20),
        ulcer_size VARCHAR(50),
        ulcer_condition VARCHAR(50),
        treatment_applied TEXT,
        wound_care_products TEXT,
        pain_level INT,
        drainage VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_weight_height' => "CREATE TABLE IF NOT EXISTS care_weight_height (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        weight DECIMAL(5,2),
        height DECIMAL(5,2),
        bmi DECIMAL(4,2),
        measurement_method VARCHAR(50),
        measurement_date DATE,
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_incident_reports' => "CREATE TABLE IF NOT EXISTS care_incident_reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        incident_type VARCHAR(50),
        severity VARCHAR(20),
        incident_description TEXT,
        location VARCHAR(100),
        witnesses TEXT,
        action_taken TEXT,
        injury_sustained VARCHAR(50),
        medical_attention BOOLEAN DEFAULT FALSE,
        family_notified BOOLEAN DEFAULT FALSE,
        incident_date DATETIME,
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_medication_records' => "CREATE TABLE IF NOT EXISTS care_medication_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        medication_name VARCHAR(200),
        dosage VARCHAR(100),
        route VARCHAR(50),
        frequency VARCHAR(100),
        administration_time DATETIME,
        administered_by VARCHAR(100),
        medication_status VARCHAR(50),
        reason_if_not_given TEXT,
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_symptom_records' => "CREATE TABLE IF NOT EXISTS care_symptom_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        symptom_category VARCHAR(50),
        symptom_description TEXT,
        severity VARCHAR(20),
        onset_time DATETIME,
        duration_hours INT,
        assessment_findings TEXT,
        interventions_applied TEXT,
        response_to_interventions VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_excretion_records' => "CREATE TABLE IF NOT EXISTS care_excretion_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        bowel_movement BOOLEAN DEFAULT FALSE,
        bowel_consistency VARCHAR(50),
        bowel_color VARCHAR(50),
        bowel_amount VARCHAR(50),
        urination BOOLEAN DEFAULT FALSE,
        urine_color VARCHAR(50),
        urine_amount VARCHAR(50),
        frequency_today INT DEFAULT 0,
        assistance_needed VARCHAR(50),
        continence_status VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_hygiene_records' => "CREATE TABLE IF NOT EXISTS care_hygiene_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        bath_type VARCHAR(50),
        bath_assistance VARCHAR(50),
        oral_care BOOLEAN DEFAULT FALSE,
        oral_care_type VARCHAR(50),
        hair_care BOOLEAN DEFAULT FALSE,
        hair_care_type VARCHAR(50),
        nail_care BOOLEAN DEFAULT FALSE,
        nail_care_type VARCHAR(50),
        skin_care BOOLEAN DEFAULT FALSE,
        skin_care_products TEXT,
        assistance_level_overall VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_feeding_records' => "CREATE TABLE IF NOT EXISTS care_feeding_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        meal_type VARCHAR(50),
        food_intake_percentage INT,
        fluid_intake_ml INT DEFAULT 0,
        feeding_method VARCHAR(50),
        appetite VARCHAR(50),
        difficulty_swallowing BOOLEAN DEFAULT FALSE,
        difficulty_chewing BOOLEAN DEFAULT FALSE,
        food_preferences TEXT,
        assistance_needed VARCHAR(50),
        meal_duration_minutes INT,
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_activity_records' => "CREATE TABLE IF NOT EXISTS care_activity_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        activity_type VARCHAR(50),
        activity_name VARCHAR(200),
        duration_minutes INT DEFAULT 0,
        participation_level VARCHAR(50),
        mood_during VARCHAR(50),
        mood_after VARCHAR(50),
        assistance_needed VARCHAR(50),
        achievement_level VARCHAR(50),
        social_interaction VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_sputum_records' => "CREATE TABLE IF NOT EXISTS care_sputum_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        sputum_amount VARCHAR(50),
        sputum_color VARCHAR(50),
        sputum_consistency VARCHAR(50),
        cough_frequency VARCHAR(50),
        cough_type VARCHAR(50),
        breathing_difficulty VARCHAR(50),
        oxygen_needed BOOLEAN DEFAULT FALSE,
        oxygen_level_percent DECIMAL(5,2),
        chest_sounds VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_mental_state_records' => "CREATE TABLE IF NOT EXISTS care_mental_state_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        mood VARCHAR(50),
        anxiety_level VARCHAR(50),
        cognitive_function VARCHAR(50),
        orientation_person BOOLEAN DEFAULT TRUE,
        orientation_place BOOLEAN DEFAULT TRUE,
        orientation_time BOOLEAN DEFAULT TRUE,
        social_interaction VARCHAR(50),
        sleep_quality VARCHAR(50),
        behavioral_changes TEXT,
        communication_ability VARCHAR(50),
        memory_function VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'care_sleep_records' => "CREATE TABLE IF NOT EXISTS care_sleep_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        facility_id INT,
        sleep_date DATE,
        sleep_time TIME,
        wake_time TIME,
        sleep_duration_hours DECIMAL(4,2),
        sleep_quality VARCHAR(50),
        night_wakings INT DEFAULT 0,
        difficulty_falling_asleep BOOLEAN DEFAULT FALSE,
        difficulty_staying_asleep BOOLEAN DEFAULT FALSE,
        sleep_position VARCHAR(50),
        sleep_aids_used TEXT,
        nap_during_day BOOLEAN DEFAULT FALSE,
        nap_duration_minutes INT DEFAULT 0,
        sleep_environment VARCHAR(50),
        notes TEXT,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

$created_tables = 0;
$existing_tables = 0;
$failed_tables = 0;

foreach ($care_tables as $table_name => $create_sql) {
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
    echo "<strong>Checking table: $table_name</strong><br>";
    
    // Check if table exists
    $check_result = $conn->query("SHOW TABLES LIKE '$table_name'");
    
    if ($check_result && $check_result->num_rows > 0) {
        echo "✅ Table already exists<br>";
        $existing_tables++;
    } else {
        echo "❌ Table does not exist. Creating...<br>";
        
        if ($conn->query($create_sql) === TRUE) {
            echo "✅ Table created successfully<br>";
            $created_tables++;
        } else {
            echo "❌ Error creating table: " . $conn->error . "<br>";
            $failed_tables++;
        }
    }
    echo "</div>";
}

echo "<div style='margin: 20px 0; padding: 15px; background: #f0f0f0; border: 1px solid #ccc;'>";
echo "<h3>Summary:</h3>";
echo "✅ Tables already existing: $existing_tables<br>";
echo "🆕 Tables created: $created_tables<br>";
echo "❌ Tables failed to create: $failed_tables<br>";
echo "</div>";

$conn->close();
?>