<?php
// save_medication.php - API สำหรับบันทึกการให้ยา

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

header('Content-Type: application/json; charset=utf-8');

// เริ่ม session และโหลด auth system
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// โหลด auth functions
require_once __DIR__ . '/../includes/auth.php';

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Only POST requests are accepted.'
    ]);
    exit;
}

// ตรวจสอบการเข้าสู่ระบบโดยใช้ฟังก์ชัน isLoggedIn
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์การเข้าถึงโดยใช้ SessionManager
if (!SessionManager::hasPermission('elderly')) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'คุณไม่มีสิทธิ์ในการบันทึกข้อมูลการให้ยา'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

try {
    // รับข้อมูลจากฟอร์ม
    $elderly_id = filter_input(INPUT_POST, 'elderly_id', FILTER_VALIDATE_INT);
    $medication_date = filter_input(INPUT_POST, 'medication_date', FILTER_SANITIZE_STRING);
    $medication_name = filter_input(INPUT_POST, 'medication_name', FILTER_SANITIZE_STRING);
    $medication_dosage = filter_input(INPUT_POST, 'medication_dosage', FILTER_SANITIZE_STRING);
    $medication_route = filter_input(INPUT_POST, 'medication_route', FILTER_SANITIZE_STRING);
    $medication_notes = filter_input(INPUT_POST, 'medication_notes', FILTER_SANITIZE_STRING);
    $given_by = filter_input(INPUT_POST, 'given_by', FILTER_SANITIZE_STRING);
    $time_periods = $_POST['time_periods'] ?? [];

    // ตรวจสอบข้อมูลที่จำเป็น
    if (!$elderly_id || !$medication_date || !$medication_name || !$medication_dosage || !$medication_route || empty($time_periods)) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณากรอกข้อมูลให้ครบถ้วน'
        ]);
        exit;
    }

    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและอยู่ในสถานพยาบาลเดียวกัน (สำหรับ facility_admin และ staff)
    $elderly_check_sql = "SELECT id, facility_id FROM elderly WHERE id = ?";
    $elderly_check_params = [$elderly_id];
    
    if ($_SESSION['user_role'] !== 'admin') {
        $elderly_check_sql .= " AND facility_id = ?";
        $elderly_check_params[] = $_SESSION['facility_id'];
    }
    
    $elderly_check_stmt = $conn->prepare($elderly_check_sql);
    $elderly_check_stmt->bind_param(str_repeat('i', count($elderly_check_params)), ...$elderly_check_params);
    $elderly_check_stmt->execute();
    $elderly_result = $elderly_check_stmt->get_result();
    
    if ($elderly_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือคุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้'
        ]);
        exit;
    }

    // สร้างตารางการให้ยาถ้ายังไม่มี (ไม่ใช้ foreign key เพื่อหลีกเลี่ยงปัญหา)
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS medication_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        medication_date DATE NOT NULL,
        medication_name VARCHAR(255) NOT NULL,
        medication_dosage VARCHAR(100) NOT NULL,
        medication_route ENUM('oral', 'injection', 'topical', 'inhaled', 'sublingual', 'rectal', 'other') NOT NULL,
        time_period ENUM('morning', 'afternoon', 'evening', 'bedtime', 'as_needed') NOT NULL,
        administration_time TIME,
        notes TEXT,
        given_by VARCHAR(100),
        recorded_by INT,
        recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_datetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_elderly_date (elderly_id, medication_date),
        INDEX idx_medication_name (medication_name),
        INDEX idx_time_period (time_period),
        INDEX idx_recorded_datetime (recorded_datetime)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if (!$conn->query($create_table_sql)) {
        error_log("Failed to create medication_records table: " . $conn->error);
        // ลองสร้างแบบง่าย ๆ หากมีปัญหา
        $simple_create = "
        CREATE TABLE IF NOT EXISTS medication_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            elderly_id INT NOT NULL,
            medication_date DATE NOT NULL,
            medication_name VARCHAR(255) NOT NULL,
            medication_dosage VARCHAR(100) NOT NULL,
            medication_route VARCHAR(50) NOT NULL,
            time_period VARCHAR(50) NOT NULL,
            administration_time TIME,
            notes TEXT,
            given_by VARCHAR(100),
            recorded_by INT,
            recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $conn->query($simple_create);
    }

    // เริ่ม transaction
    $conn->begin_transaction();

    try {

        // เตรียมคำสั่ง SQL สำหรับบันทึกข้อมูล
        $insert_sql = "
        INSERT INTO medication_records 
        (elderly_id, medication_date, medication_name, medication_dosage, medication_route, 
         time_period, administration_time, notes, given_by, recorded_by) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";
        
        $insert_stmt = $conn->prepare($insert_sql);
        
        // บันทึกข้อมูลสำหรับแต่ละช่วงเวลา
        foreach ($time_periods as $time_period) {
            $administration_time = $_POST[$time_period . '_time'] ?? null;
            
            // ถ้าไม่มีเวลาให้ตั้งเป็น null (ซึ่งฐานข้อมูลอนุญาต)
            // แต่ยังสามารถบันทึกช่วงเวลาได้
            
            $insert_stmt->bind_param(
                'issssssssi',
                $elderly_id,
                $medication_date,
                $medication_name,
                $medication_dosage,
                $medication_route,
                $time_period,
                $administration_time,
                $medication_notes,
                $given_by,
                $_SESSION['user_id']
            );
            
            if (!$insert_stmt->execute()) {
                throw new Exception('ไม่สามารถบันทึกข้อมูลการให้ยาได้: ' . $insert_stmt->error);
            }
        }
        
        // commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'บันทึกการให้ยาเรียบร้อยแล้ว',
            'data' => [
                'elderly_id' => $elderly_id,
                'medication_date' => $medication_date,
                'medication_name' => $medication_name,
                'time_periods' => $time_periods
            ]
        ]);
        
    } catch (Exception $e) {
        // rollback transaction
        $conn->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Error in save_medication.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>