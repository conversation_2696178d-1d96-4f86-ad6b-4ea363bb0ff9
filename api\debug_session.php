<?php
declare(strict_types=1);
define("AIVORA_SECURITY", true);

// Start session
session_start();

// Set content type
header("Content-Type: application/json; charset=utf-8");
header("Cache-Control: no-cache, must-revalidate");

// Debug information
$debug_info = [
    'session_id' => session_id(),
    'session_status' => session_status(),
    'session_data' => $_SESSION ?? [],
    'current_time' => time(),
    'session_variables' => [
        'user_id' => $_SESSION['user_id'] ?? 'not set',
        'username' => $_SESSION['username'] ?? 'not set',
        'user_role' => $_SESSION['user_role'] ?? 'not set',
        'facility_id' => $_SESSION['facility_id'] ?? 'not set',
        'last_activity' => $_SESSION['last_activity'] ?? 'not set'
    ]
];

// Calculate session age if last_activity exists
if (isset($_SESSION['last_activity'])) {
    $debug_info['session_age_seconds'] = time() - $_SESSION['last_activity'];
    $debug_info['session_age_minutes'] = round((time() - $_SESSION['last_activity']) / 60, 2);
    $debug_info['session_lifetime'] = 3600; // 1 hour
    $debug_info['is_expired_by_time'] = (time() - $_SESSION['last_activity']) > 3600;
}

try {
    require_once __DIR__ . "/../includes/functions.php";
    require_once __DIR__ . "/../includes/auth.php";
    
    // Test each function individually
    $debug_info['auth_functions'] = [
        'SessionManager::isLoggedIn()' => SessionManager::isLoggedIn(),
        'SessionManager::isExpired()' => SessionManager::isExpired(),
        'isLoggedIn()' => isLoggedIn(),
        'isSessionExpired()' => isSessionExpired(),
    ];
    
    if (isset($_SESSION['last_activity'])) {
        $debug_info['auth_functions']['getSessionTimeRemaining()'] = getSessionTimeRemaining();
    }
    
    // Get current user if possible
    try {
        $debug_info['getCurrentUser()'] = getCurrentUser();
    } catch (Exception $e) {
        $debug_info['getCurrentUser()'] = 'Error: ' . $e->getMessage();
    }
    
} catch (Exception $e) {
    $debug_info['auth_error'] = $e->getMessage();
    $debug_info['auth_trace'] = $e->getTraceAsString();
}

echo json_encode($debug_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>