<?php
define('AIVORA_SECURITY', true);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/config/database.php';

// ล็อกอินอัตโนมัติสำหรับทดสอบ
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['facility_id'] = 1;
$_SESSION['last_activity'] = time();

echo "🔍 Debug การบันทึกสุขอนามัย\n";
echo "================================\n";

// จำลองข้อมูลที่จะส่ง
$elderly_id = 1;
$record_date = date('Y-m-d');
$diaper_count = 3;
$diaper_pad_count = 2;
$bathing = 'self';
$oral_care = 'done';
$hair_wash = 'done';
$other_activities = ['haircut', 'nail_cut'];
$other_activities_json = json_encode($other_activities);
$notes = 'ทดสอบการบันทึกสุขอนามัย';

echo "📊 ข้อมูลที่จะบันทึก:\n";
echo "elderly_id: $elderly_id\n";
echo "diaper_count: $diaper_count\n";
echo "bathing: $bathing\n";
echo "other_activities: " . implode(', ', $other_activities) . "\n";
echo "notes: $notes\n\n";

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if ($conn->connect_error) {
    die("❌ เชื่อมต่อฐานข้อมูลไม่ได้: " . $conn->connect_error);
}
echo "✅ เชื่อมต่อฐานข้อมูลสำเร็จ\n";

// ตรวจสอบว่าผู้สูงอายุมีอยู่
$elderly_check = $conn->prepare("SELECT id, facility_id FROM elderly WHERE id = ?");
$elderly_check->bind_param('i', $elderly_id);
$elderly_check->execute();
$elderly_result = $elderly_check->get_result();

if ($elderly_result->num_rows === 0) {
    die("❌ ไม่พบข้อมูลผู้สูงอายุ ID: $elderly_id\n");
}
echo "✅ พบข้อมูลผู้สูงอายุ ID: $elderly_id\n";

echo "\n📝 ข้อมูลที่แปลงแล้วสำหรับ database:\n";
echo "elderly_id: $elderly_id (int)\n";
echo "record_date: $record_date (string)\n";
echo "diaper_count: $diaper_count (int)\n";
echo "diaper_pad_count: $diaper_pad_count (int)\n";
echo "bathing: $bathing (string)\n";
echo "oral_care: $oral_care (string)\n";
echo "hair_wash: $hair_wash (string)\n";
echo "other_activities_json: $other_activities_json (string)\n";
echo "notes: $notes (string)\n";

// SQL statement
$insert_sql = "
INSERT INTO hygiene_records 
(elderly_id, record_date, diaper_count, diaper_pad_count, bathing, oral_care, hair_wash, 
 other_activities, notes, image_path, recorded_by, recorded_by_name) 
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
";

echo "\n📄 SQL Statement:\n$insert_sql\n";

$insert_stmt = $conn->prepare($insert_sql);

if (!$insert_stmt) {
    die("❌ เตรียม statement ไม่ได้: " . $conn->error . "\n");
}
echo "✅ เตรียม statement สำเร็จ\n";

// ตรวจสอบจำนวน parameters
echo "\n🔢 จำนวน parameters ที่ต้องการ: 12\n";
echo "Parameter types: isiissssssis\n";

// Bind parameters
$image_path_string = null;
$bind_result = $insert_stmt->bind_param(
    'isiissssssis',
    $elderly_id,              // int -> i
    $record_date,             // string (date) -> s
    $diaper_count,            // int -> i
    $diaper_pad_count,        // int -> i
    $bathing,                 // string (enum) -> s
    $oral_care,               // string (enum) -> s
    $hair_wash,               // string (enum) -> s
    $other_activities_json,   // string (text) -> s
    $notes,                   // string (text) -> s
    $image_path_string,       // string (varchar) -> s
    $_SESSION['user_id'],     // int -> i
    $_SESSION['username']     // string (varchar) -> s
);

if (!$bind_result) {
    die("❌ bind_param ไม่สำเร็จ: " . $insert_stmt->error . "\n");
}
echo "✅ bind_param สำเร็จ\n";

// Execute
$execute_result = $insert_stmt->execute();

if (!$execute_result) {
    die("❌ execute ไม่สำเร็จ: " . $insert_stmt->error . "\n");
}

$new_record_id = $conn->insert_id;
echo "✅ บันทึกข้อมูลสำเร็จ! ID: $new_record_id\n";

// ตรวจสอบข้อมูลที่บันทึก
$check_sql = "SELECT * FROM hygiene_records WHERE id = ?";
$check_stmt = $conn->prepare($check_sql);
$check_stmt->bind_param('i', $new_record_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
$saved_data = $check_result->fetch_assoc();

echo "\n📋 ข้อมูลที่บันทึกแล้ว:\n";
print_r($saved_data);

$conn->close();
echo "\n🎉 การทดสอบเสร็จสิ้น\n";
?>