<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Weight Height Modal -->
<div id="weightHeightModal" class="modal" style="display: none;">
    <div class="modal-content weight-height-modal">
        <div class="modal-header">
            <h3 id="weight-height-modal-title">น้ำหนักส่วนสูง</h3>
            <span class="close" onclick="closeWeightHeightModal()">&times;</span>
        </div>
        
        <form id="weightHeightForm" class="weight-height-form">
            <input type="hidden" name="elderly_id" value="<?php echo isset($elderly['id']) ? $elderly['id'] : ''; ?>">
            <input type="hidden" id="weight_height_record_id" name="record_id" value="">
            <input type="hidden" id="weight_height_form_mode" name="form_mode" value="create">
            <input type="hidden" name="care_type" value="weight_height">
            
            <div class="form-row">
                <div class="form-group date-group">
                    <label for="weight_height_recorded_date">
                        <i class="calendar-icon">📅</i>
                        วันที่บันทึก <span class="required">*</span>
                    </label>
                    <input type="date" id="weight_height_recorded_date" name="measurement_date" 
                           value="<?php echo date('Y-m-d'); ?>" required>
                </div>
            </div>

            <div class="form-notice">
                <p><span class="required">*</span> วันที่ น้ำหนัก และส่วนสูงจำเป็นต้องกรอก</p>
                <p>⚖️ การติดตามน้ำหนักส่วนสูงเพื่อประเมินสุขภาพโดยรวม</p>
            </div>

            <!-- แสดงข้อมูลการบันทึกเมื่อเป็นโหมดแก้ไข -->
            <div id="weight-height-edit-info" class="edit-info" style="display: none;">
                <div class="info-card">
                    <h4>📝 ข้อมูลการบันทึก</h4>
                    <p><strong>ผู้บันทึก:</strong> <span id="weight_height_recorded_by_display"></span></p>
                    <p><strong>เวลาที่บันทึก:</strong> <span id="weight_height_created_at_display"></span></p>
                </div>
            </div>

            <div class="weight-height-grid">
                <div class="weight-height-item">
                    <label for="weight">
                        <i class="weight-height-icon">⚖️</i>
                        <span class="weight-height-label">น้ำหนัก <span class="required">*</span></span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="weight" name="weight" 
                               step="0.1" min="0" max="300" placeholder="65.5" required>
                        <span class="input-unit">กก.</span>
                    </div>
                </div>

                <div class="weight-height-item">
                    <label for="height">
                        <i class="weight-height-icon">📐</i>
                        <span class="weight-height-label">ส่วนสูง <span class="required">*</span></span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="height" name="height" 
                               step="0.1" min="0" max="300" placeholder="165.5" required>
                        <span class="input-unit">ซม.</span>
                    </div>
                </div>

                <div class="weight-height-item">
                    <label for="bmi">
                        <i class="weight-height-icon">📊</i>
                        <span class="weight-height-label">ดัชนีมวลกาย (BMI)</span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="bmi" name="bmi" 
                               step="0.01" min="0" max="100" placeholder="24.5" readonly>
                        <span class="input-unit">kg/m²</span>
                    </div>
                </div>

                <div class="weight-height-item">
                    <label for="measurement_method">
                        <i class="weight-height-icon">📏</i>
                        <span class="weight-height-label">วิธีการวัด</span>
                    </label>
                    <select id="measurement_method" name="measurement_method">
                        <option value="">เลือกวิธีการวัด</option>
                        <option value="standing_scale">เครื่องชั่งยืน</option>
                        <option value="bed_scale">เครื่องชั่งเตียง</option>
                        <option value="wheelchair_scale">เครื่องชั่งรถเข็น</option>
                        <option value="estimated">ประมาณการ</option>
                        <option value="previous_record">จากบันทึกก่อนหน้า</option>
                    </select>
                </div>
            </div>

            <div class="form-group notes-group">
                <label for="weight_height_notes">
                    <i class="notes-icon">📝</i>
                    หมายเหตุเพิ่มเติม
                </label>
                <textarea id="weight_height_notes" name="notes" 
                         rows="4" placeholder="บันทึกรายละเอียดเพิ่มเติมเกี่ยวกับการวัดน้ำหนักส่วนสูง..."></textarea>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeWeightHeightModal()">
                    ยกเลิก
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="save-icon">💾</i>
                    บันทึก
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Weight Height Modal Styles */
.weight-height-modal {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.weight-height-form {
    padding: 25px;
}

.weight-height-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.weight-height-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.weight-height-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-green);
}

.weight-height-item.has-data {
    border-color: var(--primary-green);
    background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
}

.weight-height-item.has-data .weight-height-icon {
    color: var(--primary-green);
}

.weight-height-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.weight-height-icon {
    font-size: 1.3rem;
    width: 30px;
    text-align: center;
}

.weight-height-label {
    font-size: 0.95rem;
}

.weight-height-item input[type="number"],
.weight-height-item select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    transition: border-color 0.3s ease;
    background: white;
}

.weight-height-item input[type="number"]:focus,
.weight-height-item select:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(41, 163, 126, 0.1);
}

.input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-group input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(41, 163, 126, 0.1);
}

.input-group input::placeholder {
    color: #bbb;
    opacity: 0.6;
}

.input-unit {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 40px;
}

/* Responsive */
@media (max-width: 768px) {
    .weight-height-modal {
        width: 95%;
        margin: 10px;
    }
    
    .weight-height-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}
</style>

<script>
function openWeightHeightModal() {
    // เปิด modal น้ำหนักส่วนสูง
    const elderlyId = document.querySelector('input[name="elderly_id"]').value;
    
    if (!elderlyId) {
        alert('ไม่พบรหัสผู้สูงอายุ');
        return;
    }

    // แสดง loading
    document.getElementById('weight-height-modal-title').textContent = 'กำลังตรวจสอบข้อมูล...';
    document.getElementById('weightHeightModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // ตรวจสอบว่าวันนี้มีข้อมูลแล้วหรือไม่
    checkTodayWeightHeightRecord(elderlyId);
}

function checkTodayWeightHeightRecord(elderlyId) {
    // ใช้ API ง่ายๆ ที่ไม่ต้องเช็ค session ซับซ้อน
    fetch(`api/check_today_weight_height_simple.php?elderly_id=${elderlyId}`, {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            console.log(`HTTP error! status: ${response.status}, using create mode`);
            setupWeightHeightCreateMode();
            return null;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return; // ถ้า response error แล้ว skip
        
        console.log('Check today weight height response:', data);
        if (data.success) {
            if (data.has_today_record) {
                // โหมดแก้ไข - มีข้อมูลวันนี้แล้ว
                setupWeightHeightEditMode(data.data);
            } else {
                // โหมดสร้างใหม่ - ยังไม่มีข้อมูลวันนี้
                setupWeightHeightCreateMode();
            }
        } else {
            console.log('API returned error, using create mode:', data.message);
            // ถ้า API error ให้ใช้โหมดสร้างใหม่
            setupWeightHeightCreateMode();
        }
    })
    .catch(error => {
        console.log('Error checking today weight height data, using create mode:', error.message);
        // ถ้าเกิด error ให้ใช้โหมดสร้างใหม่
        setupWeightHeightCreateMode();
    });
}

function setupWeightHeightCreateMode() {
    document.getElementById('weight-height-modal-title').textContent = '⚖️ บันทึกน้ำหนักส่วนสูงใหม่';
    document.getElementById('weight_height_form_mode').value = 'create';
    document.getElementById('weight_height_record_id').value = '';
    document.getElementById('weight-height-edit-info').style.display = 'none';
    
    // รีเซ็ตฟอร์มเป็นค่าเริ่มต้น
    document.getElementById('weightHeightForm').reset();
    document.getElementById('weight_height_recorded_date').value = new Date().toISOString().split('T')[0];
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#weightHeightForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">💾</i> บันทึกใหม่';
    
    // แสดงข้อความบอกว่ายังไม่มีข้อมูลวันนี้
    showWeightHeightCreateModeMessage();
    
    console.log('Setup weight height create mode completed');
}

function setupWeightHeightEditMode(data) {
    document.getElementById('weight-height-modal-title').textContent = '✏️ แก้ไขน้ำหนักส่วนสูงวันนี้';
    document.getElementById('weight_height_form_mode').value = 'edit';
    document.getElementById('weight_height_record_id').value = data.id;
    
    // แสดงข้อมูลผู้บันทึก
    document.getElementById('weight_height_recorded_by_display').textContent = data.recorded_by_name;
    document.getElementById('weight_height_created_at_display').textContent = 
        new Date(data.created_at).toLocaleString('th-TH');
    document.getElementById('weight-height-edit-info').style.display = 'block';
    
    // เติมข้อมูลในฟอร์ม
    document.getElementById('weight_height_recorded_date').value = data.measurement_date;
    document.getElementById('weight').value = data.weight || '';
    document.getElementById('height').value = data.height || '';
    document.getElementById('bmi').value = data.bmi || '';
    document.getElementById('measurement_method').value = data.measurement_method || '';
    document.getElementById('weight_height_notes').value = data.notes || '';
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#weightHeightForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">✏️</i> อัปเดต';
    
    // แสดงข้อความแจ้งว่ามีข้อมูลอยู่แล้วและสามารถแก้ไขได้
    showWeightHeightEditModeMessage(data);
    
    // อัปเดต visual feedback
    setTimeout(updateWeightHeightItemVisual, 100);
    
    console.log('Setup weight height edit mode completed for record ID:', data.id);
}

function showWeightHeightCreateModeMessage() {
    const formNotice = document.querySelector('#weightHeightModal .form-notice');
    if (formNotice) {
        const today = new Date().toLocaleDateString('th-TH');
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #fff3cd 0%, #fdeaa7 100%); border: 1px solid #f0c674; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #856404; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #856404;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #856404;"><strong>สถานะ:</strong> ❌ ยังไม่มีการบันทึกน้ำหนักส่วนสูง</p>
                <p style="margin: 5px 0; color: #856404;"><strong>การดำเนินการ:</strong> 📝 ต้องการบันทึกข้อมูลใหม่หรือไม่?</p>
            </div>
            <p><span class="required">*</span> วันที่ น้ำหนัก และส่วนสูงจำเป็นต้องกรอก</p>
            <p>⚖️ การติดตามน้ำหนักส่วนสูงเพื่อประเมินสุขภาพโดยรวม</p>
        `;
    }
}

function showWeightHeightEditModeMessage(data) {
    const formNotice = document.querySelector('#weightHeightModal .form-notice');
    if (formNotice) {
        const recordTime = new Date(data.measurement_date).toLocaleString('th-TH');
        const today = new Date().toLocaleDateString('th-TH');
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #155724; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #155724;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>สถานะ:</strong> ✅ มีการบันทึกน้ำหนักส่วนสูงแล้ว</p>
                <p style="margin: 5px 0; color: #155724;"><strong>เวลาที่บันทึก:</strong> ${recordTime}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>การดำเนินการ:</strong> ✏️ ต้องการแก้ไขข้อมูลหรือไม่?</p>
            </div>
            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #495057;">📊 รายงานน้ำหนักส่วนสูงปัจจุบัน</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    ${data.weight ? `<p style="margin: 2px 0; color: #495057;"><strong>⚖️ น้ำหนัก:</strong> ${data.weight} กก.</p>` : ''}
                    ${data.height ? `<p style="margin: 2px 0; color: #495057;"><strong>📐 ส่วนสูง:</strong> ${data.height} ซม.</p>` : ''}
                    ${data.bmi ? `<p style="margin: 2px 0; color: #495057;"><strong>📊 BMI:</strong> ${data.bmi} kg/m²</p>` : ''}
                    ${data.measurement_method ? `<p style="margin: 2px 0; color: #495057;"><strong>📏 วิธีวัด:</strong> ${data.measurement_method}</p>` : ''}
                </div>
                ${data.notes ? `<p style="margin: 10px 0 5px 0; color: #495057;"><strong>📝 หมายเหตุ:</strong> ${data.notes}</p>` : ''}
            </div>
            <p><span class="required">*</span> วันที่ น้ำหนัก และส่วนสูงจำเป็นต้องกรอก</p>
            <p>⚖️ การติดตามน้ำหนักส่วนสูงเพื่อประเมินสุขภาพโดยรวม</p>
        `;
    }
}

function closeWeightHeightModal() {
    document.getElementById('weightHeightModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('weightHeightForm').reset();
}

// Close modal when clicking outside
document.getElementById('weightHeightModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeWeightHeightModal();
    }
});

// Form validation
function validateWeightHeightForm() {
    // ตรวจสอบวันที่บันทึก
    const dateField = document.getElementById('weight_height_recorded_date');
    if (!dateField || !dateField.value.trim()) {
        alert('กรุณาเลือกวันที่บันทึก');
        if (dateField) dateField.focus();
        return false;
    }
    
    // ตรวจสอบน้ำหนัก
    const weightField = document.getElementById('weight');
    if (!weightField || !weightField.value.trim()) {
        alert('กรุณากรอกน้ำหนัก');
        if (weightField) weightField.focus();
        return false;
    }
    
    const weight = parseFloat(weightField.value);
    if (isNaN(weight) || weight <= 0 || weight > 300) {
        alert('กรุณากรอกน้ำหนักที่ถูกต้อง (1-300 กิโลกรัม)');
        weightField.focus();
        return false;
    }
    
    // ตรวจสอบส่วนสูง
    const heightField = document.getElementById('height');
    if (!heightField || !heightField.value.trim()) {
        alert('กรุณากรอกส่วนสูง');
        if (heightField) heightField.focus();
        return false;
    }
    
    const height = parseFloat(heightField.value);
    if (isNaN(height) || height <= 0 || height > 300) {
        alert('กรุณากรอกส่วนสูงที่ถูกต้อง (1-300 เซนติเมตร)');
        heightField.focus();
        return false;
    }
    
    return true;
}

// Auto calculate BMI when weight or height changes
function calculateBMI() {
    const weight = parseFloat(document.getElementById('weight').value);
    const height = parseFloat(document.getElementById('height').value);
    
    if (weight > 0 && height > 0) {
        const heightM = height / 100;
        const bmi = (weight / (heightM * heightM)).toFixed(2);
        document.getElementById('bmi').value = bmi;
    } else {
        document.getElementById('bmi').value = '';
    }
}

// Add event listeners to weight and height fields
document.getElementById('weight').addEventListener('input', calculateBMI);
document.getElementById('height').addEventListener('input', calculateBMI);

// Form submission
document.getElementById('weightHeightForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate form first
    if (!validateWeightHeightForm()) {
        return;
    }
    
    const formData = new FormData(this);
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="save-icon">⏳</i> กำลังบันทึก...';
    submitBtn.disabled = true;
    
    // เรียก API สำหรับบันทึกน้ำหนักส่วนสูง
    fetch('api/save_weight_height_simple.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
            'Cache-Control': 'no-cache'
        }
    })
    .then(response => {
        console.log('Save weight height response status:', response.status);
        
        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Session expired. Please refresh the page and login again.');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Save weight height response data:', data);
        
        if (data.success) {
            alert('บันทึกข้อมูลน้ำหนักส่วนสูงเรียบร้อยแล้ว');
            closeWeightHeightModal();
            // Update display with new data
            loadLatestWeightHeightRecord();
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
            console.error('API Error:', data);
        }
    })
    .catch(error => {
        console.error('Fetch Error:', error);
        let errorMessage = 'เกิดข้อผิดพลาดในการบันทึกข้อมูล\n\n';
        
        if (error.message.includes('Session expired') || error.message.includes('status: 401')) {
            errorMessage += 'สาเหตุ: Session หมดอายุหรือไม่ได้เข้าสู่ระบบ\n';
            errorMessage += 'วิธีแก้ไข: กรุณาเข้าสู่ระบบใหม่';
            alert(errorMessage);
            window.location.href = 'index.php';
        } else if (error.message.includes('status: 500')) {
            errorMessage += 'สาเหตุ: ข้อผิดพลาดภายในระบบ\n';
            errorMessage += 'วิธีแก้ไข:\n';
            errorMessage += '1. ตรวจสอบว่าได้เข้าสู่ระบบแล้ว\n';
            errorMessage += '2. ตรวจสอบสิทธิ์การเข้าถึง\n';
            errorMessage += '3. ตรวจสอบข้อมูลที่กรอก\n';
            errorMessage += '4. ลองรีเฟรชหน้าเว็บ';
            alert(errorMessage);
        } else {
            errorMessage += 'รายละเอียด: ' + error.message + '\n';
            errorMessage += 'วิธีแก้ไข: ลองใหม่อีกครั้งหรือติดต่อผู้ดูแลระบบ';
            alert(errorMessage);
        }
    })
    .finally(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Real-time visual feedback for form inputs
function updateWeightHeightItemVisual() {
    const weightHeightFields = ['weight', 'height', 'measurement_method'];
    
    weightHeightFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            const weightHeightItem = field.closest('.weight-height-item');
            if (field.value.trim()) {
                weightHeightItem.classList.add('has-data');
            } else {
                weightHeightItem.classList.remove('has-data');
            }
        }
    });
}

// Add input event listeners for real-time feedback
function addWeightHeightInputListeners() {
    const weightHeightFields = ['weight', 'height', 'measurement_method'];
    
    weightHeightFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('change', updateWeightHeightItemVisual);
            field.addEventListener('input', updateWeightHeightItemVisual);
        }
    });
}

// Function to load latest weight height record (placeholder)
function loadLatestWeightHeightRecord() {
    console.log('Loading latest weight height record...');
    // TODO: Implement loading latest weight height record display
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    addWeightHeightInputListeners();
});
</script>