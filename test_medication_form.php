<?php
// Test script for medication form functionality
define('AIVORA_SECURITY', true);

// Start session properly
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set complete test user session with all required fields
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'test_user';
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['facility_name'] = 'Test Facility';
    $_SESSION['last_activity'] = time(); // Important for session validation
    $_SESSION['LAST_REGENERATE'] = time(); // For session regeneration check
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Test Medication Form</title>
    <meta charset='UTF-8'>
    <style>
        body { font-family: 'Sarabun', sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 4px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        button { padding: 10px 20px; background: #9C27B0; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #7B1FA2; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>💊 ทดสอบระบบบันทึกการให้ยา</h1>
        
        <div class='test-section'>
            <h2>1. ตรวจสอบ Session</h2>
            <pre>";
print_r($_SESSION);
echo "</pre>
        </div>
        
        <div class='test-section'>
            <h2>2. ตรวจสอบการเชื่อมต่อฐานข้อมูล</h2>";

require_once 'config/database.php';
if ($conn && !$conn->connect_error) {
    echo "<p class='success'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} else {
    echo "<p class='error'>✗ ไม่สามารถเชื่อมต่อฐานข้อมูล</p>";
}

echo "</div>
        
        <div class='test-section'>
            <h2>3. ตรวจสอบตาราง medication_records</h2>";

$check = $conn->query("SHOW TABLES LIKE 'medication_records'");
if ($check && $check->num_rows > 0) {
    echo "<p class='success'>✓ ตาราง medication_records มีอยู่</p>";
    
    $count = $conn->query("SELECT COUNT(*) as total FROM medication_records");
    if ($count) {
        $total = $count->fetch_assoc()['total'];
        echo "<p class='info'>จำนวนข้อมูลในตาราง: $total รายการ</p>";
    }
} else {
    echo "<p class='error'>✗ ไม่พบตาราง medication_records</p>";
}

echo "</div>
        
        <div class='test-section'>
            <h2>4. ทดสอบบันทึกข้อมูลการให้ยา</h2>
            <button onclick='testSaveAPI()'>ทดสอบบันทึก API</button>
            <button onclick='testGetAPI()'>ทดสอบดึงข้อมูล API</button>
            <div id='test-result'></div>
        </div>
        
        <div class='test-section'>
            <h2>5. ทดสอบฟอร์มจริง</h2>
            <p>เปิดหน้า elderly_detail.php และคลิกที่ไอคอน 💊 บันทึกการให้ยา</p>
            <a href='index.php?page=elderly_detail&id=1' target='_blank'>
                <button>เปิดหน้า Elderly Detail (ID=1)</button>
            </a>
        </div>
    </div>
    
    <script>
    function testSaveAPI() {
        const formData = new FormData();
        formData.append('elderly_id', '1');
        formData.append('medication_date', new Date().toISOString().split('T')[0]);
        formData.append('medication_name', 'ยาทดสอบ');
        formData.append('medication_dosage', '500mg');
        formData.append('medication_route', 'oral');
        formData.append('medication_notes', 'ทดสอบระบบ');
        formData.append('given_by', 'Test User');
        formData.append('time_periods[]', 'morning');
        formData.append('morning_time', '08:00');
        
        fetch('api/save_medication.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            const resultDiv = document.getElementById('test-result');
            if (data.success) {
                resultDiv.innerHTML = '<p class=\"success\">✓ บันทึกข้อมูลสำเร็จ</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } else {
                resultDiv.innerHTML = '<p class=\"error\">✗ เกิดข้อผิดพลาด: ' + data.message + '</p>';
            }
        })
        .catch(error => {
            document.getElementById('test-result').innerHTML = '<p class=\"error\">✗ Error: ' + error + '</p>';
        });
    }
    
    function testGetAPI() {
        const today = new Date().toISOString().split('T')[0];
        fetch('api/get_today_medications.php?elderly_id=1&date=' + today, {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            const resultDiv = document.getElementById('test-result');
            if (data.success) {
                resultDiv.innerHTML = '<p class=\"success\">✓ ดึงข้อมูลสำเร็จ</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } else {
                resultDiv.innerHTML = '<p class=\"error\">✗ เกิดข้อผิดพลาด: ' + data.message + '</p>';
            }
        })
        .catch(error => {
            document.getElementById('test-result').innerHTML = '<p class=\"error\">✗ Error: ' + error + '</p>';
        });
    }
    </script>
</body>
</html>";

$conn->close();
?>