<?php
require_once 'config/database.php';

echo "Adding elderly_id column to vital_signs table...\n";

// Add elderly_id column first
$sql = "ALTER TABLE vital_signs ADD COLUMN elderly_id INT NULL AFTER care_record_id";
if ($conn->query($sql) === TRUE) {
    echo "Column elderly_id added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column elderly_id already exists\n";
    } else {
        echo "Error adding elderly_id: " . $conn->error . "\n";
    }
}

// Now add facility_id 
echo "Adding facility_id column...\n";
$sql2 = "ALTER TABLE vital_signs ADD COLUMN facility_id INT NULL AFTER elderly_id";
if ($conn->query($sql2) === TRUE) {
    echo "Column facility_id added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column facility_id already exists\n";
    } else {
        echo "Error adding facility_id: " . $conn->error . "\n";
    }
}

// Verify the table structure
echo "\nFinal vital_signs table structure:\n";
$result = $conn->query("DESCRIBE vital_signs");
while($row = $result->fetch_assoc()) {
    echo $row['Field'] . " - " . $row['Type'] . "\n";
}
?>