<?php
// get_today_medications.php - API สำหรับดึงข้อมูลการให้ยาของวันนี้

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

header('Content-Type: application/json; charset=utf-8');

// Helper function - define early
function formatDateThai($date) {
    $months = [
        '01' => 'มกราคม', '02' => 'กุมภาพันธ์', '03' => 'มีนาคม',
        '04' => 'เมษายน', '05' => 'พฤษภาคม', '06' => 'มิถุนายน',
        '07' => 'กรกฎาคม', '08' => 'สิงหาคม', '09' => 'กันยายน',
        '10' => 'ตุลาคม', '11' => 'พฤศจิกายน', '12' => 'ธันวาคม'
    ];
    
    $parts = explode('-', $date);
    if (count($parts) === 3) {
        $day = (int)$parts[2];
        $month = $months[$parts[1]] ?? $parts[1];
        $year = (int)$parts[0] + 543; // Convert to Buddhist year
        return "$day $month $year";
    }
    
    return $date;
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตรวจสอบการเข้าสู่ระบบ - แต่ไม่บังคับสำหรับ GET request
$is_authenticated = isset($_SESSION['user_id']) && isset($_SESSION['user_role']);

if (!$is_authenticated) {
    // ส่ง empty response แทนที่จะ error
    echo json_encode([
        'success' => true,
        'data' => [
            'date' => date('Y-m-d'),
            'date_thai' => formatDateThai(date('Y-m-d')),
            'medications' => [],
            'summary' => [
                'total_given' => 0,
                'morning' => 0,
                'afternoon' => 0,
                'evening' => 0,
                'bedtime' => 0,
                'as_needed' => 0
            ]
        ],
        'message' => 'ยังไม่มีข้อมูลการให้ยา'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์การเข้าถึง
$allowed_roles = ['admin', 'facility_admin', 'staff'];
if (!in_array($_SESSION['user_role'], $allowed_roles)) {
    // ส่ง empty response แทนที่จะ error
    echo json_encode([
        'success' => true,
        'data' => [
            'date' => date('Y-m-d'),
            'date_thai' => formatDateThai(date('Y-m-d')),
            'medications' => [],
            'summary' => [
                'total_given' => 0,
                'morning' => 0,
                'afternoon' => 0,
                'evening' => 0,
                'bedtime' => 0,
                'as_needed' => 0
            ]
        ],
        'message' => 'ยังไม่มีข้อมูลการให้ยา'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

try {
    // รับพารามิเตอร์
    $elderly_id = filter_input(INPUT_GET, 'elderly_id', FILTER_VALIDATE_INT);
    $date = filter_input(INPUT_GET, 'date', FILTER_SANITIZE_STRING) ?: date('Y-m-d');
    
    if (!$elderly_id) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณาระบุ elderly_id'
        ]);
        exit;
    }
    
    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและอยู่ในสถานพยาบาลเดียวกัน
    $elderly_check_sql = "SELECT id, first_name, last_name, facility_id FROM elderly WHERE id = ?";
    $elderly_check_params = [$elderly_id];
    
    if ($_SESSION['user_role'] !== 'admin') {
        $elderly_check_sql .= " AND facility_id = ?";
        $elderly_check_params[] = $_SESSION['facility_id'];
    }
    
    $elderly_check_stmt = $conn->prepare($elderly_check_sql);
    $elderly_check_stmt->bind_param(str_repeat('i', count($elderly_check_params)), ...$elderly_check_params);
    $elderly_check_stmt->execute();
    $elderly_result = $elderly_check_stmt->get_result();
    
    if ($elderly_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือคุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้'
        ]);
        exit;
    }
    
    $elderly_info = $elderly_result->fetch_assoc();
    
    // สร้างตารางถ้ายังไม่มี
    $check_table = $conn->query("SHOW TABLES LIKE 'medication_records'");
    if (!$check_table || $check_table->num_rows === 0) {
        echo json_encode([
            'success' => true,
            'data' => [
                'elderly_info' => $elderly_info,
                'date' => $date,
                'medications' => [],
                'summary' => [
                    'total_given' => 0,
                    'morning' => 0,
                    'afternoon' => 0,
                    'evening' => 0,
                    'bedtime' => 0,
                    'as_needed' => 0
                ]
            ],
            'message' => 'ยังไม่มีข้อมูลการให้ยา'
        ]);
        exit;
    }
    
    // ดึงข้อมูลการให้ยาของวันที่กำหนด
    $medication_sql = "
    SELECT 
        id,
        medication_name,
        medication_dosage,
        medication_route,
        time_period,
        administration_time,
        notes,
        given_by,
        recorded_datetime
    FROM medication_records 
    WHERE elderly_id = ? AND medication_date = ?
    ORDER BY 
        CASE time_period 
            WHEN 'morning' THEN 1
            WHEN 'afternoon' THEN 2  
            WHEN 'evening' THEN 3
            WHEN 'bedtime' THEN 4
            WHEN 'as_needed' THEN 5
            ELSE 6
        END,
        administration_time
    ";
    
    $medication_stmt = $conn->prepare($medication_sql);
    $medication_stmt->bind_param('is', $elderly_id, $date);
    $medication_stmt->execute();
    $medication_result = $medication_stmt->get_result();
    
    $medications = [];
    $summary = [
        'total_given' => 0,
        'morning' => 0,
        'afternoon' => 0,
        'evening' => 0,
        'bedtime' => 0,
        'as_needed' => 0
    ];
    
    while ($row = $medication_result->fetch_assoc()) {
        $medications[] = [
            'id' => $row['id'],
            'name' => $row['medication_name'],
            'dosage' => $row['medication_dosage'],
            'route' => $row['medication_route'],
            'time_period' => $row['time_period'],
            'administration_time' => $row['administration_time'],
            'notes' => $row['notes'],
            'given_by' => $row['given_by'],
            'recorded_datetime' => $row['recorded_datetime'],
            'time_period_thai' => getTimePeriodThai($row['time_period']),
            'route_thai' => getRouteThai($row['medication_route'])
        ];
        
        $summary['total_given']++;
        if (isset($summary[$row['time_period']])) {
            $summary[$row['time_period']]++;
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'elderly_info' => $elderly_info,
            'date' => $date,
            'date_thai' => formatDateThai($date),
            'medications' => $medications,
            'summary' => $summary
        ],
        'message' => $summary['total_given'] > 0 ? 
            "พบข้อมูลการให้ยา {$summary['total_given']} รายการ" : 
            'ยังไม่มีการให้ยาในวันนี้'
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_today_medications.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในการดึงข้อมูล'
    ]);
}

// Helper functions
function getTimePeriodThai($period) {
    $periods = [
        'morning' => 'เช้า',
        'afternoon' => 'กลางวัน',
        'evening' => 'เย็น',
        'bedtime' => 'ก่อนนอน',
        'as_needed' => 'ตามความต้องการ'
    ];
    return $periods[$period] ?? $period;
}

function getRouteThai($route) {
    $routes = [
        'oral' => 'รับประทาน',
        'injection' => 'ฉีด',
        'topical' => 'ทาผิวหนัง',
        'inhaled' => 'สูดดม',
        'sublingual' => 'ใต้ลิ้น',
        'rectal' => 'ทางทวารหนัก',
        'other' => 'อื่นๆ'
    ];
    return $routes[$route] ?? $route;
}

// formatDateThai function already defined at the top

$conn->close();
?>