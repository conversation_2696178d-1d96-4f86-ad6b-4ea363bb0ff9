<?php
// debug_medication_session.php - ดีบัก session สำหรับ medication API

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Debug Session สำหรับ Medication API</h1>\n";
echo "<hr>\n";

// แสดงข้อมูล session ปัจจุบัน
echo "<h2>ข้อมูล Session ปัจจุบัน:</h2>\n";
echo "<pre>";
if (empty($_SESSION)) {
    echo "ไม่มีข้อมูล Session\n";
} else {
    foreach ($_SESSION as $key => $value) {
        echo "$key: " . (is_array($value) ? print_r($value, true) : $value) . "\n";
    }
}
echo "</pre>\n";

// โหลด auth functions และทดสอบ
echo "<h2>ทดสอบ Auth Functions:</h2>\n";
try {
    require_once __DIR__ . '/includes/auth.php';
    
    echo "<p>✓ โหลด auth.php สำเร็จ</p>\n";
    
    // ทดสอบ isLoggedIn
    $isLoggedIn = isLoggedIn();
    echo "<p><strong>isLoggedIn():</strong> " . ($isLoggedIn ? 'true' : 'false') . "</p>\n";
    
    // ทดสอบ SessionManager
    $sessionIsLoggedIn = SessionManager::isLoggedIn();
    echo "<p><strong>SessionManager::isLoggedIn():</strong> " . ($sessionIsLoggedIn ? 'true' : 'false') . "</p>\n";
    
    $sessionExpired = SessionManager::isExpired();
    echo "<p><strong>SessionManager::isExpired():</strong> " . ($sessionExpired ? 'true' : 'false') . "</p>\n";
    
    // ทดสอบ permissions
    if (isset($_SESSION['user_role'])) {
        $hasElderlyPermission = SessionManager::hasPermission('elderly');
        echo "<p><strong>SessionManager::hasPermission('elderly'):</strong> " . ($hasElderlyPermission ? 'true' : 'false') . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาดในการโหลด auth: " . $e->getMessage() . "</p>\n";
}

// สร้าง session ทดสอบถ้าไม่มี
if (empty($_SESSION['user_id'])) {
    echo "<hr>\n";
    echo "<h2>สร้าง Session ทดสอบ</h2>\n";
    
    if (isset($_GET['create_session'])) {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'admin';
        $_SESSION['user_name'] = 'Administrator';
        $_SESSION['user_role'] = 'admin';
        $_SESSION['facility_id'] = 1;
        $_SESSION['facility_name'] = 'Test Facility';
        $_SESSION['last_activity'] = time();
        
        echo "<p style='color: green;'>✓ สร้าง session ทดสอบแล้ว</p>\n";
        echo "<meta http-equiv='refresh' content='1'>\n";
    } else {
        echo "<p><a href='?create_session=1' style='background: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>สร้าง Session ทดสอบ</a></p>\n";
    }
}

// ทดสอบ API medication
if (!empty($_SESSION['user_id'])) {
    echo "<hr>\n";
    echo "<h2>ทดสอบ API Medication</h2>\n";
    
    if (isset($_POST['test_api'])) {
        echo "<h3>ผลการทดสอบ API:</h3>\n";
        
        // เตรียมข้อมูลทดสอบ
        $testData = [
            'elderly_id' => 1,
            'medication_date' => date('Y-m-d'),
            'medication_name' => 'Paracetamol Test',
            'medication_dosage' => '500mg',
            'medication_route' => 'oral',
            'time_periods' => ['morning'],
            'morning_time' => '08:00',
            'medication_notes' => 'ทดสอบ API',
            'given_by' => 'ทดสอบระบบ'
        ];
        
        // เรียก API ด้วย cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/aivora/api/save_medication.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>HTTP Code:</strong> $httpCode</p>\n";
        echo "<p><strong>Response:</strong></p>\n";
        echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
        
        // แสดงข้อมูลที่ส่งไป
        echo "<h4>ข้อมูลที่ส่งไป:</h4>\n";
        echo "<pre>" . htmlspecialchars(print_r($testData, true)) . "</pre>\n";
    } else {
        echo "<form method='POST'>\n";
        echo "<button type='submit' name='test_api' value='1' style='background: #9C27B0; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>ทดสอบ API Medication</button>\n";
        echo "</form>\n";
    }
}

// ตรวจสอบตาราง medication_records
echo "<hr>\n";
echo "<h2>ตรวจสอบฐานข้อมูล:</h2>\n";
try {
    require_once __DIR__ . '/config/database.php';
    
    if ($conn) {
        echo "<p style='color: green;'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
        
        // ตรวจสอบตาราง medication_records
        $check_table = $conn->query("SHOW TABLES LIKE 'medication_records'");
        if ($check_table && $check_table->num_rows > 0) {
            echo "<p style='color: green;'>✓ ตาราง medication_records มีอยู่แล้ว</p>\n";
            
            $count_result = $conn->query("SELECT COUNT(*) as total FROM medication_records");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['total'];
                echo "<p>จำนวนข้อมูลในตาราง: <strong>$count</strong> รายการ</p>\n";
            }
        } else {
            echo "<p style='color: orange;'>! ตาราง medication_records ยังไม่มี</p>\n";
        }
        
        // ตรวจสอบตาราง elderly
        $check_elderly = $conn->query("SELECT COUNT(*) as total FROM elderly");
        if ($check_elderly) {
            $elderly_count = $check_elderly->fetch_assoc()['total'];
            echo "<p>จำนวนผู้สูงอายุ: <strong>$elderly_count</strong> คน</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาดในฐานข้อมูล: " . $e->getMessage() . "</p>\n";
}

// ลิงก์ไปยังหน้าอื่น
echo "<hr>\n";
echo "<div style='margin-top: 20px;'>\n";
echo "<h3>เครื่องมือ:</h3>\n";
echo "<p><a href='check_elderly_data.php'>ตรวจสอบข้อมูลผู้สูงอายุ</a></p>\n";
echo "<p><a href='create_medication_table.php'>สร้างตาราง medication_records</a></p>\n";
echo "<p><a href='test_medication_api.php'>ทดสอบ API แบบเต็ม</a></p>\n";
echo "<p><a href='?page=elderly'>กลับไปหน้าผู้สูงอายุ</a></p>\n";
echo "</div>\n";

// รีเซ็ต session
if (isset($_GET['reset_session'])) {
    session_destroy();
    echo "<script>window.location.href = window.location.pathname;</script>";
}

echo "<hr>\n";
echo "<p><a href='?reset_session=1' style='background: #f44336; color: white; padding: 8px 12px; text-decoration: none; border-radius: 5px;'>รีเซ็ต Session</a></p>\n";
?>