<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
define('AIVORA_SECURITY', true);

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Include session helper
require_once __DIR__ . '/session_helper.php';
require_once __DIR__ . '/../config/database.php';

// Initialize session with same settings as main app
initializeAPISession();

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// ตรวจสอบการเข้าสู่ระบบและสิทธิ์
checkAuthentication();
checkPermissions(['admin', 'facility_admin', 'staff']);

// Get current user info
$currentUser = getCurrentUser();

try {
    // รับข้อมูลจากฟอร์ม
    $elderly_id = isset($_POST['elderly_id']) ? (int)$_POST['elderly_id'] : 0;
    $incident_name = isset($_POST['incident_name']) ? trim($_POST['incident_name']) : '';
    $incident_type = isset($_POST['incident_type']) ? trim($_POST['incident_type']) : '';
    $severity = isset($_POST['severity']) ? trim($_POST['severity']) : 'minor';
    $incident_datetime = isset($_POST['incident_datetime']) ? $_POST['incident_datetime'] : '';
    $incident_location = isset($_POST['incident_location']) ? trim($_POST['incident_location']) : '';
    $incident_description = isset($_POST['incident_description']) ? trim($_POST['incident_description']) : '';
    $additional_description = isset($_POST['additional_description']) ? trim($_POST['additional_description']) : '';
    $immediate_action = isset($_POST['immediate_action']) ? trim($_POST['immediate_action']) : '';
    $improvement_plan = isset($_POST['improvement_plan']) ? trim($_POST['improvement_plan']) : '';
    
    // การแจ้งเตือน
    $notify_manager = isset($_POST['notify_manager']) ? 1 : 0;
    $notify_family = isset($_POST['notify_family']) ? 1 : 0;
    $notify_doctor = isset($_POST['notify_doctor']) ? 1 : 0;
    
    // ตรวจสอบข้อมูลที่จำเป็น
    if (empty($elderly_id) || empty($incident_name) || empty($incident_type) || 
        empty($incident_datetime) || empty($incident_location) || empty($incident_description) || 
        empty($immediate_action)) {
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน']);
        exit();
    }

    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและผู้ใช้มีสิทธิ์เข้าถึง
    $isAdmin = $currentUser['user_role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id, facility_id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล']);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง']);
        exit();
    }

    // ดึง facility_id จากข้อมูลผู้สูงอายุ
    $elderly_data = $result_check->fetch_assoc();
    $facility_id = $elderly_data['facility_id'];
    $stmt_check->close();

    // แปลงรูปแบบวันที่
    $incident_datetime = date('Y-m-d H:i:s', strtotime($incident_datetime));
    
    // สร้าง witnesses JSON (สำหรับอนาคต)
    $witnesses = json_encode([]);
    
    // เตรียมคำสั่ง SQL (ใช้ฟิลด์พื้นฐานที่สุด)
    $sql = "INSERT INTO incident_reports (
        elderly_id, user_id, incident_datetime, incident_type, 
        severity, incident_description, immediate_action_taken, notes
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error);
    }
    
    // รวมข้อมูลทั้งหมดลงใน notes และ description
    $full_description = $incident_description;
    if (!empty($additional_description)) {
        $full_description .= "\n\nคำอธิบายเพิ่มเติม: " . $additional_description;
    }
    if (!empty($incident_location)) {
        $full_description .= "\n\nสถานที่เกิดเหตุ: " . $incident_location;
    }
    
    // เพิ่มข้อมูลการแจ้งเตือน
    $notification_info = [];
    if ($notify_manager) $notification_info[] = "แจ้งผู้จัดการ";
    if ($notify_family) $notification_info[] = "แจ้งญาติ";
    if ($notify_doctor) $notification_info[] = "แจ้งแพทย์";
    
    $notes = "ชื่อเหตุการณ์: " . $incident_name . "\n";
    if (!empty($notification_info)) {
        $notes .= "การแจ้งเตือน: " . implode(", ", $notification_info) . "\n";
    }
    if (!empty($improvement_plan)) {
        $notes .= "แนวทางปรับปรุง: " . $improvement_plan . "\n";
    }
    if (!empty($incident_location)) {
        $notes .= "สถานที่เกิดเหตุ: " . $incident_location . "\n";
    }
    
    $stmt->bind_param("iissssss", 
        $elderly_id, $currentUser['user_id'], $incident_datetime, $incident_type,
        $severity, $full_description, $immediate_action, $notes
    );
    
    if ($stmt->execute()) {
        $incident_id = $conn->insert_id;
        
        // จัดการอัพโหลดรูปภาพ (ถ้ามี)
        $uploaded_images = [];
        if (isset($_FILES['incident_images']) && is_array($_FILES['incident_images']['name'])) {
            $upload_dir = __DIR__ . '/../assets/img/incidents/' . $incident_id . '/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_count = count($_FILES['incident_images']['name']);
            for ($i = 0; $i < min($file_count, 5); $i++) {
                if ($_FILES['incident_images']['error'][$i] === UPLOAD_ERR_OK) {
                    $file_info = pathinfo($_FILES['incident_images']['name'][$i]);
                    $extension = strtolower($file_info['extension']);
                    
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                        $filename = 'incident_' . ($i + 1) . '.' . $extension;
                        $upload_path = $upload_dir . $filename;
                        
                        if (move_uploaded_file($_FILES['incident_images']['tmp_name'][$i], $upload_path)) {
                            $uploaded_images[] = 'assets/img/incidents/' . $incident_id . '/' . $filename;
                        }
                    }
                }
            }
        }
        
        // บันทึกรายชื่อรูปภาพลงฐานข้อมูล (ถ้ามี)
        if (!empty($uploaded_images)) {
            $image_notes = "รูปภาพประกอบ:\n" . implode("\n", $uploaded_images);
            $update_sql = "UPDATE incident_reports SET notes = CONCAT(notes, '\n\n', ?) WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("si", $image_notes, $incident_id);
            $update_stmt->execute();
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'บันทึกรายงานเหตุการณ์เรียบร้อยแล้ว',
            'incident_id' => $incident_id,
            'uploaded_images' => $uploaded_images
        ]);
    } else {
        throw new Exception('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' . $stmt->error);
    }

} catch (Exception $e) {
    error_log("Incident Report Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
}

$conn->close();
?>