<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Sleep Recording Modal -->
<div id="sleepModal" class="modal" style="display: none;">
    <div class="modal-content sleep-modal-content">
        <div class="modal-header">
            <div>
                <h2>💤 บันทึกการนอนหลับ</h2>
                <div id="sleepSummary" class="sleep-summary">
                    <span id="sleepSummaryText">กำลังโหลด...</span>
                </div>
            </div>
            <span class="close" onclick="closeSleepModal()">&times;</span>
        </div>
        
        <!-- Today's Sleep Record Section -->
        <div id="todaySleepSection" class="today-sleep-section" style="display: none;">
            <h3>📅 การบันทึกวันนี้</h3>
            <div id="todaySleepRecord" class="today-sleep-record">
                <!-- จะถูกโหลดด้วย JavaScript -->
            </div>
        </div>
        
        <!-- Previous Sleep Records Section -->
        <div id="previousSleepSection" class="previous-sleep-section" style="display: none;">
            <h3>📝 การบันทึกก่อนหน้า</h3>
            <div id="previousSleepList" class="previous-sleep-list">
                <!-- จะถูกโหลดด้วย JavaScript -->
            </div>
        </div>
        
        <form id="sleepForm" method="POST" enctype="multipart/form-data" onsubmit="return false;">
            <input type="hidden" id="sleep_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="sleep_date">วันที่บันทึก:</label>
                <input type="date" id="sleep_date" name="record_date" required>
            </div>

            <div class="form-group">
                <label for="sleep_time">เวลา (ไม่บังคับ):</label>
                <input type="time" id="sleep_time" name="record_time">
            </div>

            <!-- คุณภาพการนอน -->
            <div class="form-section">
                <h4>🛏️ คุณภาพการนอน</h4>
                
                <div class="form-group">
                    <div class="sleep-quality-grid">
                        <div class="quality-card excellent">
                            <input type="radio" id="quality_good" name="sleep_quality" value="นอนหลับสนิทดี" required>
                            <label for="quality_good" class="quality-label">
                                <div class="quality-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">😴</span>
                                </div>
                                <div class="quality-text">นอนหลับสนิทดี</div>
                            </label>
                        </div>
                        
                        <div class="quality-card poor">
                            <input type="radio" id="quality_light" name="sleep_quality" value="นอนไม่ค่อยหลับ">
                            <label for="quality_light" class="quality-label">
                                <div class="quality-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">😪</span>
                                </div>
                                <div class="quality-text">นอนไม่ค่อยหลับ</div>
                            </label>
                        </div>
                        
                        <div class="quality-card bad">
                            <input type="radio" id="quality_insomnia" name="sleep_quality" value="นอนไม่หลับ">
                            <label for="quality_insomnia" class="quality-label">
                                <div class="quality-icon" style="background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);">
                                    <span class="icon">😵</span>
                                </div>
                                <div class="quality-text">นอนไม่หลับ</div>
                            </label>
                        </div>
                        
                        <div class="quality-card interrupted">
                            <input type="radio" id="quality_interrupted" name="sleep_quality" value="หลับๆตื่นๆ">
                            <label for="quality_interrupted" class="quality-label">
                                <div class="quality-icon" style="background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);">
                                    <span class="icon">🔄</span>
                                </div>
                                <div class="quality-text">หลับๆตื่นๆ</div>
                            </label>
                        </div>
                        
                        <div class="quality-card other">
                            <input type="radio" id="quality_other" name="sleep_quality" value="อื่นๆ">
                            <label for="quality_other" class="quality-label">
                                <div class="quality-icon" style="background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);">
                                    <span class="icon">❓</span>
                                </div>
                                <div class="quality-text">อื่นๆ</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="sleep_notes">คำอธิบายเพิ่มเติม:</label>
                <textarea id="sleep_notes" name="additional_notes" rows="3" placeholder="รายละเอียดเพิ่มเติม เช่น สาเหตุที่นอนไม่หลับ, เวลาที่นอน, การใช้ยานอนหลับ, หรือหมายเหตุอื่นๆ"></textarea>
            </div>

            <div class="form-group">
                <label for="sleep_images">รูปภาพประกอบ (ไม่บังคับ):</label>
                <input type="file" id="sleep_images" name="sleep_images[]" multiple accept="image/*" class="file-input">
                <div class="file-upload-area" onclick="document.getElementById('sleep_images').click()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">
                        <strong>คลิกเพื่อเลือกรูปภาพ</strong><br>
                        <small>รองรับ JPG, PNG, GIF (สูงสุด 5 รูป)</small>
                    </div>
                </div>
                <div id="sleepImagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label for="sleep_recorded_by">ผู้บันทึก:</label>
                <input type="text" id="sleep_recorded_by" name="recorded_by_name" value="<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>" readonly>
            </div>

            <div class="modal-actions">
                <button type="button" onclick="closeSleepModal()" class="btn-cancel">ยกเลิก</button>
                <button type="submit" class="btn-save">บันทึกการนอนหลับ</button>
            </div>
        </form>
    </div>
</div>

<style>
/* Compact modal for sleep */
.sleep-modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 85vh;
    overflow-y: auto;
}

.sleep-modal-content .modal-header h2 {
    font-size: 1.3rem;
    margin: 0;
}

.sleep-summary {
    font-size: 0.85rem;
    margin-top: 5px;
    opacity: 0.9;
}

.today-sleep-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid rgba(33, 150, 243, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
}

.today-sleep-section h3 {
    margin: 0 0 10px 0;
    color: #1976d2;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

.today-sleep-record {
    background: white;
    border: 1px solid #e3f2fd;
    border-radius: 6px;
    padding: 12px;
    font-size: 0.9rem;
}

.previous-sleep-section {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border: 1px solid rgba(156, 39, 176, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
}

.previous-sleep-section h3 {
    margin: 0 0 10px 0;
    color: #7b1fa2;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

.previous-sleep-list {
    display: grid;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.sleep-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.sleep-item:hover {
    border-color: #673AB7;
    box-shadow: 0 2px 8px rgba(103, 58, 183, 0.1);
}

.sleep-quality-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 10px;
    margin-top: 8px;
}

.quality-card {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.quality-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quality-card input[type="radio"] {
    display: none;
}

.quality-card input[type="radio"]:checked + .quality-label {
    background: rgba(103, 58, 183, 0.1);
    border: 1px solid #673AB7;
    border-radius: 4px;
}

.quality-card input[type="radio"]:checked + .quality-label .quality-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.quality-label {
    display: block;
    text-align: center;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quality-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 6px;
    transition: all 0.3s ease;
}

.quality-icon .icon {
    font-size: 1.2rem;
    filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
}

.quality-text {
    font-weight: 500;
    font-size: 0.8rem;
    color: var(--text-primary);
    line-height: 1.1;
}

/* Color coding for different sleep qualities */
.quality-card.excellent .quality-label {
    border-left: 3px solid #4CAF50;
}

.quality-card.poor .quality-label {
    border-left: 3px solid #FF9800;
}

.quality-card.bad .quality-label {
    border-left: 3px solid #F44336;
}

.quality-card.interrupted .quality-label {
    border-left: 3px solid #9C27B0;
}

.quality-card.other .quality-label {
    border-left: 3px solid #607D8B;
}

.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 6px;
}

.file-upload-area:hover {
    border-color: #673AB7;
    background: rgba(103, 58, 183, 0.05);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 6px;
    color: #6c757d;
}

.upload-text {
    color: #6c757d;
}

.upload-text strong {
    color: #673AB7;
}

.file-input {
    display: none;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    margin-top: 10px;
}

.preview-item {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-remove:hover {
    background: #c82333;
}

/* Form sections styling */
.form-section {
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h4 {
    margin: 0 0 8px 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--dark-green);
}

.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-primary);
}

/* Sleep quality status indicators */
.sleep-status-excellent {
    color: #4CAF50;
    font-weight: bold;
}

.sleep-status-poor {
    color: #FF9800;
    font-weight: bold;
}

.sleep-status-bad {
    color: #F44336;
    font-weight: bold;
}

.sleep-status-interrupted {
    color: #9C27B0;
    font-weight: bold;
}

.sleep-status-other {
    color: #607D8B;
    font-weight: bold;
}

@media (max-width: 768px) {
    .sleep-modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .sleep-quality-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .quality-icon {
        width: 30px;
        height: 30px;
    }
    
    .quality-text {
        font-size: 0.75rem;
    }
    
    .image-preview {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }
    
    .preview-item {
        width: 60px;
        height: 60px;
    }
}
</style>

<script>
function openSleepModal() {
    // Get elderly ID from the page context
    const elderlyIdElement = document.querySelector('input[name="elderly_id"]') || 
                             document.querySelector('#sleep_elderly_id') ||
                             document.querySelector('[data-elderly-id]');
    
    let elderlyId = 0;
    if (elderlyIdElement) {
        elderlyId = elderlyIdElement.value || elderlyIdElement.getAttribute('data-elderly-id');
    }
    
    // Try to get from PHP if available
    <?php if (isset($elderly_id) && $elderly_id > 0): ?>
    elderlyId = <?php echo $elderly_id; ?>;
    <?php endif; ?>
    
    elderlyId = parseInt(elderlyId) || 0;
    
    if (!elderlyId) {
        alert('ไม่พบรหัสผู้สูงอายุ');
        return;
    }

    // Set elderly ID in the form
    document.getElementById('sleep_elderly_id').value = elderlyId;
    
    // Reset form
    document.getElementById('sleepForm').reset();
    
    // Set current date and time
    const now = new Date();
    document.getElementById('sleep_date').value = now.toISOString().split('T')[0];
    document.getElementById('sleep_time').value = now.toTimeString().substr(0,5);
    
    // Set recorded by
    document.getElementById('sleep_recorded_by').value = '<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>';
    
    // Show modal
    document.getElementById('sleepModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Load sleep records
    loadSleepRecords(elderlyId);
}

function closeSleepModal() {
    document.getElementById('sleepModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('sleepForm').reset();
    clearSleepImagePreview();
}

function loadSleepRecords(elderlyId) {
    const summaryText = document.getElementById('sleepSummaryText');
    summaryText.textContent = 'กำลังโหลดข้อมูลการนอนหลับ...';
    
    // Load today's record first
    loadTodaySleepRecord(elderlyId);
    
    // Load previous records
    loadPreviousSleepRecords(elderlyId);
}

function loadTodaySleepRecord(elderlyId) {
    const today = new Date().toISOString().split('T')[0];
    
    fetch(`api/get_sleep.php?elderly_id=${elderlyId}&date=${today}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                displayTodaySleepRecord(data.data[0]);
                document.getElementById('todaySleepSection').style.display = 'block';
                updateSleepSummary('มีการบันทึกการนอนหลับวันนี้แล้ว');
            } else {
                document.getElementById('todaySleepSection').style.display = 'none';
                updateSleepSummary('ยังไม่มีการบันทึกการนอนหลับวันนี้');
            }
        })
        .catch(error => {
            console.error('Error loading today sleep record:', error);
            document.getElementById('todaySleepSection').style.display = 'none';
            updateSleepSummary('ยังไม่มีการบันทึกการนอนหลับวันนี้');
        });
}

function loadPreviousSleepRecords(elderlyId) {
    fetch(`api/get_sleep.php?elderly_id=${elderlyId}&limit=5&exclude_today=1`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                displayPreviousSleepRecords(data.data);
                document.getElementById('previousSleepSection').style.display = 'block';
            } else {
                document.getElementById('previousSleepSection').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading previous sleep records:', error);
            document.getElementById('previousSleepSection').style.display = 'none';
        });
}

function displayTodaySleepRecord(record) {
    const container = document.getElementById('todaySleepRecord');
    const recordDate = new Date(record.record_date + (record.record_time ? 'T' + record.record_time : '')).toLocaleString('th-TH');
    
    let statusClass = 'sleep-status-other';
    switch(record.sleep_quality) {
        case 'นอนหลับสนิทดี': statusClass = 'sleep-status-excellent'; break;
        case 'นอนไม่ค่อยหลับ': statusClass = 'sleep-status-poor'; break;
        case 'นอนไม่หลับ': statusClass = 'sleep-status-bad'; break;
        case 'หลับๆตื่นๆ': statusClass = 'sleep-status-interrupted'; break;
    }
    
    container.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <strong>📅 ${recordDate}</strong>
            <span class="${statusClass}">${record.sleep_quality}</span>
        </div>
        ${record.additional_notes ? `<p><strong>หมายเหตุ:</strong> ${record.additional_notes}</p>` : ''}
        <p style="font-size: 0.8rem; color: #6c757d; margin-top: 8px;">
            <strong>ผู้บันทึก:</strong> ${record.recorded_by_name}
        </p>
        <div style="margin-top: 10px;">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editTodaySleep(${record.id})">
                ✏️ แก้ไขข้อมูลวันนี้
            </button>
        </div>
    `;
}

function displayPreviousSleepRecords(records) {
    const container = document.getElementById('previousSleepList');
    container.innerHTML = '';
    
    records.forEach(record => {
        const item = document.createElement('div');
        item.className = 'sleep-item';
        
        const recordDate = new Date(record.record_date + (record.record_time ? 'T' + record.record_time : '')).toLocaleString('th-TH');
        
        let statusClass = 'sleep-status-other';
        switch(record.sleep_quality) {
            case 'นอนหลับสนิทดี': statusClass = 'sleep-status-excellent'; break;
            case 'นอนไม่ค่อยหลับ': statusClass = 'sleep-status-poor'; break;
            case 'นอนไม่หลับ': statusClass = 'sleep-status-bad'; break;
            case 'หลับๆตื่นๆ': statusClass = 'sleep-status-interrupted'; break;
        }
        
        item.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <strong>${recordDate}</strong>
                <span class="${statusClass}">${record.sleep_quality}</span>
            </div>
            ${record.additional_notes ? `<p><strong>หมายเหตุ:</strong> ${record.additional_notes.length > 50 ? record.additional_notes.substring(0, 50) + '...' : record.additional_notes}</p>` : ''}
            <p style="font-size: 0.75rem; color: #6c757d;"><strong>ผู้บันทึก:</strong> ${record.recorded_by_name}</p>
        `;
        
        container.appendChild(item);
    });
}

function updateSleepSummary(message) {
    document.getElementById('sleepSummaryText').textContent = message;
}

function editTodaySleep(sleepId) {
    // This function would handle editing today's sleep record
    alert('ฟังก์ชันแก้ไขข้อมูลการนอนหลับ (ID: ' + sleepId + ') ยังไม่ได้พัฒนา');
}

// Image preview functionality
document.getElementById('sleep_images').addEventListener('change', function(e) {
    const files = e.target.files;
    const preview = document.getElementById('sleepImagePreview');
    
    // Clear previous previews
    clearSleepImagePreview();
    
    // Limit to 5 files
    const maxFiles = Math.min(files.length, 5);
    
    for (let i = 0; i < maxFiles; i++) {
        const file = files[i];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="Preview">
                    <button type="button" class="preview-remove" onclick="removeSleepImagePreview(this, ${i})">&times;</button>
                `;
                preview.appendChild(previewItem);
            };
            
            reader.readAsDataURL(file);
        }
    }
});

function clearSleepImagePreview() {
    document.getElementById('sleepImagePreview').innerHTML = '';
}

function removeSleepImagePreview(button, index) {
    const previewItem = button.parentElement;
    previewItem.remove();
    
    // Update file input by creating new FileList without the removed file
    const fileInput = document.getElementById('sleep_images');
    const dt = new DataTransfer();
    const files = fileInput.files;
    
    for (let i = 0; i < files.length; i++) {
        if (i !== index) {
            dt.items.add(files[i]);
        }
    }
    
    fileInput.files = dt.files;
}

// Form submission
document.getElementById('sleepForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Validate form
    const elderlyId = formData.get('elderly_id');
    const recordDate = formData.get('record_date');
    const sleepQuality = formData.get('sleep_quality');
    
    if (!elderlyId || !recordDate || !sleepQuality) {
        alert('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
        return;
    }
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '⏳ กำลังบันทึก...';
    submitBtn.disabled = true;
    
    // Submit to API
    fetch('api/save_sleep.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('บันทึกข้อมูลการนอนหลับเรียบร้อยแล้ว');
            closeSleepModal();
            // Reload page or update display
            if (typeof loadSleepSummary === 'function') {
                loadSleepSummary();
            }
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('เกิดข้อผิดพลาดในการบันทึกข้อมูล');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Close modal when clicking outside
document.getElementById('sleepModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSleepModal();
    }
});
</script>