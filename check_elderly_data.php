<?php
// check_elderly_data.php - ตรวจสอบข้อมูลผู้สูงอายุ

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

echo "<h1>ตรวจสอบข้อมูลผู้สูงอายุ</h1>\n";

try {
    require_once __DIR__ . '/config/database.php';
    
    if (!$conn) {
        throw new Exception("ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
    }
    
    echo "<p style='color: green;'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
    
    // ตรวจสอบตาราง elderly
    $check_elderly = $conn->query("SHOW TABLES LIKE 'elderly'");
    if ($check_elderly && $check_elderly->num_rows > 0) {
        echo "<p style='color: green;'>✓ ตาราง elderly มีอยู่แล้ว</p>\n";
        
        // นับจำนวนข้อมูล
        $count_result = $conn->query("SELECT COUNT(*) as total FROM elderly");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['total'];
            echo "<p>จำนวนผู้สูงอายุ: <strong>$count</strong> คน</p>\n";
            
            if ($count > 0) {
                // แสดงข้อมูลผู้สูงอายุ
                echo "<h3>รายชื่อผู้สูงอายุ:</h3>\n";
                $elderly_data = $conn->query("
                    SELECT e.id, e.first_name, e.last_name, e.birth_date, f.facility_name 
                    FROM elderly e 
                    LEFT JOIN facilities f ON e.facility_id = f.id 
                    ORDER BY e.id 
                    LIMIT 10
                ");
                
                if ($elderly_data && $elderly_data->num_rows > 0) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
                    echo "<tr><th>ID</th><th>ชื่อ</th><th>นามสกุล</th><th>วันเกิด</th><th>สถานพยาบาล</th><th>การทำงาน</th></tr>\n";
                    
                    while ($row = $elderly_data->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['first_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['last_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['birth_date']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['facility_name'] ?? '-') . "</td>";
                        echo "<td>";
                        echo "<a href='?page=elderly_detail&id=" . $row['id'] . "'>ดูรายละเอียด</a> | ";
                        echo "<a href='test_medication_api.php?elderly_id=" . $row['id'] . "'>ทดสอบบันทึกยา</a>";
                        echo "</td>";
                        echo "</tr>\n";
                    }
                    echo "</table>\n";
                } else {
                    echo "<p>ไม่พบข้อมูลผู้สูงอายุ</p>\n";
                }
            } else {
                echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
                echo "<h3>ไม่มีข้อมูลผู้สูงอายุ</h3>\n";
                echo "<p>กรุณาเพิ่มข้อมูลผู้สูงอายุก่อนทดสอบระบบบันทึกยา</p>\n";
                echo "<p><a href='?page=add_elderly'>เพิ่มข้อมูลผู้สูงอายุ</a></p>\n";
                echo "</div>\n";
            }
        }
    } else {
        echo "<p style='color: red;'>✗ ไม่พบตาราง elderly</p>\n";
    }
    
    // ตรวจสอบตาราง users
    echo "<hr>\n";
    $check_users = $conn->query("SHOW TABLES LIKE 'users'");
    if ($check_users && $check_users->num_rows > 0) {
        echo "<p style='color: green;'>✓ ตาราง users มีอยู่แล้ว</p>\n";
        
        $users_count = $conn->query("SELECT COUNT(*) as total FROM users");
        if ($users_count) {
            $user_total = $users_count->fetch_assoc()['total'];
            echo "<p>จำนวนผู้ใช้ในระบบ: <strong>$user_total</strong> คน</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ ไม่พบตาราง users</p>\n";
    }
    
    // ตรวจสอบตาราง medication_records
    echo "<hr>\n";
    $check_medication = $conn->query("SHOW TABLES LIKE 'medication_records'");
    if ($check_medication && $check_medication->num_rows > 0) {
        echo "<p style='color: green;'>✓ ตาราง medication_records มีอยู่แล้ว</p>\n";
        
        $med_count = $conn->query("SELECT COUNT(*) as total FROM medication_records");
        if ($med_count) {
            $medication_total = $med_count->fetch_assoc()['total'];
            echo "<p>จำนวนข้อมูลการให้ยา: <strong>$medication_total</strong> รายการ</p>\n";
        }
    } else {
        echo "<p style='color: orange;'>! ตาราง medication_records ยังไม่มี - จะถูกสร้างอัตโนมัติเมื่อบันทึกยาครั้งแรก</p>\n";
        echo "<p><a href='create_medication_table.php'>สร้างตารางทันที</a></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<hr>
<div style="margin-top: 20px;">
    <h3>เครื่องมือทดสอบ:</h3>
    <p><a href="test_medication_api.php" style="background: #9C27B0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">ทดสอบ API บันทึกยา</a></p>
    <p><a href="create_medication_table.php" style="background: #2196F3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">สร้างตาราง medication_records</a></p>
    <p><a href="?page=elderly" style="background: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">กลับไปหน้าผู้สูงอายุ</a></p>
</div>