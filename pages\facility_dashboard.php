<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}

// ตรวจสอบสิทธิ์การเข้าถึง
if (!isset($_SESSION['user_role'])) {
    header('Location: ?page=access_denied');
    exit();
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';
global $conn; // เพิ่มบรรทัดนี้เพื่อใช้ global $conn

$facility_id = null;
$facility_name = '';

// สำหรับ Admin, รับ ID จาก GET parameter
if ($_SESSION['user_role'] === 'admin') {
    if (!isset($_GET['id']) || !filter_var($_GET['id'], FILTER_VALIDATE_INT)) {
        // ถ้าไม่มี ID หรือ ID ไม่ถูกต้อง, redirect กลับไปหน้า facilities พร้อมข้อความ error
        $_SESSION['error_message'] = "ไม่พบ ID สถานพยาบาลที่ถูกต้อง";
        header('Location: index.php?page=facilities');
        exit();
    }
    $facility_id = (int)$_GET['id'];

    // ดึงชื่อสถานพยาบาลสำหรับ Admin
    $stmt = $conn->prepare("SELECT facility_name FROM facilities WHERE id = ?");
    $stmt->bind_param('i', $facility_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        $_SESSION['error_message'] = "ไม่พบสถานพยาบาลสำหรับ ID ที่ระบุ";
        header('Location: index.php?page=facilities');
        exit();
    }
    $facility_data = $result->fetch_assoc();
    $facility_name = $facility_data['facility_name'];
    $stmt->close();

} elseif ($_SESSION['user_role'] === 'facility_admin') {
    // สำหรับ Facility Admin, ใช้ ID จาก Session
    $facility_id = $_SESSION['facility_id'];
    $facility_name = $_SESSION['facility_name'];
} else {
    // Role อื่นๆ ไม่มีสิทธิ์เข้าถึง
    header('Location: ?page=access_denied');
    exit();
}

// ตรวจสอบว่า facility_id มีค่าหรือไม่
if (empty($facility_id)) {
    echo "ไม่สามารถระบุสถานพยาบาลได้";
    exit();
}


$user_name = $_SESSION['user_name'] ?? 'ผู้ดูแล';

// Debug information (แสดงเฉพาะในโหมด development)
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_log("Dashboard Debug - User: " . $user_name . ", Facility ID: " . $facility_id . ", Facility Name: " . $facility_name);
}

// ดึงข้อมูลสถิติจากฐานข้อมูลจริง
$stats = [];

// จำนวนผู้สูงอายุในสถานพยาบาล
$sql = "SELECT COUNT(*) as count FROM elderly WHERE facility_id = ? AND status = 'active'";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$stats['elderly_count'] = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

// จำนวนห้องว่างและห้องทั้งหมด
$sql = "SELECT COUNT(*) as total FROM rooms WHERE facility_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$stats['total_rooms'] = $stmt->get_result()->fetch_assoc()['total'];
$stmt->close();

$sql = "SELECT COUNT(*) as available FROM rooms WHERE facility_id = ? AND status = 'available'";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$stats['available_rooms'] = $stmt->get_result()->fetch_assoc()['available'];
$stmt->close();

// จำนวนผู้ใช้งานในสถานพยาบาล (ไม่รวม admin)
$sql = "SELECT COUNT(*) as count FROM users WHERE facility_id = ? AND role != 'admin' AND status = 'active'";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$stats['active_users'] = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

// จำนวนกิจกรรมวันนี้ (ตั้งค่าเป็น 0 ชั่วคราว เนื่องจากไม่มีตาราง schedules)
$stats['activities_today'] = 0;

// จำนวนเหตุการณ์ที่ยังไม่แก้ไข (ตั้งค่าเป็น 0 ชั่วคราว เนื่องจากไม่มีตาราง events)
$stats['pending_events'] = 0;

// === เพิ่มข้อมูลใหม่ตามที่ขอ ===

// 1. Medical Conditions Statistics (โรคประจำตัวของผู้สูงอายุ)
$medical_conditions = [];
$sql = "SELECT medical_conditions, COUNT(*) as count 
        FROM elderly 
        WHERE facility_id = ? AND status = 'active' AND medical_conditions IS NOT NULL AND medical_conditions != ''
        GROUP BY medical_conditions 
        ORDER BY count DESC 
        LIMIT 10";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $medical_conditions[] = $row;
}
$stmt->close();

// 2. Staff Summary (สรุปบุคลากร)
$staff_summary = [];
$sql = "SELECT u.role, u.name, u.username, u.email, u.last_login, u.status, 0 as today_activities
        FROM users u 
        WHERE u.facility_id = ? AND u.role IN ('facility_admin', 'staff') 
        ORDER BY u.role, u.name";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $staff_summary[] = $row;
}
$stmt->close();

// 3. Elderly Summary (สรุปผู้สูงอายุ)
$elderly_summary = [];
$sql = "SELECT CONCAT(e.first_name, ' ', e.last_name) as name, 
               TIMESTAMPDIFF(YEAR, e.birth_date, CURDATE()) as age, 
               e.gender, e.medical_conditions, e.emergency_contact_name,
        r.room_number, e.status, e.admission_date, 0 as recent_events
        FROM elderly e 
        LEFT JOIN rooms r ON e.room_id = r.id
        WHERE e.facility_id = ? 
        ORDER BY e.first_name, e.last_name";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $elderly_summary[] = $row;
}
$stmt->close();

// 4. เพิ่มสถิติรายสัปดาห์และรายเดือน
$sql = "SELECT 
        COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as new_elderly_week,
        COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as new_elderly_month
        FROM elderly WHERE facility_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $facility_id);
$stmt->execute();
$elderly_stats = $stmt->get_result()->fetch_assoc();
$stats = array_merge($stats, $elderly_stats);
$stmt->close();
?>

<div class="container-fluid px-4">
    <!-- Modern Welcome Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="welcome-header-card position-relative overflow-hidden">
                <div class="header-gradient-bg"></div>
                <div class="card-body position-relative z-index-2 p-5">
                    <div class="row align-items-center">
                        <div class="col-lg-8 col-md-7">
                            <div class="welcome-content">
                                <h1 class="display-6 fw-bold text-white mb-3">
                                    <i class="fas fa-hospital me-3 text-white-50"></i>
                                    <?= htmlspecialchars($facility_name) ?>
                                </h1>
                                <p class="lead text-white-75 mb-2">
                                    สวัสดี, <span class="fw-semibold"><?= htmlspecialchars($user_name) ?></span>
                                </p>
                                <div class="d-flex flex-wrap align-items-center gap-4 text-white-50">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar-day me-2"></i>
                                        <span><?= date('d/m/Y') ?></span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-clock me-2"></i>
                                        <span id="current-time"><?= date('H:i:s') ?></span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="status-indicator online me-2"></span>
                                        <span>ระบบทำงานปกติ</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-5 text-end">
                            <div class="welcome-illustration d-none d-md-block">
                                <div class="floating-elements">
                                    <div class="floating-icon floating-icon-1">
                                        <i class="fas fa-user-md"></i>
                                    </div>
                                    <div class="floating-icon floating-icon-2">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <div class="floating-icon floating-icon-3">
                                        <i class="fas fa-stethoscope"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Stats Cards -->
    <div class="row g-4 mb-5">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <a href="?page=elderly" class="text-decoration-none">
                <div class="modern-stat-card elderly-card position-relative overflow-hidden">
                    <div class="card-gradient-bg elderly-gradient"></div>
                    <div class="card-body position-relative z-index-2 p-4">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <div class="stat-content">
                                    <p class="stat-label text-white-75 mb-2 fw-medium">
                                        ผู้สูงอายุทั้งหมด
                                    </p>
                                    <h3 class="stat-number text-white fw-bold mb-1"><?= $stats['elderly_count'] ?></h3>
                                    <small class="stat-change text-white-50">
                                        <i class="fas fa-arrow-up me-1"></i>คน
                                    </small>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="stat-icon-wrapper">
                                    <i class="fas fa-users fa-2x text-white-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-shine"></div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <a href="?page=rooms" class="text-decoration-none">
                <div class="modern-stat-card rooms-card position-relative overflow-hidden">
                    <div class="card-gradient-bg rooms-gradient"></div>
                    <div class="card-body position-relative z-index-2 p-4">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <div class="stat-content">
                                    <p class="stat-label text-white-75 mb-2 fw-medium">
                                        ห้องพักว่าง
                                    </p>
                                    <h3 class="stat-number text-white fw-bold mb-1"><?= $stats['available_rooms'] ?><span class="fs-5 text-white-75">/ <?= $stats['total_rooms'] ?></span></h3>
                                    <small class="stat-change text-white-50">
                                        <i class="fas fa-bed me-1"></i>ห้อง
                                    </small>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="stat-icon-wrapper">
                                    <i class="fas fa-bed fa-2x text-white-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-shine"></div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <a href="?page=facility_users&facility_id=<?= $facility_id ?>" class="text-decoration-none">
                <div class="modern-stat-card users-card position-relative overflow-hidden">
                    <div class="card-gradient-bg users-gradient"></div>
                    <div class="card-body position-relative z-index-2 p-4">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <div class="stat-content">
                                    <p class="stat-label text-white-75 mb-2 fw-medium">
                                        ผู้ใช้งานระบบ
                                    </p>
                                    <h3 class="stat-number text-white fw-bold mb-1"><?= $stats['active_users'] ?></h3>
                                    <small class="stat-change text-white-50">
                                        <i class="fas fa-user-check me-1"></i>คน
                                    </small>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="stat-icon-wrapper">
                                    <i class="fas fa-user-friends fa-2x text-white-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-shine"></div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <a href="?page=schedule" class="text-decoration-none">
                <div class="modern-stat-card activities-card position-relative overflow-hidden">
                    <div class="card-gradient-bg activities-gradient"></div>
                    <div class="card-body position-relative z-index-2 p-4">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <div class="stat-content">
                                    <p class="stat-label text-white-75 mb-2 fw-medium">
                                        กิจกรรมวันนี้
                                    </p>
                                    <h3 class="stat-number text-white fw-bold mb-1"><?= $stats['activities_today'] ?></h3>
                                    <small class="stat-change text-white-50">
                                        <i class="fas fa-calendar-check me-1"></i>กิจกรรม
                                    </small>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="stat-icon-wrapper">
                                    <i class="fas fa-calendar fa-2x text-white-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-shine"></div>
                </div>
            </a>
        </div>
    </div>

    <!-- Medical Conditions Chart & Number of Elderly -->
    <div class="row mb-5">
        <!-- 1. กราฟสรุป Medical Conditions -->
        <div class="col-xl-8 col-lg-7">
            <div class="modern-dashboard-card">
                <div class="card-header-custom">
                    <h5 class="card-title-custom">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        สรุปโรคประจำตัวผู้สูงอายุ
                    </h5>
                    <div class="card-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshMedicalChart()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body-custom p-4">
                    <?php if (empty($medical_conditions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ยังไม่มีข้อมูลโรคประจำตัว</p>
                        </div>
                    <?php else: ?>
                        <div class="chart-container">
                            <canvas id="medicalConditionsChart" width="400" height="200"></canvas>
                        </div>
                        <div class="mt-4">
                            <div class="row">
                                <?php foreach (array_slice($medical_conditions, 0, 6) as $index => $condition): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="condition-item">
                                        <div class="condition-color" style="background-color: <?php echo ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3'][$index % 6]; ?>"></div>
                                        <div class="condition-info">
                                            <strong><?php echo htmlspecialchars($condition['medical_conditions']); ?></strong>
                                            <span class="text-muted"><?php echo $condition['count']; ?> คน</span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 2. Number of Elderly -->
        <div class="col-xl-4 col-lg-5">
            <div class="modern-dashboard-card h-100">
                <div class="card-header-custom">
                    <h5 class="card-title-custom">
                        <i class="fas fa-users me-2 text-success"></i>
                        จำนวนผู้สูงอายุ
                    </h5>
                </div>
                <div class="card-body-custom p-4">
                    <div class="elderly-stats-container">
                        <div class="elderly-total-card text-center mb-4">
                            <div class="elderly-number-display">
                                <h1 class="display-2 fw-bold text-primary mb-0"><?= $stats['elderly_count'] ?></h1>
                                <p class="text-muted mb-0">คน</p>
                            </div>
                        </div>
                        
                        <div class="elderly-breakdown">
                            <div class="breakdown-item">
                                <div class="breakdown-icon bg-success">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="breakdown-info">
                                    <span class="breakdown-label">เพิ่มเข้ามาสัปดาห์นี้</span>
                                    <span class="breakdown-number"><?= $stats['new_elderly_week'] ?> คน</span>
                                </div>
                            </div>
                            
                            <div class="breakdown-item">
                                <div class="breakdown-icon bg-info">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="breakdown-info">
                                    <span class="breakdown-label">เพิ่มเข้ามาเดือนนี้</span>
                                    <span class="breakdown-number"><?= $stats['new_elderly_month'] ?> คน</span>
                                </div>
                            </div>
                            
                            <div class="breakdown-item">
                                <div class="breakdown-icon bg-warning">
                                    <i class="fas fa-bed"></i>
                                </div>
                                <div class="breakdown-info">
                                    <span class="breakdown-label">ห้องว่าง</span>
                                    <span class="breakdown-number"><?= $stats['available_rooms'] ?>/<?= $stats['total_rooms'] ?> ห้อง</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Staff Summary & Elderly Summary -->
    <div class="row mb-5">
        <!-- 3. Summary by Staff List -->
        <div class="col-xl-6 col-lg-6">
            <div class="modern-dashboard-card">
                <div class="card-header-custom">
                    <h5 class="card-title-custom">
                        <i class="fas fa-user-md me-2 text-info"></i>
                        สรุปบุคลากร
                    </h5>
                    <div class="card-actions">
                        <a href="?page=facility_users&facility_id=<?= $facility_id ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-eye me-1"></i>ดูทั้งหมด
                        </a>
                    </div>
                </div>
                <div class="card-body-custom p-0">
                    <?php if (empty($staff_summary)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ยังไม่มีข้อมูลบุคลากร</p>
                        </div>
                    <?php else: ?>
                        <div class="staff-list">
                            <?php foreach (array_slice($staff_summary, 0, 5) as $staff): ?>
                            <div class="staff-item">
                                <div class="staff-avatar">
                                    <div class="avatar-circle <?php echo $staff['role'] === 'facility_admin' ? 'bg-warning' : 'bg-info'; ?>">
                                        <i class="fas <?php echo $staff['role'] === 'facility_admin' ? 'fa-user-tie' : 'fa-user'; ?>"></i>
                                    </div>
                                </div>
                                <div class="staff-info">
                                    <div class="staff-name"><?php echo htmlspecialchars($staff['name']); ?></div>
                                    <div class="staff-details">
                                        <span class="badge <?php echo $staff['role'] === 'facility_admin' ? 'bg-warning' : 'bg-info'; ?>">
                                            <?php echo $staff['role'] === 'facility_admin' ? 'ผู้ดูแล' : 'พนักงาน'; ?>
                                        </span>
                                        <span class="text-muted ms-2">
                                            <i class="fas fa-clock"></i>
                                            <?php echo $staff['last_login'] ? date('d/m/Y', strtotime($staff['last_login'])) : 'ยังไม่เคยเข้า'; ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="staff-stats">
                                    <div class="stat-badge">
                                        <span class="stat-number"><?php echo $staff['today_activities']; ?></span>
                                        <span class="stat-label">กิจกรรมวันนี้</span>
                                    </div>
                                    <div class="status-indicator <?php echo $staff['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>"></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 4. Summary by Elderly List -->
        <div class="col-xl-6 col-lg-6">
            <div class="modern-dashboard-card">
                <div class="card-header-custom">
                    <h5 class="card-title-custom">
                        <i class="fas fa-users me-2 text-success"></i>
                        สรุปผู้สูงอายุ
                    </h5>
                    <div class="card-actions">
                        <a href="?page=elderly" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-eye me-1"></i>ดูทั้งหมด
                        </a>
                    </div>
                </div>
                <div class="card-body-custom p-0">
                    <?php if (empty($elderly_summary)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ยังไม่มีข้อมูลผู้สูงอายุ</p>
                        </div>
                    <?php else: ?>
                        <div class="elderly-list">
                            <?php foreach (array_slice($elderly_summary, 0, 5) as $elderly): ?>
                            <div class="elderly-item">
                                <div class="elderly-avatar">
                                    <div class="avatar-circle <?php echo $elderly['gender'] === 'male' ? 'bg-primary' : 'bg-danger'; ?>">
                                        <i class="fas <?php echo $elderly['gender'] === 'male' ? 'fa-male' : 'fa-female'; ?>"></i>
                                    </div>
                                </div>
                                <div class="elderly-info">
                                    <div class="elderly-name"><?php echo htmlspecialchars($elderly['name']); ?></div>
                                    <div class="elderly-details">
                                        <span class="age-badge"><?php echo $elderly['age']; ?> ปี</span>
                                        <span class="room-badge">
                                            <i class="fas fa-door-open"></i>
                                            <?php echo $elderly['room_number'] ? 'ห้อง ' . $elderly['room_number'] : 'ไม่มีห้อง'; ?>
                                        </span>
                                    </div>
                                    <?php if ($elderly['medical_conditions']): ?>
                                    <div class="medical-condition">
                                        <i class="fas fa-heartbeat text-danger"></i>
                                        <small><?php echo htmlspecialchars($elderly['medical_conditions']); ?></small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="elderly-stats">
                                    <div class="stat-badge">
                                        <span class="stat-number"><?php echo $elderly['recent_events']; ?></span>
                                        <span class="stat-label">เหตุการณ์ 7 วัน</span>
                                    </div>
                                    <div class="status-indicator <?php echo $elderly['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>"></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="quick-actions-container">
                <div class="section-header mb-4">
                    <h4 class="section-title fw-bold mb-2">
                        <i class="fas fa-bolt me-3 text-primary"></i>เมนูด่วน
                    </h4>
                    <p class="section-subtitle text-muted">เข้าถึงฟังก์ชันหลักของระบบได้อย่างรวดเร็ว</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <a href="?page=elderly" class="quick-action-link text-decoration-none">
                            <div class="quick-action-card elderly-action position-relative">
                                <div class="action-bg-pattern"></div>
                                <div class="card-body text-center p-4 position-relative z-index-2">
                                    <div class="action-icon-wrapper mb-3">
                                        <div class="action-icon elderly-icon">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                    </div>
                                    <h6 class="action-title fw-semibold mb-2">จัดการผู้สูงอายุ</h6>
                                    <p class="action-description text-muted small mb-0">เพิ่ม แก้ไข จัดการข้อมูล</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <a href="?page=facility_users&facility_id=<?= $facility_id ?>" class="quick-action-link text-decoration-none">
                            <div class="quick-action-card users-action position-relative">
                                <div class="action-bg-pattern"></div>
                                <div class="card-body text-center p-4 position-relative z-index-2">
                                    <div class="action-icon-wrapper mb-3">
                                        <div class="action-icon users-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                    </div>
                                    <h6 class="action-title fw-semibold mb-2">จัดการผู้ใช้งาน</h6>
                                    <p class="action-description text-muted small mb-0">จัดการบัญชีผู้ใช้</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <a href="?page=rooms" class="quick-action-link text-decoration-none">
                            <div class="quick-action-card rooms-action position-relative">
                                <div class="action-bg-pattern"></div>
                                <div class="card-body text-center p-4 position-relative z-index-2">
                                    <div class="action-icon-wrapper mb-3">
                                        <div class="action-icon rooms-icon">
                                            <i class="fas fa-bed"></i>
                                        </div>
                                    </div>
                                    <h6 class="action-title fw-semibold mb-2">จัดการห้องพัก</h6>
                                    <p class="action-description text-muted small mb-0">จัดการห้องพักและที่พัก</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-xl-3 col-lg-6 col-md-6">
                        <a href="?page=events" class="quick-action-link text-decoration-none">
                            <div class="quick-action-card events-action position-relative">
                                <div class="action-bg-pattern"></div>
                                <div class="card-body text-center p-4 position-relative z-index-2">
                                    <div class="action-icon-wrapper mb-3">
                                        <div class="action-icon events-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                    </div>
                                    <h6 class="action-title fw-semibold mb-2">เหตุการณ์</h6>
                                    <p class="action-description text-muted small mb-0">ติดตามเหตุการณ์</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>กิจกรรมล่าสุด
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <?php
                        // สำหรับตอนนี้ไม่มีตาราง activity_log แสดง empty state
                        $activities = [];
                        
                        if (empty($activities)): ?>
                            <div class="list-group-item text-center text-muted py-4">
                                <i class="fas fa-history fa-2x mb-2"></i>
                                <p class="mb-0">ยังไม่มีกิจกรรมล่าสุด</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($activities as $activity): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between">
                                        <span>
                                            <i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>
                                            <?= htmlspecialchars($activity['details']) ?>
                                            <?php if ($activity['user_name']): ?>
                                                <small class="text-muted">โดย <?= htmlspecialchars($activity['user_name']) ?></small>
                                            <?php endif; ?>
                                        </span>
                                        <small class="text-muted"><?= date('H:i', strtotime($activity['created_at'])) ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>สถานะระบบ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>การใช้งานห้องพัク</span>
                            <span><?= $stats['total_rooms'] > 0 ? round(($stats['total_rooms'] - $stats['available_rooms']) / $stats['total_rooms'] * 100) : 0 ?>%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?= $stats['total_rooms'] > 0 ? round(($stats['total_rooms'] - $stats['available_rooms']) / $stats['total_rooms'] * 100) : 0 ?>%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>เหตุการณ์ที่ต้องจัดการ</span>
                            <span class="badge bg-danger"><?= $stats['pending_events'] ?></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>สถานะเซิร์ฟเวอร์</span>
                            <span class="badge bg-success">Online</span>
                        </div>
                    </div>

                    <div class="text-center">
                        <button class="btn btn-primary btn-sm" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i>รีเฟรชข้อมูล
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern CSS Styles for Facility Dashboard -->
<style>
/* === MODERN WELCOME HEADER === */
.welcome-header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    border: none;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
    position: relative;
}

.header-gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    opacity: 0.9;
}

.welcome-header-card .card-body {
    background: transparent;
    z-index: 10;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.85) !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.7) !important;
}

.z-index-2 {
    z-index: 2;
}

/* Floating Animation Elements */
.floating-elements {
    position: relative;
    height: 200px;
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
}

.floating-icon-1 {
    top: 20px;
    right: 50px;
    animation-delay: -2s;
}

.floating-icon-2 {
    top: 80px;
    right: 20px;
    animation-delay: -4s;
}

.floating-icon-3 {
    top: 140px;
    right: 80px;
    animation-delay: -1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* === MODERN STATS CARDS === */
.modern-stat-card {
    border-radius: 20px;
    border: none;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    backdrop-filter: blur(10px);
    height: 100%;
    min-height: 140px;
    display: flex;
    flex-direction: column;
}

.modern-stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.modern-stat-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.card-gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.elderly-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rooms-gradient {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.users-gradient {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.activities-gradient {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-shine {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%) rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
    pointer-events: none;
    z-index: 1;
}

.modern-stat-card:hover .card-shine {
    animation: shine 0.8s ease-out;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) rotate(45deg);
        opacity: 0;
    }
}

.stat-label {
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
}

.stat-change {
    font-size: 0.75rem;
}

/* === MODERN QUICK ACTIONS === */
.quick-actions-container {
    padding: 0;
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-title {
    color: #2d3748;
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

.section-subtitle {
    color: #718096;
    font-size: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.quick-action-card {
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.quick-action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: transparent;
}

.action-bg-pattern {
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    transition: all 0.3s ease;
}

.quick-action-card:hover .action-bg-pattern {
    transform: scale(1.2);
    opacity: 0.8;
}

.action-icon-wrapper {
    text-align: center;
}

.action-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.elderly-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.users-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

.rooms-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.events-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
}

.quick-action-card:hover .action-icon {
    transform: scale(1.1) rotate(5deg);
}

.action-title {
    color: #2d3748;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.action-description {
    color: #718096;
    font-size: 0.875rem;
}

/* === MODERN ACTIVITIES AND STATUS === */
.modern-card {
    background: #ffffff;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.modern-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header-modern {
    background: transparent;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.card-title-modern {
    color: #2d3748;
    font-size: 1.25rem;
    margin: 0;
}

.card-subtitle-modern {
    color: #718096;
    font-size: 0.875rem;
    margin: 0;
}

.card-footer-modern {
    background: #f7fafc;
    border-top: 1px solid #e2e8f0;
}

/* Activities Timeline */
.activities-timeline {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    transition: all 0.2s ease;
}

.activity-item:hover {
    background-color: #f7fafc;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.activity-text {
    color: #2d3748;
    font-size: 0.9rem;
    line-height: 1.4;
}

.activity-time {
    color: #718096;
    font-size: 0.8rem;
}

.activity-user {
    color: #718096;
    font-size: 0.8rem;
}

/* Empty State */
.empty-state .empty-icon {
    opacity: 0.5;
}

/* Modern Progress Bars */
.modern-progress {
    height: 8px;
    background-color: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-track {
    width: 100%;
    height: 100%;
    background-color: #e2e8f0;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Status Metrics */
.status-item {
    margin-bottom: 1.5rem;
}

.status-metric {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.status-metric:hover {
    background: #edf2f7;
    transform: translateX(4px);
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-title {
    color: #2d3748;
    font-size: 0.9rem;
}

/* Modern Badges */
.badge-warning-modern {
    background: linear-gradient(135deg, #f6ad55, #ed8936);
    color: white;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.badge-success-modern {
    background: linear-gradient(135deg, #68d391, #38b2ac);
    color: white;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

/* Status Indicators */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
}

.status-indicator.online {
    background: #38b2ac;
    box-shadow: 0 0 0 2px rgba(56, 178, 172, 0.3);
    animation: pulse-online 2s infinite;
}

@keyframes pulse-online {
    0% { box-shadow: 0 0 0 2px rgba(56, 178, 172, 0.3); }
    50% { box-shadow: 0 0 0 6px rgba(56, 178, 172, 0.1); }
    100% { box-shadow: 0 0 0 2px rgba(56, 178, 172, 0.3); }
}

/* === MOBILE-FIRST RESPONSIVE DESIGN === */

/* Mobile Base Styles (320px and up) */
.elderly-count-number {
    font-size: 3rem;
    line-height: 1;
}

.chart-container-mobile {
    position: relative;
    height: 250px;
    margin-bottom: 1rem;
}

.condition-item {
    padding: 0.75rem;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.condition-item:hover {
    background: #e9ecef;
    transform: translateX(3px);
}

.condition-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 0.5rem;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.condition-info {
    display: flex;
    flex-direction: column;
}

.condition-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 0.25rem;
}

.condition-count {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Activities Mobile */
.activities-mobile-container {
    max-height: 350px;
    overflow-y: auto;
}

.activity-list {
    padding: 0;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-content {
    display: flex;
    align-items: flex-start;
}

.activity-indicator {
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
    margin-right: 0.75rem;
    margin-top: 0.5rem;
    flex-shrink: 0;
}

.activity-text {
    flex: 1;
}

.activity-description {
    font-size: 0.9rem;
    color: #495057;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6c757d;
}

.activity-user {
    font-weight: 500;
}

.activity-time {
    font-weight: 500;
}

/* System Status Mobile */
.system-status-container {
    padding: 0;
}

.status-item {
    margin-bottom: 1rem;
}

.status-label {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

.status-value {
    font-size: 0.9rem;
    color: #007bff;
    font-weight: 600;
}

.progress-container {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-mobile {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 4px;
    transition: width 0.6s ease;
}

.medical-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

/* Tablet Styles (768px and up) */
@media (min-width: 768px) {
    .elderly-count-number {
        font-size: 3.5rem;
    }
    
    .chart-container-mobile {
        height: 280px;
    }
    
    .condition-color {
        width: 18px;
        height: 18px;
        margin-right: 0.75rem;
    }
    
    .condition-name {
        font-size: 1rem;
    }
    
    .condition-count {
        font-size: 0.85rem;
    }
    
    .activities-mobile-container {
        max-height: 400px;
    }
    
    .activity-description {
        font-size: 1rem;
    }
    
    .activity-meta {
        font-size: 0.85rem;
    }
    
    .medical-text {
        max-width: 200px;
    }
}

/* Desktop Styles (992px and up) */
@media (min-width: 992px) {
    .elderly-count-number {
        font-size: 4rem;
    }
    
    .chart-container-mobile {
        height: 300px;
    }
    
    .condition-color {
        width: 20px;
        height: 20px;
    }
    
    .medical-text {
        max-width: none;
        white-space: normal;
        overflow: visible;
        text-overflow: clip;
    }
}

/* Large Desktop Styles (1200px and up) */
@media (min-width: 1200px) {
    .floating-elements {
        display: block;
    }
    
    .welcome-header-card .row {
        text-align: left;
    }
}

/* === LEGACY RESPONSIVE DESIGN === */
@media (max-width: 1200px) {
    .floating-elements {
        display: none;
    }
    
    .welcome-header-card .row {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .action-icon {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }
    
    .modern-stat-card:hover {
        transform: translateY(-2px) scale(1.01);
    }
    
    .quick-action-card:hover {
        transform: translateY(-1px);
    }
    
    .avatar-circle {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
    
    .breakdown-item {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .breakdown-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.75rem;
    }
    
    .breakdown-label {
        font-size: 0.8rem;
    }
    
    .breakdown-number {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .welcome-header-card .card-body {
        padding: 2rem 1.5rem !important;
    }
    
    .display-6 {
        font-size: 1.25rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .section-header {
        margin-bottom: 1rem;
    }
    
    .action-icon {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }
    
    .elderly-count-number {
        font-size: 2.5rem;
    }
    
    .breakdown-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .breakdown-icon {
        width: 30px;
        height: 30px;
        margin-right: 0.5rem;
        font-size: 0.8rem;
    }
    
    .breakdown-label {
        font-size: 0.75rem;
    }
    
    .breakdown-number {
        font-size: 0.9rem;
    }
    
    .staff-item, .elderly-item {
        padding: 0.75rem 1rem;
    }
    
    .avatar-circle {
        width: 30px;
        height: 30px;
        font-size: 0.7rem;
    }
    
    .staff-name, .elderly-name {
        font-size: 0.9rem;
    }
    
    .stat-badge {
        min-width: 50px;
        padding: 0.25rem;
    }
    
    .stat-badge .stat-number {
        font-size: 1rem;
    }
    
    .stat-badge .stat-label {
        font-size: 0.6rem;
    }
    
    .condition-item {
        padding: 0.5rem;
    }
    
    .condition-color {
        width: 12px;
        height: 12px;
        margin-right: 0.5rem;
    }
    
    .condition-name {
        font-size: 0.8rem;
    }
    
    .condition-count {
        font-size: 0.7rem;
    }
    
    .activity-item {
        padding: 0.75rem;
    }
    
    .activity-indicator {
        width: 6px;
        height: 6px;
        margin-right: 0.5rem;
        margin-top: 0.4rem;
    }
    
    .activity-description {
        font-size: 0.8rem;
    }
    
    .activity-meta {
        font-size: 0.7rem;
    }
}

/* === MOBILE TOUCH OPTIMIZATIONS === */

/* Touch-friendly button sizes */
@media (hover: none) and (pointer: coarse) {
    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        min-height: 44px;
    }
    
    .quick-action-card {
        min-height: 120px;
    }
    
    .modern-stat-card {
        min-height: 120px;
        height: 100%;
    }
    
    /* Disable hover effects on touch devices */
    .modern-stat-card:hover,
    .quick-action-card:hover,
    .condition-item:hover,
    .breakdown-item:hover,
    .staff-item:hover,
    .elderly-item:hover,
    .activity-item:hover {
        transform: none;
        background: inherit;
    }
}

/* Smooth scrolling for mobile */
.activities-mobile-container,
.staff-list,
.elderly-list {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: rgba(0,0,0,0.2) transparent;
}

/* Mobile scrollbar styles */
.activities-mobile-container::-webkit-scrollbar,
.staff-list::-webkit-scrollbar,
.elderly-list::-webkit-scrollbar {
    width: 4px;
}

.activities-mobile-container::-webkit-scrollbar-track,
.staff-list::-webkit-scrollbar-track,
.elderly-list::-webkit-scrollbar-track {
    background: transparent;
}

.activities-mobile-container::-webkit-scrollbar-thumb,
.staff-list::-webkit-scrollbar-thumb,
.elderly-list::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,0.2);
    border-radius: 2px;
}

/* Improve text readability on mobile */
@media (max-width: 480px) {
    .card-title-custom {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.25rem;
    }
    
    body {
        font-size: 14px;
        line-height: 1.5;
    }
    
    /* Ensure proper spacing on very small screens */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .row {
        margin-left: -0.375rem;
        margin-right: -0.375rem;
    }
    
    .row > * {
        padding-left: 0.375rem;
        padding-right: 0.375rem;
    }
}

/* === UTILITY CLASSES === */
.text-decoration-none:hover {
    text-decoration: none !important;
}

.cursor-pointer {
    cursor: pointer;
}

.transition-all {
    transition: all 0.3s ease;
}

/* Mobile-specific utility classes */
@media (max-width: 767px) {
    .text-truncate-mobile {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
    }
    
    .d-mobile-block {
        display: block !important;
    }
    
    .d-mobile-none {
        display: none !important;
    }
}

/* === LEGACY COMPATIBILITY === */
a.card-link {
    text-decoration: none;
}

.card-link .card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-link:hover .card {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* === NEW DASHBOARD COMPONENTS === */

/* Modern Dashboard Cards */
.modern-dashboard-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.modern-dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.card-header-custom {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-bottom: 2px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title-custom {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: #495057;
}

.card-actions {
    margin-left: auto;
}

.card-body-custom {
    padding: 0;
}

/* Medical Conditions Chart */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1rem;
}

.condition-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.condition-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.condition-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 0.75rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.condition-info {
    display: flex;
    flex-direction: column;
}

/* Number of Elderly Display */
.elderly-stats-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.elderly-total-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 1.5rem;
}

.elderly-number-display h1 {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1;
}

.elderly-breakdown {
    flex: 1;
}

.breakdown-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.breakdown-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.breakdown-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1rem;
}

.breakdown-info {
    display: flex;
    flex-direction: column;
}

.breakdown-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.breakdown-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

/* Staff and Elderly Lists */
.staff-list, .elderly-list {
    max-height: 400px;
    overflow-y: auto;
}

.staff-item, .elderly-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.staff-item:hover, .elderly-item:hover {
    background: #f8f9fa;
}

.staff-item:last-child, .elderly-item:last-child {
    border-bottom: none;
}

.staff-avatar, .elderly-avatar {
    margin-right: 1rem;
}

.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.staff-info, .elderly-info {
    flex: 1;
    margin-right: 1rem;
}

.staff-name, .elderly-name {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    color: #495057;
}

.staff-details, .elderly-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.medical-condition {
    margin-top: 0.25rem;
    color: #dc3545;
    font-size: 0.85rem;
}

.age-badge, .room-badge {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
}

.room-badge {
    background: #fff3cd;
    color: #856404;
}

.staff-stats, .elderly-stats {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: 0.5rem;
}

.stat-badge {
    text-align: center;
    padding: 0.5rem;
    background: #e3f2fd;
    border-radius: 8px;
    min-width: 70px;
}

.stat-badge .stat-number {
    display: block;
    font-weight: 600;
    font-size: 1.1rem;
    color: #1976d2;
}

.stat-badge .stat-label {
    display: block;
    font-size: 0.7rem;
    color: #666;
    margin-top: 0.25rem;
    text-transform: none;
    letter-spacing: normal;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.status-active {
    background: #009692;
}

.status-inactive {
    background: #dc3545;
}
</style>

<script>
// Enhanced dashboard JavaScript with modern features
document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced Facility Dashboard loaded');
    
    // Initialize Medical Conditions Chart
    initializeMedicalChart();
    
    // Time update with smooth transitions
    function updateTime() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('th-TH', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            // Only update if time has changed
            if (timeElement.textContent !== timeString) {
                timeElement.style.opacity = '0.7';
                setTimeout(() => {
                    timeElement.textContent = timeString;
                    timeElement.style.opacity = '1';
                }, 100);
            }
        }
    }
    
    // Update time every second
    setInterval(updateTime, 1000);
    
    // Enhanced refresh function with loading state
    window.refreshDashboard = function() {
        const button = event.target;
        const originalText = button.innerHTML;
        
        // Show loading state
        button.innerHTML = '<i class="fas fa-sync-alt fa-spin me-2"></i>กำลังรีเฟรช...';
        button.disabled = true;
        
        // Add a small delay for visual feedback
        setTimeout(() => {
            location.reload();
        }, 500);
    };
    
    // Refresh activities function
    window.refreshActivities = function() {
        const button = event.target;
        const originalHTML = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
        button.disabled = true;
        
        // Simulate refresh (in real implementation, this would be an AJAX call)
        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.disabled = false;
            
            // Show notification
            showNotification('กิจกรรมได้รับการอัพเดทแล้ว', 'success');
        }, 1000);
    };
    
    // Modern notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Add smooth scroll behavior to quick action links
    const quickActionLinks = document.querySelectorAll('.quick-action-link');
    quickActionLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add ripple effect
            const ripple = document.createElement('div');
            ripple.className = 'ripple';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Animate stats cards on load
    const statCards = document.querySelectorAll('.modern-stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
    
    // Animate quick action cards
    const actionCards = document.querySelectorAll('.quick-action-card');
    actionCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, (statCards.length * 150) + (index * 100));
    });
    
    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.modern-card, .status-metric').forEach(el => {
        observer.observe(el);
    });
    
    // Add hover sound effects (optional - can be disabled)
    if (window.AudioContext || window.webkitAudioContext) {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        function playHoverSound() {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }
        
        // Add subtle sound to interactive elements (disabled by default)
        // document.querySelectorAll('.modern-stat-card, .quick-action-card').forEach(el => {
        //     el.addEventListener('mouseenter', playHoverSound);
        // });
    }
    
    console.log('Modern Dashboard ready with enhanced features');
});

// Medical Conditions Chart Implementation
function initializeMedicalChart() {
    const chartContainer = document.getElementById('medicalConditionsChart');
    if (!chartContainer) {
        console.warn('Medical conditions chart container not found');
        return;
    }

    // Get chart data from PHP
    <?php if (!empty($medical_conditions)): ?>
    const medicalData = {
        labels: [<?php 
            $labels = [];
            foreach ($medical_conditions as $condition) {
                $labels[] = "'" . addslashes($condition['medical_conditions']) . "'";
            }
            echo implode(', ', $labels);
        ?>],
        datasets: [{
            data: [<?php 
                $counts = [];
                foreach ($medical_conditions as $condition) {
                    $counts[] = $condition['count'];
                }
                echo implode(', ', $counts);
            ?>],
            backgroundColor: [
                '#FF6384',
                '#36A2EB', 
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40',
                '#FF6384',
                '#C9CBCF'
            ],
            borderColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56', 
                '#4BC0C0',
                '#9966FF',
                '#FF9F40',
                '#FF6384',
                '#C9CBCF'
            ],
            borderWidth: 2,
            hoverBorderWidth: 3
        }]
    };

    const chartConfig = {
        type: 'doughnut',
        data: medicalData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Prompt, sans-serif',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} คน (${percentage}%)`;
                        }
                    },
                    titleFont: {
                        family: 'Prompt, sans-serif'
                    },
                    bodyFont: {
                        family: 'Prompt, sans-serif'
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeOutQuart'
            },
            cutout: '60%'
        }
    };

    // Create the chart
    const medicalChart = new Chart(chartContainer, chartConfig);
    
    // Add center text
    Chart.register({
        id: 'centerText',
        beforeDraw: function(chart) {
            if (chart.config.options.elements && chart.config.options.elements.center) {
                const ctx = chart.ctx;
                const centerConfig = chart.config.options.elements.center;
                const fontStyle = centerConfig.fontStyle || 'Arial';
                const txt = centerConfig.text;
                const color = centerConfig.color || '#000';
                const maxFontSize = centerConfig.maxFontSize || 75;
                const sidePadding = centerConfig.sidePadding || 20;
                const sidePaddingCalculated = (sidePadding / 100) * (chart.innerRadius * 2);
                
                ctx.font = "30px " + fontStyle;
                const stringWidth = ctx.measureText(txt).width;
                const elementWidth = (chart.innerRadius * 2) - sidePaddingCalculated;

                const fontSizeToUse = Math.min(maxFontSize, elementWidth / stringWidth * 30);
                const centerX = ((chart.chartArea.left + chart.chartArea.right) / 2);
                const centerY = ((chart.chartArea.top + chart.chartArea.bottom) / 2);

                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = fontSizeToUse + "px " + fontStyle;
                ctx.fillStyle = color;
                ctx.fillText(txt, centerX, centerY);
            }
        }
    });
    
    // Add center text to show total
    chartConfig.options.elements = {
        center: {
            text: '<?php echo isset($stats['elderly_count']) ? $stats['elderly_count'] : '0'; ?>',
            color: '#666',
            fontStyle: 'Prompt',
            sidePadding: 20,
            maxFontSize: 25
        }
    };
    
    console.log('Medical conditions chart initialized successfully');
    
    <?php else: ?>
    // No data available
    chartContainer.innerHTML = `
        <div class="d-flex flex-column align-items-center justify-content-center h-100 text-muted">
            <i class="fas fa-chart-pie fa-3x mb-3 opacity-50"></i>
            <p class="mb-0">ยังไม่มีข้อมูลโรคประจำตัว</p>
        </div>
    `;
    console.log('No medical conditions data available');
    <?php endif; ?>
}

// Refresh functions for dynamic updates
function refreshStaffSummary() {
    const button = event.target;
    const originalHTML = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
    button.disabled = true;
    
    // In real implementation, this would make an AJAX call
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.disabled = false;
        showNotification('ข้อมูลเจ้าหน้าที่ได้รับการอัพเดทแล้ว', 'success');
    }, 1000);
}

function refreshElderlySummary() {
    const button = event.target;
    const originalHTML = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
    button.disabled = true;
    
    // In real implementation, this would make an AJAX call
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.disabled = false;
        showNotification('ข้อมูลผู้สูงอายุได้รับการอัพเดทแล้ว', 'success');
    }, 1000);
}

function refreshMedicalChart() {
    const button = event.target;
    const originalHTML = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
    button.disabled = true;
    
    // In real implementation, this would make an AJAX call to get new data
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.disabled = false;
        showNotification('กราฟโรคประจำตัวได้รับการอัพเดทแล้ว', 'success');
        
        // Re-initialize chart with new data
        initializeMedicalChart();
    }, 1000);
}

// CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
        top: 50%;
        left: 50%;
        width: 100px;
        height: 100px;
        margin-top: -50px;
        margin-left: -50px;
    }
    
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>

<!-- Chart.js for Medical Conditions Chart -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>