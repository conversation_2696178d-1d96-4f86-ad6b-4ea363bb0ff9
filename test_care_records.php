<?php
// Test script for care records
define('AIVORA_SECURITY', true);
require_once 'config/database.php';

echo "<h1>🧪 ทดสอบระบบบันทึกการดูแล</h1>\n";

// Check which tables exist
echo "<h2>📋 ตรวจสอบตารางในฐานข้อมูล</h2>\n";

$care_tables = [
    'care_vital_signs' => 'สัญญาณชีพ',
    'medication_records' => 'บันทึกการให้ยา', 
    'symptoms_records' => 'บันทึกอาการ',
    'excretion_records' => 'การขับถ่าย',
    'hygiene_records' => 'สุขอนามัย',
    'feeding_records' => 'ทาน/ฟีดอาหาร',
    'sleep_records' => 'การนอนหลับ',
    'activities_records' => 'กิจกรรม',
    'care_turning_records' => 'การพลิกตัว',
    'care_pressure_sores' => 'แผลกดทับ',
    'care_weight_height' => 'น้ำหนักและส่วนสูง',
    'care_incident_reports' => 'รายงานเหตุการณ์',
    'sputum_records' => 'คุณสมบัติของเสมหะ',
    'mental_state_records' => 'สภาวะจิตใจ/อารมณ์'
];

$existing_tables = [];

foreach ($care_tables as $table => $description) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "✅ <strong>$table</strong> ($description) - มีอยู่<br>\n";
        $existing_tables[] = $table;
        
        // Count records
        $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "&nbsp;&nbsp;&nbsp;📊 จำนวนรายการ: $count<br>\n";
        }
    } else {
        echo "❌ <strong>$table</strong> ($description) - ไม่มี<br>\n";
    }
}

echo "<br><h2>🔍 ตรวจสอบข้อมูลผู้สูงอายุ</h2>\n";

// Check elderly records
$elderly_result = $conn->query("SELECT id, first_name, last_name FROM elderly LIMIT 5");
if ($elderly_result && $elderly_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>ชื่อ</th><th>นามสกุล</th></tr>\n";
    
    while ($elderly = $elderly_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$elderly['id']}</td>";
        echo "<td>{$elderly['first_name']}</td>";
        echo "<td>{$elderly['last_name']}</td>";
        echo "</tr>\n";
    }
    echo "</table><br>\n";
} else {
    echo "❌ ไม่พบข้อมูลผู้สูงอายุ<br>\n";
}

// Test API directly
echo "<h2>🔗 ทดสอบ API โดยตรง</h2>\n";

// Get the first elderly ID for testing
$elderly_result = $conn->query("SELECT id FROM elderly LIMIT 1");
if ($elderly_result && $elderly_result->num_rows > 0) {
    $elderly_id = $elderly_result->fetch_assoc()['id'];
    $test_date = date('Y-m-d'); // Today
    
    echo "📞 ทดสอบ API: elderly_id=$elderly_id, date=$test_date<br>\n";
    
    // Create test session
    session_start();
    $_SESSION['user_id'] = 1; // Fake user for testing
    
    // Test API call
    $url = "http://localhost:8080/aivora/api/get_care_records_by_date.php?elderly_id=$elderly_id&date=$test_date";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Cookie: ' . session_name() . '=' . session_id()
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ API Response: <br>\n";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
        } else {
            echo "❌ Invalid JSON response: $response<br>\n";
        }
    } else {
        echo "❌ ไม่สามารถเรียก API ได้<br>\n";
    }
}

// Add sample data function
echo "<h2>➕ เพิ่มข้อมูลตัวอย่าง</h2>\n";

if (isset($_POST['add_sample_data'])) {
    $elderly_id = intval($_POST['elderly_id']);
    $sample_date = $_POST['sample_date'];
    
    echo "<h3>🔄 กำลังเพิ่มข้อมูลตัวอย่าง...</h3>\n";
    
    // Add sample vital signs (if table exists)
    if (in_array('care_vital_signs', $existing_tables)) {
        $sql = "INSERT INTO care_vital_signs (elderly_id, temperature, heart_rate, blood_pressure_systolic, blood_pressure_diastolic, respiratory_rate, oxygen_saturation, blood_sugar, notes, recorded_at, created_at, updated_at) VALUES (?, 36.5, 72, 120, 80, 18, 98, 95, 'ข้อมูลตัวอย่างสำหรับทดสอบ', ?, NOW(), NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("is", $elderly_id, $sample_date . ' 08:00:00');
            if ($stmt->execute()) {
                echo "✅ เพิ่มข้อมูลสัญญาณชีพสำเร็จ<br>\n";
            } else {
                echo "❌ เพิ่มข้อมูลสัญญาณชีพไม่สำเร็จ: " . $stmt->error . "<br>\n";
            }
            $stmt->close();
        }
    }
    
    // Add sample medication (if table exists)
    if (in_array('medication_records', $existing_tables)) {
        $sql = "INSERT INTO medication_records (elderly_id, medication_date, medication_name, medication_dosage, medication_route, time_period, administration_time, notes, given_by, recorded_by, recorded_datetime) VALUES (?, ?, 'Paracetamol', '500mg', 'oral', 'morning', '08:00:00', 'ข้อมูลตัวอย่างสำหรับทดสอบ', 'พยาบาล', 1, NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("is", $elderly_id, $sample_date);
            if ($stmt->execute()) {
                echo "✅ เพิ่มข้อมูลการให้ยาสำเร็จ<br>\n";
            } else {
                echo "❌ เพิ่มข้อมูลการให้ยาไม่สำเร็จ: " . $stmt->error . "<br>\n";
            }
            $stmt->close();
        }
    }
    
    // Add sample symptoms (if table exists)
    if (in_array('symptoms_records', $existing_tables)) {
        $sql = "INSERT INTO symptoms_records (elderly_id, record_date, record_time, symptoms, severity, temperature, notes, recorded_by, recorded_by_name, recorded_datetime) VALUES (?, ?, '10:30:00', 'ปวดหัวเล็กน้อย', 'mild', 36.8, 'ข้อมูลตัวอย่างสำหรับทดสอบ', 1, 'พยาบาล', NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("is", $elderly_id, $sample_date);
            if ($stmt->execute()) {
                echo "✅ เพิ่มข้อมูลอาการสำเร็จ<br>\n";
            } else {
                echo "❌ เพิ่มข้อมูลอาการไม่สำเร็จ: " . $stmt->error . "<br>\n";
            }
            $stmt->close();
        }
    }
    
    echo "<br>✨ <strong>เพิ่มข้อมูลตัวอย่างเสร็จสิ้น!</strong><br>\n";
    echo "🔄 <a href='?'>รีเฟรชหน้า</a> เพื่อดูการเปลี่ยนแปลง<br>\n";
}

// Sample data form
if (!empty($existing_tables)) {
    $elderly_result = $conn->query("SELECT id, first_name, last_name FROM elderly LIMIT 1");
    if ($elderly_result && $elderly_result->num_rows > 0) {
        $elderly = $elderly_result->fetch_assoc();
        
        echo "<form method='POST'>\n";
        echo "<h4>📝 เพิ่มข้อมูลตัวอย่างสำหรับการทดสอบ</h4>\n";
        echo "<p>ผู้สูงอายุ: {$elderly['first_name']} {$elderly['last_name']} (ID: {$elderly['id']})</p>\n";
        echo "<input type='hidden' name='elderly_id' value='{$elderly['id']}'>\n";
        echo "<label>วันที่: <input type='date' name='sample_date' value='" . date('Y-m-d') . "' required></label><br><br>\n";
        echo "<button type='submit' name='add_sample_data' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>➕ เพิ่มข้อมูลตัวอย่าง</button>\n";
        echo "</form><br>\n";
    }
}

// Test calendar date query
echo "<h2>📅 ทดสอบการ Query ตามวันที่</h2>\n";

if (isset($_GET['test_date']) && isset($_GET['test_elderly_id'])) {
    $test_date = $_GET['test_date'];
    $test_elderly_id = intval($_GET['test_elderly_id']);
    
    echo "<h3>🔍 ค้นหาข้อมูลวันที่: $test_date สำหรับผู้สูงอายุ ID: $test_elderly_id</h3>\n";
    
    foreach ($existing_tables as $table) {
        echo "<h4>📋 ตาราง: $table</h4>\n";
        
        // Different date column names for different tables
        $date_column = 'recorded_datetime';
        if ($table === 'medication_records') {
            $date_column = 'medication_date';
        } elseif ($table === 'symptoms_records') {
            $date_column = 'record_date';
        } elseif (strpos($table, 'care_') === 0) {
            $date_column = 'recorded_at';
        }
        
        $sql = "SELECT * FROM $table WHERE elderly_id = ? AND DATE($date_column) = ?";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("is", $test_elderly_id, $test_date);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                echo "✅ พบ {$result->num_rows} รายการ<br>\n";
                echo "<table border='1' style='border-collapse: collapse; font-size: 12px;'>\n";
                
                // Show headers
                $fields = $result->fetch_fields();
                echo "<tr>";
                foreach ($fields as $field) {
                    echo "<th>{$field->name}</th>";
                }
                echo "</tr>\n";
                
                // Show data
                $result->data_seek(0);
                while ($row = $result->fetch_assoc()) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                    }
                    echo "</tr>\n";
                }
                echo "</table><br>\n";
            } else {
                echo "❌ ไม่พบข้อมูล<br>\n";
            }
            $stmt->close();
        }
    }
}

// Quick test form
$elderly_result = $conn->query("SELECT id, first_name, last_name FROM elderly LIMIT 5");
if ($elderly_result && $elderly_result->num_rows > 0) {
    echo "<form method='GET'>\n";
    echo "<h4>🔍 ทดสอบค้นหาข้อมูลตามวันที่</h4>\n";
    echo "<label>ผู้สูงอายุ: <select name='test_elderly_id'>";
    $elderly_result->data_seek(0);
    while ($elderly = $elderly_result->fetch_assoc()) {
        echo "<option value='{$elderly['id']}'>{$elderly['first_name']} {$elderly['last_name']} (ID: {$elderly['id']})</option>";
    }
    echo "</select></label><br><br>\n";
    echo "<label>วันที่: <input type='date' name='test_date' value='" . date('Y-m-d') . "' required></label><br><br>\n";
    echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔍 ค้นหาข้อมูล</button>\n";
    echo "</form><br>\n";
}

echo "<h2>📋 คำสั่งที่เป็นประโยชน์</h2>\n";
echo "<ul>\n";
echo "<li><strong>ดูข้อมูลในตาราง:</strong> <code>SELECT * FROM care_vital_signs WHERE elderly_id = 1;</code></li>\n";
echo "<li><strong>ลบข้อมูลทดสอบ:</strong> <code>DELETE FROM care_vital_signs WHERE notes LIKE '%ตัวอย่าง%';</code></li>\n";
echo "<li><strong>ดูข้อมูลตามวันที่:</strong> <code>SELECT * FROM medication_records WHERE DATE(medication_date) = '2024-08-19';</code></li>\n";
echo "</ul>\n";

$conn->close();
?>