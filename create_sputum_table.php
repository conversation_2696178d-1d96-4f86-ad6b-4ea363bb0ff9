<?php
// create_sputum_table.php - <PERSON><PERSON><PERSON> to create sputum table manually if needed
define('AIVORA_SECURITY', true);

require_once __DIR__ . '/config/database.php';

try {
    // Check if table already exists
    $table_check = $conn->query("SHOW TABLES LIKE 'care_sputum'");
    
    if ($table_check->num_rows > 0) {
        echo "✅ Table 'care_sputum' already exists.\n";
    } else {
        echo "📋 Creating 'care_sputum' table...\n";
        
        // Read and execute the SQL file
        $sql_file = __DIR__ . '/config/create_sputum_table.sql';
        
        if (!file_exists($sql_file)) {
            throw new Exception("SQL file not found: " . $sql_file);
        }
        
        $sql = file_get_contents($sql_file);
        
        if ($conn->multi_query($sql)) {
            // Clear any pending results
            while ($conn->more_results() && $conn->next_result()) {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            }
            
            echo "✅ Table 'care_sputum' created successfully!\n";
            
            // Verify table was created
            $verify_check = $conn->query("SHOW TABLES LIKE 'care_sputum'");
            if ($verify_check->num_rows > 0) {
                echo "✅ Table verification successful.\n";
                
                // Show table structure
                echo "\n📊 Table structure:\n";
                $describe = $conn->query("DESCRIBE care_sputum");
                while ($row = $describe->fetch_assoc()) {
                    echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Key']}\n";
                }
            } else {
                echo "❌ Table verification failed.\n";
            }
            
        } else {
            throw new Exception("Error creating table: " . $conn->error);
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

$conn->close();
?>