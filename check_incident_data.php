<?php
require_once 'config/database.php';

echo "<h3>ตรวจสอบข้อมูลในตาราง incident_reports</h3>";

try {
    // ตรวจสอบว่ามีตารางหรือไม่
    $check_table = "SHOW TABLES LIKE 'incident_reports'";
    $result_table = $conn->query($check_table);
    
    if ($result_table->num_rows == 0) {
        echo "<p>❌ ไม่พบตาราง incident_reports</p>";
        echo "<p><a href='create_incident_table.php'>สร้างตารางใหม่</a></p>";
        exit;
    }
    
    echo "<p>✅ พบตาราง incident_reports</p>";
    
    // นับจำนวนข้อมูลทั้งหมด
    $count_sql = "SELECT COUNT(*) as total FROM incident_reports";
    $count_result = $conn->query($count_sql);
    $count_row = $count_result->fetch_assoc();
    $total_records = $count_row['total'];
    
    echo "<p><strong>จำนวนรายงานเหตุการณ์ทั้งหมด: {$total_records} รายการ</strong></p>";
    
    if ($total_records > 0) {
        // แสดงข้อมูล 10 รายการล่าสุด
        $data_sql = "SELECT ir.*, e.first_name, e.last_name, u.name as user_name 
                     FROM incident_reports ir 
                     LEFT JOIN elderly e ON ir.elderly_id = e.id 
                     LEFT JOIN users u ON ir.user_id = u.id 
                     ORDER BY ir.created_at DESC 
                     LIMIT 10";
        
        $data_result = $conn->query($data_sql);
        
        if ($data_result && $data_result->num_rows > 0) {
            echo "<h4>รายงานเหตุการณ์ล่าสุด (10 รายการ):</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
            echo "<tr style='background-color: #f2f2f2;'>";
            echo "<th>ID</th><th>ผู้สูงอายุ</th><th>ประเภท</th><th>ความรุนแรง</th>";
            echo "<th>วันที่เกิดเหตุ</th><th>ผู้บันทึก</th><th>วันที่บันทึก</th><th>รายละเอียด</th>";
            echo "</tr>";
            
            while($row = $data_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['incident_type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['severity']) . "</td>";
                echo "<td>" . htmlspecialchars($row['incident_datetime']) . "</td>";
                echo "<td>" . htmlspecialchars($row['user_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
                echo "<td>" . htmlspecialchars(substr($row['incident_description'], 0, 50)) . "...</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // แสดงรายละเอียดรายการล่าสุด
            $data_result->data_seek(0); // Reset pointer
            $latest = $data_result->fetch_assoc();
            
            echo "<h4>รายละเอียดรายงานล่าสุด:</h4>";
            echo "<div style='border: 1px solid #ccc; padding: 15px; background-color: #f9f9f9;'>";
            echo "<p><strong>ID:</strong> " . $latest['id'] . "</p>";
            echo "<p><strong>ผู้สูงอายุ:</strong> " . htmlspecialchars($latest['first_name'] . ' ' . $latest['last_name']) . "</p>";
            echo "<p><strong>ประเภทเหตุการณ์:</strong> " . htmlspecialchars($latest['incident_type']) . "</p>";
            echo "<p><strong>ความรุนแรง:</strong> " . htmlspecialchars($latest['severity']) . "</p>";
            echo "<p><strong>วันที่เกิดเหตุ:</strong> " . htmlspecialchars($latest['incident_datetime']) . "</p>";
            echo "<p><strong>รายละเอียด:</strong><br>" . nl2br(htmlspecialchars($latest['incident_description'])) . "</p>";
            echo "<p><strong>การแก้ไข:</strong><br>" . nl2br(htmlspecialchars($latest['immediate_action_taken'])) . "</p>";
            if (!empty($latest['notes'])) {
                echo "<p><strong>หมายเหตุ:</strong><br>" . nl2br(htmlspecialchars($latest['notes'])) . "</p>";
            }
            echo "<p><strong>ผู้บันทึก:</strong> " . htmlspecialchars($latest['user_name']) . "</p>";
            echo "<p><strong>วันที่บันทึก:</strong> " . htmlspecialchars($latest['created_at']) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p>ไม่มีข้อมูลรายงานเหตุการณ์</p>";
        echo "<p>ลองทดสอบเพิ่มรายงานใหม่ผ่านฟอร์ม</p>";
    }
    
    // แสดงสถิติเบื้องต้น
    if ($total_records > 0) {
        echo "<h4>สถิติเบื้องต้น:</h4>";
        
        // สถิติตามประเภท
        $type_sql = "SELECT incident_type, COUNT(*) as count FROM incident_reports GROUP BY incident_type ORDER BY count DESC";
        $type_result = $conn->query($type_sql);
        if ($type_result && $type_result->num_rows > 0) {
            echo "<p><strong>ตามประเภทเหตุการณ์:</strong></p>";
            echo "<ul>";
            while($row = $type_result->fetch_assoc()) {
                echo "<li>" . htmlspecialchars($row['incident_type']) . ": " . $row['count'] . " รายการ</li>";
            }
            echo "</ul>";
        }
        
        // สถิติตามความรุนแรง
        $severity_sql = "SELECT severity, COUNT(*) as count FROM incident_reports GROUP BY severity ORDER BY count DESC";
        $severity_result = $conn->query($severity_sql);
        if ($severity_result && $severity_result->num_rows > 0) {
            echo "<p><strong>ตามความรุนแรง:</strong></p>";
            echo "<ul>";
            while($row = $severity_result->fetch_assoc()) {
                echo "<li>" . htmlspecialchars($row['severity']) . ": " . $row['count'] . " รายการ</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
}

$conn->close();
?>

<style>
table { font-family: Arial, sans-serif; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
tr:nth-child(even) { background-color: #f9f9f9; }
</style>