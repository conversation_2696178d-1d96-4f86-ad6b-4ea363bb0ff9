-- สร้างตาราง medication_records สำหรับบันทึกการให้ยา
CREATE TABLE IF NOT EXISTS medication_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    elderly_id INT NOT NULL,
    medication_date DATE NOT NULL,
    medication_name VARCHAR(255) NOT NULL,
    medication_dosage VARCHAR(100) NOT NULL,
    medication_route ENUM('oral', 'injection', 'topical', 'inhaled', 'sublingual', 'rectal', 'other') NOT NULL,
    time_period ENUM('morning', 'afternoon', 'evening', 'bedtime', 'as_needed') NOT NULL,
    administration_time TIME,
    notes TEXT,
    given_by VARCHAR(100),
    recorded_by INT,
    recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_datetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes for better performance
    INDEX idx_elderly_date (elderly_id, medication_date),
    INDEX idx_medication_name (medication_name),
    INDEX idx_time_period (time_period),
    INDEX idx_recorded_datetime (recorded_datetime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- เพิ่มข้อมูลตัวอย่าง (optional)
-- INSERT INTO medication_records 
-- (elderly_id, medication_date, medication_name, medication_dosage, medication_route, time_period, administration_time, notes, given_by, recorded_by)
-- VALUES 
-- (1, CURDATE(), 'Paracetamol', '500mg', 'oral', 'morning', '08:00:00', 'ให้หลังอาหาร', 'พยาบาลสุดา', 1),
-- (1, CURDATE(), 'Metformin', '850mg', 'oral', 'evening', '19:00:00', 'ก่อนอาหาร', 'พยาบาลสุดา', 1);