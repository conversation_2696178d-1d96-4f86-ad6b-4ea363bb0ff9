<?php
// get_sputum.php - API for retrieving sputum records
define('AIVORA_SECURITY', true);

// Set content type and security headers
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Include session helper
require_once __DIR__ . '/session_helper.php';
require_once __DIR__ . '/../config/database.php';

// Initialize session with same settings as main app
initializeAPISession();

// Check GET method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Check authentication and permissions
checkAuthentication();
checkPermissions(['admin', 'facility_admin', 'staff']);

// Get current user info
$currentUser = getCurrentUser();

try {
    // Get parameters
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
    $date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';
    
    // Validate required parameters
    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }
    
    // Check if elderly exists and user has access
    $isAdmin = $currentUser['user_role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id, facility_id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล']);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง']);
        exit();
    }
    
    // Check if care_sputum table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'care_sputum'");
    if ($table_check->num_rows == 0) {
        echo json_encode([
            'success' => true, 
            'message' => 'ยังไม่มีข้อมูลเสมหะ',
            'data' => [],
            'total' => 0
        ]);
        exit();
    }
    
    // Build query
    $where_conditions = ["cs.elderly_id = ?"];
    $params = [$elderly_id];
    $types = "i";
    
    // Add date filters if provided
    if (!empty($date_from)) {
        $where_conditions[] = "cs.record_date >= ?";
        $params[] = $date_from;
        $types .= "s";
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "cs.record_date <= ?";
        $params[] = $date_to;
        $types .= "s";
    }
    
    // Add facility check for non-admin users
    if (!$isAdmin) {
        $where_conditions[] = "cs.facility_id = ?";
        $params[] = $currentUser['facility_id'];
        $types .= "i";
    }
    
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM care_sputum cs " . $where_clause;
    $count_stmt = $conn->prepare($count_sql);
    
    if (!$count_stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการนับข้อมูล']);
        exit();
    }
    
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total = $count_result->fetch_assoc()['total'];
    
    // Get records with pagination
    $sql = "SELECT cs.*, u.username as recorded_by_username
            FROM care_sputum cs
            LEFT JOIN users u ON cs.recorded_by = u.id
            " . $where_clause . "
            ORDER BY cs.record_date DESC, cs.record_time DESC, cs.created_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error]);
        exit();
    }
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $records = [];
    while ($row = $result->fetch_assoc()) {
        // Decode images JSON
        $images = null;
        if (!empty($row['images'])) {
            $decoded_images = json_decode($row['images'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_images)) {
                $images = $decoded_images;
            }
        }
        
        $records[] = [
            'id' => (int)$row['id'],
            'elderly_id' => (int)$row['elderly_id'],
            'facility_id' => (int)$row['facility_id'],
            'record_date' => $row['record_date'],
            'record_time' => $row['record_time'],
            'has_sputum' => $row['has_sputum'],
            'expulsion_method' => $row['expulsion_method'],
            'odor' => $row['odor'],
            'color' => $row['color'],
            'choking_status' => $row['choking_status'],
            'notes' => $row['notes'],
            'images' => $images,
            'recorded_by' => (int)$row['recorded_by'],
            'recorded_by_name' => $row['recorded_by_name'],
            'recorded_by_username' => $row['recorded_by_username'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'ดึงข้อมูลเสมหะเรียบร้อยแล้ว',
        'data' => $records,
        'total' => (int)$total,
        'limit' => $limit,
        'offset' => $offset,
        'has_more' => ($offset + $limit) < $total
    ]);

} catch (Exception $e) {
    error_log("Error in get_sputum.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดระบบ: ' . $e->getMessage()]);
}

$conn->close();
?>