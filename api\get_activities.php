<?php
header('Content-Type: application/json; charset=utf-8');
ini_set('display_errors', 0);
error_reporting(0);

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์
if (!in_array($_SESSION['user_role'], ['admin', 'facility_admin', 'staff'])) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'ไม่มีสิทธิ์ในการดูข้อมูลนี้'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

if (!isset($conn) || $conn->connect_error) {
    echo json_encode([
        'success' => false,
        'message' => 'ไม่สามารถเชื่อมต่อฐานข้อมูลได้'
    ]);
    exit;
}

try {
    // รับพารามิเตอร์
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $date = isset($_GET['date']) ? trim($_GET['date']) : null;

    if ($elderly_id <= 0) {
        throw new Exception('รหัสผู้สูงอายุไม่ถูกต้อง');
    }

    // ตรวจสอบสิทธิ์เข้าถึงข้อมูลผู้สูงอายุ
    if ($_SESSION['user_role'] !== 'admin') {
        $facility_check_sql = "SELECT id FROM elderly WHERE id = ? AND facility_id = ?";
        $facility_stmt = $conn->prepare($facility_check_sql);
        $facility_stmt->bind_param("ii", $elderly_id, $_SESSION['facility_id']);
        $facility_stmt->execute();
        $facility_result = $facility_stmt->get_result();
        
        if ($facility_result->num_rows === 0) {
            throw new Exception('ไม่พบข้อมูลผู้สูงอายุในสถานพยาบาลของคุณ');
        }
        $facility_stmt->close();
    }

    // สร้างคำสั่ง SQL
    $where_conditions = ["ar.elderly_id = ?"];
    $params = [$elderly_id];
    $types = "i";

    if ($date) {
        $where_conditions[] = "ar.date = ?";
        $params[] = $date;
        $types .= "s";
    }

    $where_clause = implode(" AND ", $where_conditions);

    // ดึงข้อมูลกิจกรรม
    $sql = "SELECT ar.*, u.username as recorded_by_name,
                   DATE_FORMAT(ar.date, '%d/%m/%Y') as date_thai,
                   DATE_FORMAT(ar.recorded_datetime, '%d/%m/%Y %H:%i') as recorded_datetime_thai
            FROM activities_records ar 
            LEFT JOIN users u ON ar.recorded_by = u.id 
            WHERE $where_clause 
            ORDER BY ar.date DESC, ar.recorded_datetime DESC 
            LIMIT ?";

    $stmt = $conn->prepare($sql);
    $params[] = $limit;
    $types .= "i";
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $activities = [];
    while ($row = $result->fetch_assoc()) {
        // Decode JSON fields
        $activity_types = json_decode($row['activity_types'], true) ?: [];
        $cooperation_level = json_decode($row['cooperation_level'], true) ?: [];
        $images = json_decode($row['images'], true) ?: [];

        $activities[] = [
            'id' => $row['id'],
            'date' => $row['date'],
            'date_thai' => $row['date_thai'],
            'activity_types' => $activity_types,
            'activity_types_text' => implode(', ', $activity_types),
            'cooperation_level' => $cooperation_level,
            'cooperation_level_text' => implode(', ', $cooperation_level),
            'notes' => $row['notes'],
            'images' => $images,
            'image_count' => count($images),
            'recorded_by' => $row['recorded_by'],
            'recorded_by_name' => $row['recorded_by_name'] ?: 'ไม่ทราบชื่อ',
            'recorded_datetime' => $row['recorded_datetime'],
            'recorded_datetime_thai' => $row['recorded_datetime_thai']
        ];
    }

    // สร้าง summary
    $summary_sql = "SELECT 
                        COUNT(*) as total_records,
                        COUNT(CASE WHEN date = CURDATE() THEN 1 END) as today_records,
                        COUNT(CASE WHEN date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_records,
                        COUNT(CASE WHEN date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as month_records
                    FROM activities_records 
                    WHERE elderly_id = ?";

    $summary_stmt = $conn->prepare($summary_sql);
    $summary_stmt->bind_param("i", $elderly_id);
    $summary_stmt->execute();
    $summary_result = $summary_stmt->get_result();
    $summary = $summary_result->fetch_assoc();

    // ตรวจสอบการบันทึกในวันนี้
    $today_check_sql = "SELECT ar.*, u.username as recorded_by_name 
                        FROM activities_records ar 
                        LEFT JOIN users u ON ar.recorded_by = u.id 
                        WHERE ar.elderly_id = ? AND ar.date = CURDATE()";
    
    $today_stmt = $conn->prepare($today_check_sql);
    $today_stmt->bind_param("i", $elderly_id);
    $today_stmt->execute();
    $today_result = $today_stmt->get_result();
    $today_record = $today_result->fetch_assoc();

    $today_status = null;
    if ($today_record) {
        $today_status = [
            'recorded' => true,
            'recorded_by' => $today_record['recorded_by_name'] ?: 'ไม่ทราบชื่อ',
            'recorded_time' => date('H:i', strtotime($today_record['recorded_datetime'])),
            'activity_count' => count(json_decode($today_record['activity_types'], true) ?: [])
        ];
    } else {
        $today_status = ['recorded' => false];
    }

    $stmt->close();
    $summary_stmt->close();
    $today_stmt->close();

    echo json_encode([
        'success' => true,
        'data' => [
            'activities' => $activities,
            'summary' => $summary,
            'today_status' => $today_status,
            'total_found' => count($activities)
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>