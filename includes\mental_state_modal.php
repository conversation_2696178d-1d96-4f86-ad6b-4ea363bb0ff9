<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Mental State Recording Modal -->
<div id="mentalStateModal" class="modal" style="display: none;">
    <div class="modal-content mental-state-modal-content">
        <div class="modal-header">
            <div>
                <h2>😊 บันทึกสภาวะจิตใจ</h2>
                <div id="mentalStateSummary" class="mental-state-summary">
                    <span id="mentalStateSummaryText">กำลังโหลด...</span>
                </div>
            </div>
            <span class="close" onclick="closeMentalStateModal()">&times;</span>
        </div>
        
        <!-- Previous Mental State Records Section -->
        <div id="previousMentalStateSection" class="previous-mental-state-section" style="display: none;">
            <h3>📝 การบันทึกสภาวะจิตใจก่อนหน้า</h3>
            <div id="previousMentalStateList" class="previous-mental-state-list">
                <!-- จะถูกโหลดด้วย JavaScript -->
            </div>
        </div>
        
        <form id="mentalStateForm" method="POST" enctype="multipart/form-data" onsubmit="return false;">
            <input type="hidden" id="mental_state_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="mental_state_date">วันที่บันทึก:</label>
                <input type="date" id="mental_state_date" name="record_date" required>
            </div>

            <div class="form-group">
                <label for="mental_state_time">เวลา (ไม่บังคับ):</label>
                <input type="time" id="mental_state_time" name="record_time">
            </div>

            <!-- สภาวะจิตใจ -->
            <div class="form-section">
                <h4>🧠 สภาวะจิตใจ (เลือกได้หลายข้อ)</h4>
                
                <div class="form-group">
                    <div class="mental-conditions-grid">
                        <div class="condition-card positive">
                            <input type="checkbox" id="condition_alert" name="mental_conditions[]" value="ตื่นตัวดี">
                            <label for="condition_alert" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">👁️</span>
                                </div>
                                <div class="condition-text">ตื่นตัวดี</div>
                            </label>
                        </div>
                        
                        <div class="condition-card positive">
                            <input type="checkbox" id="condition_aware" name="mental_conditions[]" value="รู้สึกตัวดี">
                            <label for="condition_aware" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">✨</span>
                                </div>
                                <div class="condition-text">รู้สึกตัวดี</div>
                            </label>
                        </div>
                        
                        <div class="condition-card positive">
                            <input type="checkbox" id="condition_happy" name="mental_conditions[]" value="อารมณ์แจ่มใส">
                            <label for="condition_happy" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #FFD54F 0%, #FBC02D 100%);">
                                    <span class="icon">😊</span>
                                </div>
                                <div class="condition-text">อารมณ์แจ่มใส</div>
                            </label>
                        </div>
                        
                        <div class="condition-card positive">
                            <input type="checkbox" id="condition_responsive" name="mental_conditions[]" value="ตอบสนองดี">
                            <label for="condition_responsive" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);">
                                    <span class="icon">👍</span>
                                </div>
                                <div class="condition-text">ตอบสนองดี</div>
                            </label>
                        </div>
                        
                        <div class="condition-card negative">
                            <input type="checkbox" id="condition_drowsy" name="mental_conditions[]" value="ไม่ค่อยตื่นตัว">
                            <label for="condition_drowsy" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">😴</span>
                                </div>
                                <div class="condition-text">ไม่ค่อยตื่นตัว</div>
                            </label>
                        </div>
                        
                        <div class="condition-card negative">
                            <input type="checkbox" id="condition_depressed" name="mental_conditions[]" value="ซึมลง">
                            <label for="condition_depressed" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);">
                                    <span class="icon">😞</span>
                                </div>
                                <div class="condition-text">ซึมลง</div>
                            </label>
                        </div>
                        
                        <div class="condition-card critical">
                            <input type="checkbox" id="condition_confused" name="mental_conditions[]" value="สับสน">
                            <label for="condition_confused" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">😵</span>
                                </div>
                                <div class="condition-text">สับสน</div>
                            </label>
                        </div>
                        
                        <div class="condition-card critical">
                            <input type="checkbox" id="condition_delirious" name="mental_conditions[]" value="มีอาการเพ้อ">
                            <label for="condition_delirious" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">🤪</span>
                                </div>
                                <div class="condition-text">มีอาการเพ้อ</div>
                            </label>
                        </div>
                        
                        <div class="condition-card critical">
                            <input type="checkbox" id="condition_unresponsive" name="mental_conditions[]" value="ไม่ตอบสนอง">
                            <label for="condition_unresponsive" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #9E9E9E 0%, #616161 100%);">
                                    <span class="icon">😶</span>
                                </div>
                                <div class="condition-text">ไม่ตอบสนอง</div>
                            </label>
                        </div>
                        
                        <div class="condition-card critical">
                            <input type="checkbox" id="condition_agitated" name="mental_conditions[]" value="โวยวาย">
                            <label for="condition_agitated" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);">
                                    <span class="icon">😤</span>
                                </div>
                                <div class="condition-text">โวยวาย</div>
                            </label>
                        </div>
                        
                        <div class="condition-card neutral">
                            <input type="checkbox" id="condition_other" name="mental_conditions[]" value="อื่นๆ">
                            <label for="condition_other" class="condition-label">
                                <div class="condition-icon" style="background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);">
                                    <span class="icon">➕</span>
                                </div>
                                <div class="condition-text">อื่นๆ</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="mental_state_notes">คำอธิบายเพิ่มเติม:</label>
                <textarea id="mental_state_notes" name="additional_notes" rows="3" placeholder="รายละเอียดเพิ่มเติม เช่น พฤติกรรมที่สังเกตเห็น, การตอบสนอง, หรือหมายเหตุอื่นๆ"></textarea>
            </div>

            <div class="form-group">
                <label for="mental_state_images">รูปภาพประกอบ (ไม่บังคับ):</label>
                <input type="file" id="mental_state_images" name="mental_state_images[]" multiple accept="image/*" class="file-input">
                <div class="file-upload-area" onclick="document.getElementById('mental_state_images').click()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">
                        <strong>คลิกเพื่อเลือกรูปภาพ</strong><br>
                        <small>รองรับ JPG, PNG, GIF (สูงสุด 5 รูป)</small>
                    </div>
                </div>
                <div id="mentalStateImagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label for="mental_state_recorded_by">ผู้บันทึก:</label>
                <input type="text" id="mental_state_recorded_by" name="recorded_by_name" value="<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>" readonly>
            </div>

            <div class="modal-actions">
                <button type="button" onclick="closeMentalStateModal()" class="btn-cancel">ยกเลิก</button>
                <button type="submit" class="btn-save">บันทึกสภาวะจิตใจ</button>
            </div>
        </form>
    </div>
</div>

<style>
/* Compact modal for mental state */
.mental-state-modal-content {
    width: 90%;
    max-width: 650px;
    max-height: 85vh;
    overflow-y: auto;
}

.mental-state-modal-content .modal-header h2 {
    font-size: 1.3rem;
    margin: 0;
}

.mental-state-summary {
    font-size: 0.85rem;
    margin-top: 5px;
    opacity: 0.9;
}

.previous-mental-state-section {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
}

.previous-mental-state-section h3 {
    margin: 0 0 10px 0;
    color: #2e7d32;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

.previous-mental-state-list {
    display: grid;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.mental-state-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.mental-state-item:hover {
    border-color: #4CAF50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.mental-conditions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.condition-card {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.condition-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.condition-card input[type="checkbox"] {
    display: none;
}

.condition-card input[type="checkbox"]:checked + .condition-label {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid #4CAF50;
    border-radius: 4px;
}

.condition-card input[type="checkbox"]:checked + .condition-label .condition-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.condition-label {
    display: block;
    text-align: center;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.condition-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 4px;
    transition: all 0.3s ease;
}

.condition-icon .icon {
    font-size: 1rem;
    filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
}

.condition-text {
    font-weight: 500;
    font-size: 0.75rem;
    color: var(--text-primary);
    line-height: 1.1;
}

/* Color coding for different condition types */
.condition-card.positive .condition-label {
    border-left: 3px solid #4CAF50;
}

.condition-card.negative .condition-label {
    border-left: 3px solid #FF9800;
}

.condition-card.critical .condition-label {
    border-left: 3px solid #F44336;
}

.condition-card.neutral .condition-label {
    border-left: 3px solid #9C27B0;
}

.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 6px;
}

.file-upload-area:hover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.05);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 6px;
    color: #6c757d;
}

.upload-text {
    color: #6c757d;
}

.upload-text strong {
    color: #4CAF50;
}

.file-input {
    display: none;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    margin-top: 10px;
}

.preview-item {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-remove:hover {
    background: #c82333;
}

/* Form sections styling */
.form-section {
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h4 {
    margin: 0 0 8px 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--dark-green);
}

.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .mental-state-modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .mental-conditions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }
    
    .condition-icon {
        width: 28px;
        height: 28px;
    }
    
    .condition-text {
        font-size: 0.7rem;
    }
    
    .image-preview {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }
    
    .preview-item {
        width: 60px;
        height: 60px;
    }
}
</style>

<script>
function openMentalStateModal() {
    // Get elderly ID from the page context
    const elderlyIdElement = document.querySelector('input[name="elderly_id"]') || 
                             document.querySelector('#mental_state_elderly_id') ||
                             document.querySelector('[data-elderly-id]');
    
    let elderlyId = 0;
    if (elderlyIdElement) {
        elderlyId = elderlyIdElement.value || elderlyIdElement.getAttribute('data-elderly-id');
    }
    
    // Try to get from PHP if available
    <?php if (isset($elderly_id) && $elderly_id > 0): ?>
    elderlyId = <?php echo $elderly_id; ?>;
    <?php endif; ?>
    
    elderlyId = parseInt(elderlyId) || 0;
    
    if (!elderlyId) {
        alert('ไม่พบรหัสผู้สูงอายุ');
        return;
    }

    // Set elderly ID in the form
    document.getElementById('mental_state_elderly_id').value = elderlyId;
    
    // Reset form
    document.getElementById('mentalStateForm').reset();
    
    // Set current date and time
    const now = new Date();
    document.getElementById('mental_state_date').value = now.toISOString().split('T')[0];
    document.getElementById('mental_state_time').value = now.toTimeString().substr(0,5);
    
    // Set recorded by
    document.getElementById('mental_state_recorded_by').value = '<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>';
    
    // Show modal
    document.getElementById('mentalStateModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Load previous records
    loadPreviousMentalStateRecords(elderlyId);
}

function closeMentalStateModal() {
    document.getElementById('mentalStateModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('mentalStateForm').reset();
    clearMentalStateImagePreview();
}

function loadPreviousMentalStateRecords(elderlyId) {
    fetch(`api/get_mental_state.php?elderly_id=${elderlyId}&limit=5`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                displayPreviousMentalStateRecords(data.data);
                document.getElementById('previousMentalStateSection').style.display = 'block';
            } else {
                document.getElementById('previousMentalStateSection').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading previous mental state records:', error);
            document.getElementById('previousMentalStateSection').style.display = 'none';
        });
}

function displayPreviousMentalStateRecords(records) {
    const container = document.getElementById('previousMentalStateList');
    container.innerHTML = '';
    
    records.forEach(record => {
        const item = document.createElement('div');
        item.className = 'mental-state-item';
        
        const recordDate = new Date(record.record_date + (record.record_time ? 'T' + record.record_time : '')).toLocaleString('th-TH');
        const conditions = record.mental_conditions ? record.mental_conditions.split(',').join(', ') : '-';
        
        item.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                <strong>${recordDate}</strong>
                <span style="font-size: 0.7rem; color: #6c757d;">
                    จำนวน: ${record.mental_conditions ? record.mental_conditions.split(',').length : 0} อาการ
                </span>
            </div>
            <p><strong>สภาวะ:</strong> ${conditions}</p>
            ${record.additional_notes ? `<p><strong>หมายเหตุ:</strong> ${record.additional_notes.length > 80 ? record.additional_notes.substring(0, 80) + '...' : record.additional_notes}</p>` : ''}
            <p style="font-size: 0.75rem; color: #6c757d;"><strong>ผู้บันทึก:</strong> ${record.recorded_by_name}</p>
        `;
        
        container.appendChild(item);
    });
}

// Image preview functionality
document.getElementById('mental_state_images').addEventListener('change', function(e) {
    const files = e.target.files;
    const preview = document.getElementById('mentalStateImagePreview');
    
    // Clear previous previews
    clearMentalStateImagePreview();
    
    // Limit to 5 files
    const maxFiles = Math.min(files.length, 5);
    
    for (let i = 0; i < maxFiles; i++) {
        const file = files[i];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="Preview">
                    <button type="button" class="preview-remove" onclick="removeMentalStateImagePreview(this, ${i})">&times;</button>
                `;
                preview.appendChild(previewItem);
            };
            
            reader.readAsDataURL(file);
        }
    }
});

function clearMentalStateImagePreview() {
    document.getElementById('mentalStateImagePreview').innerHTML = '';
}

function removeMentalStateImagePreview(button, index) {
    const previewItem = button.parentElement;
    previewItem.remove();
    
    // Update file input by creating new FileList without the removed file
    const fileInput = document.getElementById('mental_state_images');
    const dt = new DataTransfer();
    const files = fileInput.files;
    
    for (let i = 0; i < files.length; i++) {
        if (i !== index) {
            dt.items.add(files[i]);
        }
    }
    
    fileInput.files = dt.files;
}

// Form submission
document.getElementById('mentalStateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Validate form
    const elderlyId = formData.get('elderly_id');
    const recordDate = formData.get('record_date');
    const mentalConditions = formData.getAll('mental_conditions[]');
    
    if (!elderlyId || !recordDate) {
        alert('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
        return;
    }
    
    if (mentalConditions.length === 0) {
        alert('กรุณาเลือกสภาวะจิตใจอย่างน้อย 1 รายการ');
        return;
    }
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '⏳ กำลังบันทึก...';
    submitBtn.disabled = true;
    
    // Submit to API
    fetch('api/save_mental_state.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('บันทึกข้อมูลสภาวะจิตใจเรียบร้อยแล้ว');
            closeMentalStateModal();
            // Reload page or update display
            if (typeof loadMentalStateSummary === 'function') {
                loadMentalStateSummary();
            }
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('เกิดข้อผิดพลาดในการบันทึกข้อมูล');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Close modal when clicking outside
document.getElementById('mentalStateModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeMentalStateModal();
    }
});
</script>