<?php
// API ง่ายๆ สำหรับตรวจสอบการพลิกตัววันนี้ โดยไม่ต้องเช็ค session ซับซ้อน
session_start();
header('Content-Type: application/json; charset=utf-8');

// กำหนดค่าคงที่สำหรับความปลอดภัย
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

try {
    // เชื่อมต่อฐานข้อมูล
    require_once __DIR__ . '/../config/database_simple.php';
    
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้: ' . (isset($conn) ? $conn->connect_error : 'Connection not established'));
    }
    
    // รับพารามิเตอร์
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;

    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }

    // ตรวจสอบว่าตาราง care_turning_records มีอยู่หรือไม่
    $table_check = $conn->query("SHOW TABLES LIKE 'care_turning_records'");
    if ($table_check->num_rows == 0) {
        // ยังไม่มีตาราง - ส่งกลับว่าไม่มีข้อมูล
        echo json_encode([
            'success' => true,
            'has_today_record' => false,
            'mode' => 'create',
            'message' => 'ยังไม่มีตารางข้อมูล - สร้างใหม่'
        ]);
        exit();
    }

    // ตรวจสอบว่าวันนี้มีการบันทึกแล้วหรือไม่ - ใช้ตาราง care_turning_records
    $today = date('Y-m-d');
    $sql = "SELECT tr.*, COALESCE(u.name, u.username, 'ระบบ') as user_name 
            FROM care_turning_records tr 
            LEFT JOIN users u ON tr.user_id = u.id 
            WHERE tr.elderly_id = ? AND DATE(tr.recorded_at) = ?
            ORDER BY tr.recorded_at DESC 
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL']);
        exit();
    }

    $stmt->bind_param("is", $elderly_id, $today);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // มีข้อมูลวันนี้แล้ว - แสดงข้อมูลสำหรับการแก้ไข
        $turning_record = $result->fetch_assoc();
        
        // แปลง recorded_at เป็น date และ time
        $recorded_datetime = $turning_record['recorded_at'];
        $recorded_date = date('Y-m-d', strtotime($recorded_datetime));
        $recorded_time = date('H:i:s', strtotime($recorded_datetime));
        
        echo json_encode([
            'success' => true,
            'has_today_record' => true,
            'mode' => 'edit',
            'data' => [
                'id' => $turning_record['id'],
                'recorded_date' => $recorded_date,
                'recorded_time' => $recorded_time,
                'turning_position' => $turning_record['turning_position'],
                'turning_time' => $turning_record['turning_time'],
                'skin_condition' => $turning_record['skin_condition'],
                'pressure_relief' => $turning_record['pressure_relief'],
                'notes' => $turning_record['notes'],
                'recorded_by_name' => $turning_record['user_name'],
                'created_at' => $turning_record['created_at']
            ]
        ]);
    } else {
        // ยังไม่มีข้อมูลวันนี้ - โหมดเพิ่มใหม่
        echo json_encode([
            'success' => true,
            'has_today_record' => false,
            'mode' => 'create'
        ]);
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in check_today_turning_simple.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'เกิดข้อผิดพลาดระบบ: ' . $e->getMessage(),
        'mode' => 'create' // ถ้า error ให้ใช้โหมดสร้างใหม่
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>