<?php
ini_set('session.name', 'AIVORA_SESSION');
session_start();
// กำหนดค่าการเชื่อมต่อ
define('DB_HOST', '************');
define('DB_USER', 'root');
define('DB_PASS', '5545zz**');
define('DB_NAME', 'aivora_db');

// กำหนดค่าคงที่สำหรับความปลอดภัย
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เชื่อมต่อ MySQL โดยใช้ global variable
global $conn;
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// ตรวจสอบการเชื่อมต่อ
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// ตั้งค่า charset เป็น utf8mb4
$conn->set_charset("utf8mb4");

// สร้างตารางโดยไม่มี Foreign Key ก่อน
$tables = [
    "CREATE TABLE IF NOT EXISTS facilities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        facility_code VARCHAR(50) UNIQUE NOT NULL,
        facility_name VARCHAR(255) NOT NULL,
        facility_type VARCHAR(100) DEFAULT 'elderly_care_center',
        notes TEXT,
        address TEXT NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(100),
        website VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        role ENUM('admin', 'staff', 'facility_admin') NOT NULL,
        facility_id INT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        force_password_change TINYINT(1) DEFAULT 0,
        last_login DATETIME,
        failed_attempts INT DEFAULT 0,
        account_locked TINYINT(1) DEFAULT 0,
        password_reset_token VARCHAR(255) NULL,
        password_reset_expires DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS rooms (
        id INT AUTO_INCREMENT PRIMARY KEY,
        room_number VARCHAR(10) NOT NULL,
        floor INT NOT NULL,
        type ENUM('single', 'shared') NOT NULL,
        status ENUM('available', 'occupied', 'maintenance') DEFAULT 'available',
        price DECIMAL(10,2) NOT NULL,
        description TEXT,
        facility_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS elderly (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title ENUM('นาย', 'นาง', 'นางสาว') NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        nickname VARCHAR(50),
        birth_date DATE NOT NULL,
        id_card VARCHAR(13) UNIQUE NOT NULL,
        blood_type ENUM('A', 'B', 'AB', 'O', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-') NULL,
        allergies TEXT,
        emergency_contact TEXT NOT NULL,
        room_id INT,
        facility_id INT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS schedules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        activity_type ENUM('medication', 'checkup', 'activity', 'other') NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        event_type ENUM('emergency', 'incident', 'observation', 'other') NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        severity ENUM('low', 'medium', 'high') NOT NULL,
        status ENUM('pending', 'in_progress', 'resolved') DEFAULT 'pending',
        reported_by INT NOT NULL,
        resolved_by INT,
        resolved_at DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS stock (
        id INT AUTO_INCREMENT PRIMARY KEY,
        item_name VARCHAR(255) NOT NULL,
        category ENUM('medicine', 'medical_supply', 'food', 'cleaning', 'other') NOT NULL,
        quantity INT NOT NULL,
        unit VARCHAR(50) NOT NULL,
        minimum_quantity INT NOT NULL,
        location VARCHAR(100),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS stock_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        stock_id INT NOT NULL,
        transaction_type ENUM('in', 'out') NOT NULL,
        quantity INT NOT NULL,
        reference_number VARCHAR(50),
        note TEXT,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS invoices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        invoice_number VARCHAR(20) UNIQUE NOT NULL,
        issue_date DATE NOT NULL,
        due_date DATE NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
        payment_date DATE,
        payment_method ENUM('cash', 'transfer', 'other'),
        note TEXT,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS invoice_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id INT NOT NULL,
        description VARCHAR(255) NOT NULL,
        quantity INT NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        category ENUM('exercise', 'recreation', 'therapy', 'other') NOT NULL,
        max_participants INT,
        duration INT NOT NULL COMMENT 'Duration in minutes',
        equipment_needed TEXT,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS activity_participants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        activity_id INT NOT NULL,
        elderly_id INT NOT NULL,
        status ENUM('registered', 'attended', 'cancelled') DEFAULT 'registered',
        note TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS accessories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        category ENUM('mobility', 'medical', 'personal_care', 'other') NOT NULL,
        quantity INT NOT NULL,
        status ENUM('available', 'in_use', 'maintenance', 'disposed') DEFAULT 'available',
        location VARCHAR(100),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS accessory_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        accessory_id INT NOT NULL,
        elderly_id INT NOT NULL,
        assigned_date DATE NOT NULL,
        return_date DATE,
        status ENUM('active', 'returned', 'lost') DEFAULT 'active',
        note TEXT,
        assigned_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL,
        attempt_time INT NOT NULL,
        ip_address VARCHAR(50) NOT NULL,
        INDEX (username, attempt_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS security_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        event_type VARCHAR(50) NOT NULL,
        details TEXT,
        ip_address VARCHAR(50) NOT NULL,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (user_id, event_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS banned_ips (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ip_address VARCHAR(50) NOT NULL UNIQUE,
        reason TEXT,
        banned_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        details TEXT,
        ip_address VARCHAR(50) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (user_id, action)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL,
        message TEXT NOT NULL,
        recipients VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (type, created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    "CREATE TABLE IF NOT EXISTS notification_reads (
        id INT AUTO_INCREMENT PRIMARY KEY,
        notification_id INT NOT NULL,
        user_id INT NOT NULL,
        read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_read (notification_id, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

// สร้างตาราง
foreach ($tables as $sql) {
    if ($conn->query($sql) !== TRUE) {
        die("Error creating table: " . $conn->error);
    }
}

// เพิ่ม Foreign Key หลังจากสร้างตารางเสร็จ
$foreign_keys = [
    "ALTER TABLE users ADD CONSTRAINT fk_users_facility FOREIGN KEY (facility_id) REFERENCES facilities(id)",
    "ALTER TABLE rooms ADD CONSTRAINT fk_rooms_facility FOREIGN KEY (facility_id) REFERENCES facilities(id)",
    "ALTER TABLE elderly ADD CONSTRAINT fk_elderly_facility FOREIGN KEY (facility_id) REFERENCES facilities(id)",
    "ALTER TABLE schedules ADD CONSTRAINT fk_schedules_elderly FOREIGN KEY (elderly_id) REFERENCES elderly(id)",
    "ALTER TABLE schedules ADD CONSTRAINT fk_schedules_user FOREIGN KEY (created_by) REFERENCES users(id)",
    "ALTER TABLE events ADD CONSTRAINT fk_events_elderly FOREIGN KEY (elderly_id) REFERENCES elderly(id)",
    "ALTER TABLE events ADD CONSTRAINT fk_events_reported_by FOREIGN KEY (reported_by) REFERENCES users(id)",
    "ALTER TABLE events ADD CONSTRAINT fk_events_resolved_by FOREIGN KEY (resolved_by) REFERENCES users(id)",
    "ALTER TABLE stock_transactions ADD CONSTRAINT fk_stock_transactions_stock FOREIGN KEY (stock_id) REFERENCES stock(id)",
    "ALTER TABLE stock_transactions ADD CONSTRAINT fk_stock_transactions_user FOREIGN KEY (created_by) REFERENCES users(id)",
    "ALTER TABLE invoices ADD CONSTRAINT fk_invoices_elderly FOREIGN KEY (elderly_id) REFERENCES elderly(id)",
    "ALTER TABLE invoices ADD CONSTRAINT fk_invoices_user FOREIGN KEY (created_by) REFERENCES users(id)",
    "ALTER TABLE invoice_items ADD CONSTRAINT fk_invoice_items_invoice FOREIGN KEY (invoice_id) REFERENCES invoices(id)",
    "ALTER TABLE activities ADD CONSTRAINT fk_activities_user FOREIGN KEY (created_by) REFERENCES users(id)",
    "ALTER TABLE activity_participants ADD CONSTRAINT fk_activity_participants_activity FOREIGN KEY (activity_id) REFERENCES activities(id)",
    "ALTER TABLE activity_participants ADD CONSTRAINT fk_activity_participants_elderly FOREIGN KEY (elderly_id) REFERENCES elderly(id)",
    "ALTER TABLE accessory_assignments ADD CONSTRAINT fk_accessory_assignments_accessory FOREIGN KEY (accessory_id) REFERENCES accessories(id)",
    "ALTER TABLE accessory_assignments ADD CONSTRAINT fk_accessory_assignments_elderly FOREIGN KEY (elderly_id) REFERENCES elderly(id)",
    "ALTER TABLE accessory_assignments ADD CONSTRAINT fk_accessory_assignments_user FOREIGN KEY (assigned_by) REFERENCES users(id)",
    "ALTER TABLE notification_reads ADD CONSTRAINT fk_notification_reads_notification FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE",
    "ALTER TABLE notification_reads ADD CONSTRAINT fk_notification_reads_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE"
];

// เพิ่ม Foreign Key (ถ้าไม่มีอยู่แล้ว)
foreach ($foreign_keys as $fk_sql) {
    if ($conn->query($fk_sql) !== TRUE) {
        // ถ้าเพิ่มไม่ได้ ให้ข้ามไป (อาจจะมีอยู่แล้ว)
        error_log("Warning: Could not add foreign key: " . $conn->error);
    }
}

// เพิ่ม unique constraint สำหรับ rooms
$result = $conn->query("SHOW INDEX FROM rooms WHERE Key_name = 'unique_room_facility'");
if ($result->num_rows == 0) {
    $alter_sql = "ALTER TABLE rooms ADD UNIQUE KEY unique_room_facility (room_number, facility_id)";
    if ($conn->query($alter_sql) !== TRUE) {
        error_log("Warning: Could not add unique constraint to rooms table: " . $conn->error);
    }
}

// สร้าง admin user ถ้ายังไม่มี
$default_username = 'admin';
$default_password = password_hash('admin123', PASSWORD_DEFAULT);
$check_admin = $conn->query("SELECT id FROM users WHERE username = 'admin'");
if ($check_admin->num_rows == 0) {
    $sql = "INSERT INTO users (username, password, name, role) VALUES ('admin', '$default_password', 'Administrator', 'admin')";
    if ($conn->query($sql) !== TRUE) {
        die("Error creating default admin user: " . $conn->error);
    }
}
?>