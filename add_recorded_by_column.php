<?php
require_once 'config/database.php';

echo "Adding recorded_by column to vital_signs table...\n";

$sql = "ALTER TABLE vital_signs ADD COLUMN recorded_by INT NULL AFTER additional_notes";

if ($conn->query($sql) === TRUE) {
    echo "Column recorded_by added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column recorded_by already exists\n";
    } else {
        echo "Error: " . $conn->error . "\n";
    }
}

// Also add the foreign key if it doesn't exist
echo "Adding foreign key constraint...\n";
$fk_sql = "ALTER TABLE vital_signs ADD CONSTRAINT fk_vital_signs_recorded_by FOREIGN KEY (recorded_by) REFERENCES users(id)";

if ($conn->query($fk_sql) === TRUE) {
    echo "Foreign key constraint added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate key name") !== false || strpos($conn->error, "already exists") !== false) {
        echo "Foreign key constraint already exists\n";
    } else {
        echo "Warning - Could not add foreign key: " . $conn->error . "\n";
    }
}

// Verify the column was added
echo "\nChecking vital_signs table structure:\n";
$result = $conn->query("DESCRIBE vital_signs");
while($row = $result->fetch_assoc()) {
    echo $row['Field'] . " - " . $row['Type'] . "\n";
}
?>