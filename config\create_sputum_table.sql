-- Create care_sputum table for storing sputum records
CREATE TABLE IF NOT EXISTS care_sputum (
    id INT AUTO_INCREMENT PRIMARY KEY,
    elderly_id INT NOT NULL,
    facility_id INT NOT NULL,
    record_date DATE NOT NULL,
    record_time TIME DEFAULT NULL,
    
    -- Sputum presence
    has_sputum ENUM('มีเสมหะ', 'ไม่มีเสมหะ') NOT NULL COMMENT 'Sputum presence',
    
    -- Expulsion method (only if has sputum)
    expulsion_method ENUM('ขับเสมหะได้ด้วยตัวเอง', 'ดูดเสมหะ', 'ดูดน้ำลาย') DEFAULT NULL COMMENT 'How sputum was expelled',
    
    -- Sputum characteristics (only if has sputum)
    odor ENUM('ปกติ', 'เหม็น') DEFAULT NULL COMMENT 'Sputum odor',
    color ENUM('สีใส', 'สีขาวออกเทา', 'สีเหลือง', 'สีเขียว', 'สีแดง', 'สีน้ำตาล', 'สีดำ') DEFAULT NULL COMMENT 'Sputum color',
    
    -- Choking status
    choking_status ENUM('ไม่สำลัก', 'สำลัก') NOT NULL DEFAULT 'ไม่สำลัก' COMMENT 'Choking status',
    
    -- Additional information
    notes TEXT NULL COMMENT 'Additional notes and observations',
    images TEXT NULL COMMENT 'Image file paths (JSON array)',
    
    -- Recording information
    recorded_by INT NOT NULL COMMENT 'User ID who recorded this',
    recorded_by_name VARCHAR(100) NOT NULL COMMENT 'Name of the person who recorded',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE,
    FOREIGN KEY (facility_id) REFERENCES facilities(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_elderly_date (elderly_id, record_date),
    INDEX idx_facility_date (facility_id, record_date),
    INDEX idx_record_date (record_date),
    INDEX idx_has_sputum (has_sputum),
    INDEX idx_choking_status (choking_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Sputum care records for elderly patients';