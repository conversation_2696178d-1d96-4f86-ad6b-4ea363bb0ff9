<?php
/**
 * Comprehensive test for both vital signs APIs
 * Tests both save_vital_signs_simple.php and save_vital_signs_debug.php
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

define('AIVORA_SECURITY', true);

// Include database connection
require_once __DIR__ . '/config/database.php';

echo "<h1>Vital Signs API Test</h1>";
echo "<p>Testing both save_vital_signs_simple.php and save_vital_signs_debug.php</p>";

// Function to make API call
function callAPI($endpoint, $data) {
    $url = 'http://localhost:8080/aivora/api/' . $endpoint;
    
    $postData = http_build_query($data);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/x-www-form-urlencoded',
            'content' => $postData
        ]
    ]);
    
    $result = file_get_contents($url, false, $context);
    
    // Get HTTP response code
    $responseCode = 200;
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (preg_match('/^HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                $responseCode = intval($matches[1]);
                break;
            }
        }
    }
    
    return [
        'response_code' => $responseCode,
        'body' => $result,
        'json' => json_decode($result, true)
    ];
}

// Test database connection
echo "<h2>1. Database Connection Test</h2>";
global $conn;
if (isset($conn) && !$conn->connect_error) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Get a test elderly_id
    $elderly_result = $conn->query("SELECT id FROM elderly LIMIT 1");
    if ($elderly_result && $elderly_result->num_rows > 0) {
        $elderly_data = $elderly_result->fetch_assoc();
        $test_elderly_id = $elderly_data['id'];
        echo "<p style='color: green;'>✓ Found elderly record with ID: $test_elderly_id</p>";
    } else {
        // Create a test elderly record
        $conn->query("INSERT INTO elderly (name, age) VALUES ('Test Patient', 70)");
        $test_elderly_id = $conn->insert_id;
        echo "<p style='color: blue;'>✓ Created test elderly record with ID: $test_elderly_id</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . ($conn ? $conn->connect_error : 'Connection not established') . "</p>";
    exit;
}

// Test data for vital signs
$vitalSignsData = [
    'elderly_id' => $test_elderly_id,
    'temperature' => 37.5,
    'heart_rate' => 72,
    'blood_pressure_systolic' => 120,
    'blood_pressure_diastolic' => 80,
    'respiratory_rate' => 16,
    'oxygen_saturation' => 98,
    'blood_sugar' => 110,
    'additional_notes' => 'Test vital signs record',
    'recorded_date' => date('Y-m-d'),
    'recorded_time' => date('H:i:s'),
    'form_mode' => 'create'
];

echo "<h2>2. API Endpoint Tests</h2>";

// Test endpoints
$endpoints = [
    'save_vital_signs_debug.php' => 'Debug API (Simplified Auth)',
    'save_vital_signs_simple.php' => 'Simple API (Full Auth)'
];

foreach ($endpoints as $endpoint => $description) {
    echo "<h3>Testing $description ($endpoint)</h3>";
    
    // Test API call
    $response = callAPI($endpoint, $vitalSignsData);
    
    echo "<p><strong>HTTP Status Code:</strong> {$response['response_code']}</p>";
    echo "<p><strong>Raw Response:</strong></p>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($response['body']);
    echo "</pre>";
    
    if ($response['json']) {
        echo "<p><strong>Parsed JSON:</strong></p>";
        echo "<pre style='background: #f0f8ff; padding: 10px; border: 1px solid #ddd;'>";
        print_r($response['json']);
        echo "</pre>";
        
        // Analyze response
        if (isset($response['json']['success'])) {
            if ($response['json']['success']) {
                echo "<p style='color: green;'>✓ API call successful</p>";
                if (isset($response['json']['record_id'])) {
                    echo "<p style='color: green;'>✓ Record ID returned: {$response['json']['record_id']}</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ API call failed: {$response['json']['message']}</p>";
                if (isset($response['json']['debug'])) {
                    echo "<p><strong>Debug Information:</strong></p>";
                    echo "<pre style='background: #ffe4e1; padding: 10px; border: 1px solid #ddd;'>";
                    print_r($response['json']['debug']);
                    echo "</pre>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ Invalid JSON response format</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Invalid JSON response</p>";
        if ($response['response_code'] >= 500) {
            echo "<p style='color: red;'>✗ Server error (500 Internal Server Error)</p>";
        }
    }
    
    echo "<hr>";
}

// Test database table creation
echo "<h2>3. Database Table Test</h2>";
$table_check = $conn->query("SHOW TABLES LIKE 'elderly_vital_signs'");
if ($table_check->num_rows > 0) {
    echo "<p style='color: green;'>✓ elderly_vital_signs table exists</p>";
    
    // Check table structure
    $structure_result = $conn->query("DESCRIBE elderly_vital_signs");
    if ($structure_result) {
        echo "<p><strong>Table Structure:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $structure_result->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check if any records were inserted
    $count_result = $conn->query("SELECT COUNT(*) as count FROM elderly_vital_signs");
    if ($count_result) {
        $count_data = $count_result->fetch_assoc();
        echo "<p style='color: green;'>✓ Total records in table: {$count_data['count']}</p>";
        
        if ($count_data['count'] > 0) {
            // Show latest record
            $latest_result = $conn->query("SELECT * FROM elderly_vital_signs ORDER BY created_at DESC LIMIT 1");
            if ($latest_result && $latest_result->num_rows > 0) {
                $latest_data = $latest_result->fetch_assoc();
                echo "<p><strong>Latest Record:</strong></p>";
                echo "<pre style='background: #f0f8ff; padding: 10px; border: 1px solid #ddd;'>";
                print_r($latest_data);
                echo "</pre>";
            }
        }
    }
} else {
    echo "<p style='color: red;'>✗ elderly_vital_signs table does not exist</p>";
}

echo "<h2>4. Session Test</h2>";
session_start();
echo "<p><strong>Session Status:</strong> " . session_status() . "</p>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Data:</strong></p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Test Complete</h2>";
echo "<p>Check the results above to see if both APIs are working correctly.</p>";
?>