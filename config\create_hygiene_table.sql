-- Create care_hygiene table for storing hygiene records
CREATE TABLE IF NOT EXISTS care_hygiene (
    id INT AUTO_INCREMENT PRIMARY KEY,
    elderly_id INT NOT NULL,
    facility_id INT NOT NULL,
    record_date DATE NOT NULL,
    
    -- Diaper information
    diaper_count INT DEFAULT 0 COMMENT 'Number of diapers used',
    diaper_pad_count INT DEFAULT 0 COMMENT 'Number of diaper pads used',
    
    -- Body cleaning
    bathing ENUM('self', 'assisted', 'wipe') NOT NULL DEFAULT 'self' COMMENT 'Type of bathing',
    oral_care ENUM('done', 'not_done') NOT NULL DEFAULT 'done' COMMENT 'Oral care status',
    hair_wash ENUM('done', 'not_done') NOT NULL DEFAULT 'done' COMMENT 'Hair washing status',
    
    -- Other activities (stored as JSON or comma-separated)
    other_activities TEXT NULL COMMENT 'Other hygiene activities: haircut, nail_cut, ear_clean, shave',
    
    notes TEXT NULL COMMENT 'Additional notes',
    images TEXT NULL COMMENT 'Image file paths (JSON array)',
    
    recorded_by INT NOT NULL COMMENT 'User ID who recorded this',
    recorded_by_name VA<PERSON>HA<PERSON>(100) NOT NULL COMMENT 'Name of the person who recorded',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE,
    FOREIGN KEY (facility_id) REFERENCES facilities(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_elderly_date (elderly_id, record_date),
    INDEX idx_facility_date (facility_id, record_date),
    INDEX idx_record_date (record_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hygiene care records for elderly patients';