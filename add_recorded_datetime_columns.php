<?php
require_once 'config/database.php';

echo "Adding recorded_date and recorded_time columns to vital_signs table...\n";

// Add recorded_date column
$sql1 = "ALTER TABLE vital_signs ADD COLUMN recorded_date DATE NULL AFTER recorded_by";
if ($conn->query($sql1) === TRUE) {
    echo "Column recorded_date added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column recorded_date already exists\n";
    } else {
        echo "Error adding recorded_date: " . $conn->error . "\n";
    }
}

// Add recorded_time column
$sql2 = "ALTER TABLE vital_signs ADD COLUMN recorded_time TIME NULL AFTER recorded_date";
if ($conn->query($sql2) === TRUE) {
    echo "Column recorded_time added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column recorded_time already exists\n";
    } else {
        echo "Error adding recorded_time: " . $conn->error . "\n";
    }
}

// Also add facility_id for consistency with other tables
echo "Adding facility_id column...\n";
$sql3 = "ALTER TABLE vital_signs ADD COLUMN facility_id INT NULL AFTER elderly_id";
if ($conn->query($sql3) === TRUE) {
    echo "Column facility_id added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column facility_id already exists\n";
    } else {
        echo "Error adding facility_id: " . $conn->error . "\n";
    }
}

// Add elderly_id column if missing
echo "Adding elderly_id column...\n";
$sql4 = "ALTER TABLE vital_signs ADD COLUMN elderly_id INT NULL AFTER care_record_id";
if ($conn->query($sql4) === TRUE) {
    echo "Column elderly_id added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column elderly_id already exists\n";
    } else {
        echo "Error adding elderly_id: " . $conn->error . "\n";
    }
}

// Verify the table structure
echo "\nFinal vital_signs table structure:\n";
$result = $conn->query("DESCRIBE vital_signs");
while($row = $result->fetch_assoc()) {
    echo $row['Field'] . " - " . $row['Type'] . "\n";
}
?>