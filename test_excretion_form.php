<?php
define('AIVORA_SECURITY', true);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';

// ล็อกอินอัตโนมัติสำหรับทดสอบ
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['last_activity'] = time();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>Test Excretion Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        form { max-width: 500px; }
        label { display: block; margin: 10px 0 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #29a37e; color: white; border: none; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .radio-group { display: flex; gap: 10px; }
        .radio-group label { margin: 0; padding: 5px; }
    </style>
</head>
<body>
    <h2>ทดสอบฟอร์มบันทึกการขับถ่าย</h2>
    
    <div class="status">
        <strong>สถานะ Session:</strong> <?= isLoggedIn() ? '✓ ล็อกอินแล้ว' : '✗ ยังไม่ได้ล็อกอิน' ?><br>
        <strong>User ID:</strong> <?= $_SESSION['user_id'] ?? 'ไม่มี' ?><br>
        <strong>Username:</strong> <?= $_SESSION['username'] ?? 'ไม่มี' ?><br>
        <strong>Role:</strong> <?= $_SESSION['user_role'] ?? 'ไม่มี' ?>
    </div>

    <form id="excretionForm">
        <label>รหัสผู้สูงอายุ:</label>
        <input type="number" name="elderly_id" value="1" required>
        
        <label>วันที่บันทึก:</label>
        <input type="date" name="record_date" value="<?= date('Y-m-d') ?>" required>
        
        <div class="section">
            <h3>💧 ปัสสาวะ</h3>
            
            <label>จำนวนครั้ง:</label>
            <input type="number" name="urine_frequency" min="0" max="50" value="5">
            
            <label>ปริมาณ (ml):</label>
            <input type="number" name="urine_volume" min="0" max="5000" value="300">
            
            <label>วิธีการขับถ่าย:</label>
            <div class="radio-group">
                <label><input type="radio" name="urine_method" value="self" checked> ปัสสาวะได้ด้วยตัวเอง</label>
                <label><input type="radio" name="urine_method" value="catheter"> ใส่สายสวนปัสสาวะ</label>
                <label><input type="radio" name="urine_method" value="other"> อื่นๆ</label>
            </div>
            
            <label>ระบุวิธีอื่นๆ (ถ้าเลือกอื่นๆ):</label>
            <input type="text" name="urine_other_text" placeholder="ระบุวิธีการ">
        </div>
        
        <div class="section">
            <h3>💩 อุจจาระ</h3>
            
            <label>จำนวนครั้ง:</label>
            <input type="number" name="stool_frequency" min="0" max="20" value="1">
            
            <label>ปริมาณ (ml):</label>
            <input type="number" name="stool_volume" min="0" max="2000" value="150">
            
            <label>สีอุจจาระ:</label>
            <input type="text" name="stool_color" placeholder="เช่น น้ำตาล เหลือง ดำ เขียว" value="น้ำตาล">
            
            <label>ลักษณะอุจจาระ:</label>
            <div class="radio-group">
                <label><input type="radio" name="stool_consistency" value="normal" checked> ปกติ</label>
                <label><input type="radio" name="stool_consistency" value="liquid"> เหลว</label>
                <label><input type="radio" name="stool_consistency" value="semi_liquid"> กึ่งเหลว</label>
                <label><input type="radio" name="stool_consistency" value="hard"> แข็ง</label>
            </div>
        </div>
        
        <label>คำอธิบายเพิ่มเติม:</label>
        <textarea name="notes" placeholder="รายละเอียดเพิ่มเติม สังเกต หรือข้อสำคัญอื่นๆ">การขับถ่ายปกติ ไม่มีอาการผิดปกติ</textarea>
        
        <label>ผู้บันทึก:</label>
        <input type="text" name="recorded_by_name" value="<?= $_SESSION['username'] ?? '' ?>" readonly>
        
        <button type="submit">บันทึกการขับถ่าย</button>
    </form>
    
    <div id="result"></div>

    <script>
    document.getElementById('excretionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const resultDiv = document.getElementById('result');
        const submitBtn = this.querySelector('button[type="submit"]');
        
        submitBtn.disabled = true;
        submitBtn.textContent = 'กำลังบันทึก...';
        resultDiv.innerHTML = '<div class="status">กำลังส่งข้อมูล...</div>';
        
        fetch('api/save_excretion.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            
            if (data.success) {
                resultDiv.innerHTML = '<div class="status success">✓ ' + data.message + '</div>';
            } else {
                resultDiv.innerHTML = '<div class="status error">✗ ' + data.message + '</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = '<div class="status error">✗ เกิดข้อผิดพลาด: ' + error.message + '</div>';
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'บันทึกการขับถ่าย';
        });
    });
    </script>
</body>
</html>