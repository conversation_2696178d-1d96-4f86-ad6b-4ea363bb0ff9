<?php
/**
 * Direct test page for vital signs functionality
 */
define('AIVORA_SECURITY', true);

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database
require_once __DIR__ . '/config/database.php';

// Get sample elderly for testing
$elderly_list = [];
if (isset($conn)) {
    $elderly_result = $conn->query("SELECT id, first_name, last_name FROM elderly LIMIT 5");
    if ($elderly_result) {
        while ($row = $elderly_result->fetch_assoc()) {
            $elderly_list[] = $row;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบบันทึกสัญญาณชีพ</title>
    <style>
        body {
            font-family: 'Sarabun', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .btn {
            background: #28a745;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #218838;
        }
        .btn-debug {
            background: #6c757d;
        }
        .btn-debug:hover {
            background: #5a6268;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .alert-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🩺 ทดสอบระบบบันทึกสัญญาณชีพ</h1>
        
        <div id="status-section">
            <h3>📊 สถานะระบบ</h3>
            <p><span class="status-indicator <?= isset($conn) && !$conn->connect_error ? 'status-ok' : 'status-error' ?>"></span> 
               Database: <?= isset($conn) && !$conn->connect_error ? 'เชื่อมต่อสำเร็จ' : 'เชื่อมต่อไม่ได้' ?></p>
            <p><span class="status-indicator <?= !empty($elderly_list) ? 'status-ok' : 'status-error' ?>"></span> 
               Elderly Data: <?= !empty($elderly_list) ? count($elderly_list) . ' records found' : 'ไม่พบข้อมูล' ?></p>
            <p><span class="status-indicator <?= session_status() === PHP_SESSION_ACTIVE ? 'status-ok' : 'status-error' ?>"></span> 
               Session: <?= session_status() === PHP_SESSION_ACTIVE ? 'Active (ID: ' . session_id() . ')' : 'Inactive' ?></p>
        </div>

        <form id="vitalSignsTestForm">
            <div class="form-group">
                <label for="elderly_id">เลือกผู้สูงอายุ:</label>
                <select id="elderly_id" name="elderly_id" required>
                    <option value="">-- เลือกผู้สูงอายุ --</option>
                    <?php foreach ($elderly_list as $elderly): ?>
                        <option value="<?= $elderly['id'] ?>"><?= $elderly['first_name'] . ' ' . $elderly['last_name'] ?> (ID: <?= $elderly['id'] ?>)</option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="recorded_date">วันที่บันทึก:</label>
                    <input type="date" id="recorded_date" name="recorded_date" value="<?= date('Y-m-d') ?>" required>
                </div>
                <div class="form-group">
                    <label for="recorded_time">เวลาบันทึก:</label>
                    <input type="time" id="recorded_time" name="recorded_time" value="<?= date('H:i') ?>" required>
                </div>
            </div>

            <h3>🌡️ สัญญาณชีพ</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="temperature">อุณหภูมิ (°C):</label>
                    <input type="number" id="temperature" name="temperature" step="0.1" min="30" max="45" placeholder="36.5">
                </div>
                <div class="form-group">
                    <label for="heart_rate">อัตราการเต้นหัวใจ (bpm):</label>
                    <input type="number" id="heart_rate" name="heart_rate" min="40" max="200" placeholder="72">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="blood_pressure_systolic">ความดันตัวบน (mmHg):</label>
                    <input type="number" id="blood_pressure_systolic" name="blood_pressure_systolic" min="80" max="200" placeholder="120">
                </div>
                <div class="form-group">
                    <label for="blood_pressure_diastolic">ความดันตัวล่าง (mmHg):</label>
                    <input type="number" id="blood_pressure_diastolic" name="blood_pressure_diastolic" min="40" max="120" placeholder="80">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="respiratory_rate">อัตราการหายใจ (ครั้ง/นาที):</label>
                    <input type="number" id="respiratory_rate" name="respiratory_rate" min="8" max="40" placeholder="16">
                </div>
                <div class="form-group">
                    <label for="oxygen_saturation">ความอิ่มตัวออกซิเจน (%):</label>
                    <input type="number" id="oxygen_saturation" name="oxygen_saturation" min="70" max="100" placeholder="98">
                </div>
            </div>

            <div class="form-group">
                <label for="blood_sugar">ระดับน้ำตาลในเลือด (mg/dL):</label>
                <input type="number" id="blood_sugar" name="blood_sugar" min="50" max="500" placeholder="100">
            </div>

            <div class="form-group">
                <label for="additional_notes">บันทึกเพิ่มเติม:</label>
                <textarea id="additional_notes" name="additional_notes" rows="3" placeholder="บันทึกข้อมูลเพิ่มเติม..."></textarea>
            </div>

            <input type="hidden" name="form_mode" value="create">

            <div>
                <button type="submit" class="btn">💾 บันทึกสัญญาณชีพ</button>
                <button type="button" class="btn btn-debug" onclick="testWithDebugAPI()">🔧 ทดสอบด้วย Debug API</button>
                <button type="button" class="btn btn-debug" onclick="runSystemDebug()">🔍 ตรวจสอบระบบ</button>
            </div>
        </form>

        <div id="result-section"></div>
    </div>

    <script>
        // Test with main API
        document.getElementById('vitalSignsTestForm').addEventListener('submit', function(e) {
            e.preventDefault();
            testVitalSigns('api/save_vital_signs_simple.php');
        });

        // Test with debug API
        function testWithDebugAPI() {
            testVitalSigns('api/save_vital_signs_debug.php');
        }

        function testVitalSigns(apiEndpoint) {
            const formData = new FormData(document.getElementById('vitalSignsTestForm'));
            const resultSection = document.getElementById('result-section');
            
            // Show loading
            resultSection.innerHTML = '<div class="alert alert-info">🔄 กำลังบันทึก...</div>';
            
            fetch(apiEndpoint, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                
                if (data.success) {
                    resultSection.innerHTML = `
                        <div class="alert alert-success">
                            ✅ <strong>สำเร็จ!</strong> ${data.message}
                            <br><small>Record ID: ${data.record_id || 'N/A'}</small>
                        </div>
                    `;
                } else {
                    let debugInfo = '';
                    if (data.debug) {
                        debugInfo = '<div class="debug-info">' + JSON.stringify(data.debug, null, 2) + '</div>';
                    }
                    resultSection.innerHTML = `
                        <div class="alert alert-error">
                            ❌ <strong>ล้มเหลว:</strong> ${data.message}
                            ${debugInfo}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                resultSection.innerHTML = `
                    <div class="alert alert-error">
                        ❌ <strong>เกิดข้อผิดพลาด:</strong> ${error.message}
                    </div>
                `;
            });
        }

        function runSystemDebug() {
            const resultSection = document.getElementById('result-section');
            resultSection.innerHTML = '<div class="alert alert-info">🔄 กำลังตรวจสอบระบบ...</div>';
            
            fetch('debug_vital_signs_system.php', {
                method: 'GET',
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(data => {
                resultSection.innerHTML = `
                    <h3>🔍 ผลการตรวจสอบระบบ</h3>
                    <div class="debug-info">${data}</div>
                `;
            })
            .catch(error => {
                resultSection.innerHTML = `
                    <div class="alert alert-error">
                        ❌ <strong>เกิดข้อผิดพลาดในการตรวจสอบระบบ:</strong> ${error.message}
                    </div>
                `;
            });
        }

        // Auto-fill test data
        function fillTestData() {
            document.getElementById('temperature').value = '36.5';
            document.getElementById('heart_rate').value = '72';
            document.getElementById('blood_pressure_systolic').value = '120';
            document.getElementById('blood_pressure_diastolic').value = '80';
            document.getElementById('respiratory_rate').value = '16';
            document.getElementById('oxygen_saturation').value = '98';
            document.getElementById('blood_sugar').value = '100';
            document.getElementById('additional_notes').value = 'Test vital signs record';
        }

        // Add test data button
        window.onload = function() {
            const form = document.getElementById('vitalSignsTestForm');
            const testButton = document.createElement('button');
            testButton.type = 'button';
            testButton.className = 'btn btn-debug';
            testButton.textContent = '📝 กรอกข้อมูลทดสอบ';
            testButton.onclick = fillTestData;
            form.querySelector('div').appendChild(testButton);
        };
    </script>
</body>
</html>