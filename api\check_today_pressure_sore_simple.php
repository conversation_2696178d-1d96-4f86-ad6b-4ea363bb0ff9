<?php
// API ง่ายๆ สำหรับตรวจสอบแผลกดทับวันนี้ โดยไม่ต้องเช็ค session ซับซ้อน
session_start();
header('Content-Type: application/json; charset=utf-8');

// กำหนดค่าคงที่สำหรับความปลอดภัย
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

try {
    // เชื่อมต่อฐานข้อมูล
    require_once __DIR__ . '/../config/database_simple.php';
    
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้: ' . (isset($conn) ? $conn->connect_error : 'Connection not established'));
    }
    
    // รับพารามิเตอร์
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;

    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }

    // ตรวจสอบว่าตาราง care_pressure_sores มีอยู่หรือไม่
    $table_check = $conn->query("SHOW TABLES LIKE 'care_pressure_sores'");
    if ($table_check->num_rows == 0) {
        // ยังไม่มีตาราง - ส่งกลับว่าไม่มีข้อมูล
        echo json_encode([
            'success' => true,
            'has_today_record' => false,
            'mode' => 'create',
            'message' => 'ยังไม่มีตารางข้อมูล - สร้างใหม่'
        ]);
        exit();
    }

    // ตรวจสอบว่าวันนี้มีการบันทึกแล้วหรือไม่ - ใช้ตาราง care_pressure_sores
    $today = date('Y-m-d');
    $sql = "SELECT ps.*, COALESCE(u.name, u.username, 'ระบบ') as user_name 
            FROM care_pressure_sores ps 
            LEFT JOIN users u ON ps.user_id = u.id 
            WHERE ps.elderly_id = ? AND DATE(ps.recorded_at) = ?
            ORDER BY ps.recorded_at DESC 
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL']);
        exit();
    }

    $stmt->bind_param("is", $elderly_id, $today);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // มีข้อมูลวันนี้แล้ว - แสดงข้อมูลสำหรับการแก้ไข
        $pressure_sore_record = $result->fetch_assoc();
        
        // แปลง recorded_at เป็น date และ time
        $recorded_datetime = $pressure_sore_record['recorded_at'];
        $recorded_date = date('Y-m-d', strtotime($recorded_datetime));
        $recorded_time = date('H:i:s', strtotime($recorded_datetime));
        
        echo json_encode([
            'success' => true,
            'has_today_record' => true,
            'mode' => 'edit',
            'data' => [
                'id' => $pressure_sore_record['id'],
                'recorded_date' => $recorded_date,
                'recorded_time' => $recorded_time,
                'sore_location' => $pressure_sore_record['sore_location'],
                'sore_stage' => $pressure_sore_record['sore_stage'],
                'sore_size_length' => $pressure_sore_record['sore_size_length'],
                'sore_size_width' => $pressure_sore_record['sore_size_width'],
                'sore_size_depth' => $pressure_sore_record['sore_size_depth'],
                'wound_condition' => $pressure_sore_record['wound_condition'],
                'surrounding_skin' => $pressure_sore_record['surrounding_skin'],
                'drainage_type' => $pressure_sore_record['drainage_type'],
                'drainage_amount' => $pressure_sore_record['drainage_amount'],
                'pain_level' => $pressure_sore_record['pain_level'],
                'treatment_applied' => $pressure_sore_record['treatment_applied'],
                'dressing_type' => $pressure_sore_record['dressing_type'],
                'notes' => $pressure_sore_record['notes'],
                'recorded_by_name' => $pressure_sore_record['user_name'],
                'created_at' => $pressure_sore_record['created_at']
            ]
        ]);
    } else {
        // ยังไม่มีข้อมูลวันนี้ - โหมดเพิ่มใหม่
        echo json_encode([
            'success' => true,
            'has_today_record' => false,
            'mode' => 'create'
        ]);
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in check_today_pressure_sore_simple.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'เกิดข้อผิดพลาดระบบ: ' . $e->getMessage(),
        'mode' => 'create' // ถ้า error ให้ใช้โหมดสร้างใหม่
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>