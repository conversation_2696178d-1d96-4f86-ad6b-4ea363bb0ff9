<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}

// เริ่ม session ด้วยการใช้ SecurityManager เหมือน auth.php
require_once __DIR__ . '/../includes/security.php';
SecurityManager::secureSession();

// ตรวจสอบสิทธิ์การเข้าถึงก่อนโหลดอะไรอื่น
if (!isset($_SESSION['user_role']) || !in_array($_SESSION['user_role'], ['admin', 'facility_admin', 'staff'])) {
    // ถ้าไม่มี session ให้ redirect ไปหน้า login
    if (!isset($_SESSION['user_id'])) {
        echo '<script>alert("กรุณาเข้าสู่ระบบก่อนใช้งาน"); window.location.href = "index.php";</script>';
        exit();
    }
    header('Location: ?page=access_denied');
    exit();
}

// ตรวจสอบว่า session ยังไม่หมดอายุ
require_once __DIR__ . '/../includes/auth.php';
if (!isLoggedIn()) {
    echo '<script>alert("Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่"); window.location.href = "index.php";</script>';
    exit();
}

// Update last activity to keep session alive (matches API behavior)
$_SESSION['last_activity'] = time();

// รับค่า ID ผู้สูงอายุและตรวจสอบก่อนโหลดอะไรอื่น
$elderly_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($elderly_id <= 0) {
    header('Location: ?page=elderly');
    exit();
}

// เชื่อมต่อฐานข้อมูลหลังจากตรวจสอบทุกอย่างแล้ว
global $conn;
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// debug ตัวแปร $conn
if (!isset($conn) || !$conn || $conn->connect_error) {
    die("ไม่สามารถเชื่อมต่อฐานข้อมูลได้: " . (isset($conn) && $conn->connect_error ? $conn->connect_error : 'ตัวแปร conn ไม่มีหรือเชื่อมต่อไฟล์ได้'));
}

// ตรวจสอบ role ของผู้ใช้และกำหนดค่า UI
$isAdmin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
$isFacilityAdmin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'facility_admin';

// สำหรับ facility_admin และ staff ให้ตรวจสอบว่าผู้สูงอายุอยู่ในสถานพยาบาลเดียวกันหรือไม่
$facility_check = "";
$params = [$elderly_id];
$types = "i";

if (!$isAdmin) {
    $facility_check = " AND e.facility_id = ?";
    $params[] = $_SESSION['facility_id'];
    $types = "ii";
}

// ดึงข้อมูลผู้สูงอายุ
$sql = "SELECT e.*, f.facility_name, f.facility_code, r.room_number, r.room_type
        FROM elderly e
        LEFT JOIN facilities f ON e.facility_id = f.id
        LEFT JOIN rooms r ON e.room_id = r.id
        WHERE e.id = ?" . $facility_check;

$stmt = $conn->prepare($sql);
if (!$stmt) {
    die("เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: " . $conn->error);
}

$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    // Use JavaScript redirect instead of header to avoid headers already sent error
    echo '<script>window.location.href = "?page=elderly";</script>';
    exit();
}

$elderly = $result->fetch_assoc();

// คำนวณอายุ
$age = '';
if (!empty($elderly['birth_date'])) {
    $birth = new DateTime($elderly['birth_date']);
    $now = new DateTime();
    $age = $now->diff($birth)->y;
}

// Get profile image
$profile_image = null;
$supported_ext = ['jpg', 'jpeg', 'png', 'gif'];

$avatar_image_path = "assets/img/elderly/" . $elderly['id'] . "/avatar.";
foreach ($supported_ext as $ext) {
    if (file_exists($avatar_image_path . $ext)) {
        $profile_image = $avatar_image_path . $ext;
        break;
    }
}

if (!$profile_image) {
    $profile_image_path = "assets/img/elderly/" . $elderly['id'] . "/profile.";
    foreach ($supported_ext as $ext) {
        if (file_exists($profile_image_path . $ext)) {
            $profile_image = $profile_image_path . $ext;
            break;
        }
    }
}

// Default avatar if no image found
if (!$profile_image) {
    $profile_image = "https://i.imgur.com/T5Y4R5x.png"; // Default avatar
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aivora - Patient Dashboard</title>
    <style>
        /* General Styles */
        :root {
            --primary-green: #29a37e;
            --dark-green: #0d3830;
            --background-color: #f0f4f3;
            --card-background: #ffffff;
            --text-primary: #333333;
            --text-secondary: #6c757d;
            --border-color: #e9ecef;
            --high-risk-red: #dc3545;
            --active-status-green: #d4edda;
            --active-status-text: #155724;
            --mobility-issues-bg: #e2e3e5;
            --mobility-issues-text: #383d41;
        }

        @import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Sarabun', sans-serif;
            margin: 0;
            background-color: var(--background-color);
            color: var(--text-primary);
        }

        /* Main Layout */
        .dashboard-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .main-header {
            background-color: var(--dark-green);
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .logo::before {
            content: '❄'; /* Placeholder for snowflake icon */
            font-size: 1.8rem;
            color: var(--primary-green);
        }

        .breadcrumbs {
            color: #bdc3c7;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .nursing-home-selector {
            background: none;
            border: 1px solid #7f8c8d;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
        }

        .main-content {
            display: flex;
            flex: 1;
            padding: 25px;
            gap: 25px;
        }

        .patient-main-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        /* Cards */
        .card {
            background-color: var(--card-background);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        /* Header Card */
        .patient-header-card {
            display: flex;
            gap: 20px;
            align-items: stretch;
            padding: 0;
        }
        
        .patient-info-container {
            flex: 3;
            background-color: var(--card-background);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .actions-container {
             display: flex;
             flex-direction: column;
             gap: 10px;
             align-self: stretch;
             padding: 0;
             flex: 1;
             min-width: 200px;
        }

        .action-btn {
            background-color: var(--card-background);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 15px 20px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            flex: 1;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            border-color: var(--primary-green);
        }
        
        .action-btn.emergency {
            background-color: var(--high-risk-red);
            color: white;
            border: none;
        }
        
        .action-btn.emergency:hover {
            background-color: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
        
        .action-btn.emergency::before { content: '◉'; } /* Simple circle for icon */
        .action-btn.add-note::before { content: '+'; }

        .patient-details {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .patient-avatar {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            object-fit: cover;
        }

        .patient-name-info h1 {
            margin: 0;
            font-size: 1.8rem;
            color: var(--text-primary);
        }

        .patient-name-info p {
            margin: 5px 0 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .qr-code {
            width: 60px;
            height: 60px;
            margin-left: auto;
            border: 1px solid var(--border-color);
            padding: 4px;
            border-radius: 4px;
        }

        /* Patient Details Icons */
        .patient-details-icons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
            gap: 15px;
            margin: 20px 0;
            padding: 15px;
            background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
            border-radius: 12px;
            border: 1px solid rgba(41, 163, 126, 0.1);
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 10px 5px;
            border-radius: 8px;
        }
        
        .icon-item:hover {
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 0.8);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .icon-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }
        
        .icon-item:hover .icon-circle {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25);
        }
        
        .icon-circle .icon {
            font-size: 1.4rem;
            filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.2));
        }
        
        .icon-label {
            font-size: 0.7rem;
            text-align: center;
            color: var(--text-secondary);
            font-weight: 500;
            line-height: 1.2;
            max-width: 60px;
        }

        .patient-status-tabs {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        .status-tab {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .status-tab.overview {
            background-color: var(--dark-green);
            color: white;
        }
        
        .status-tab.high-risk {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        /* Main Nav */
        .patient-nav {
            display: flex;
            gap: 6px;
            border-bottom: 2px solid var(--border-color);
            padding: 15px 0;
            background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
            border-radius: 12px 12px 0 0;
            margin: -20px -20px 20px -20px;
            padding: 15px 15px;
            flex-wrap: nowrap;
            overflow-x: auto;
        }
        
        .patient-nav a {
            text-decoration: none;
            color: var(--text-secondary);
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.06);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .patient-nav a::before {
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .patient-nav a:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(41, 163, 126, 0.15);
            border-color: var(--primary-green);
            color: var(--primary-green);
        }
        
        .patient-nav a.active {
            color: white;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
            border-color: var(--primary-green);
            box-shadow: 0 4px 15px rgba(41, 163, 126, 0.3);
        }
        
        .patient-nav a.active::before {
            color: white;
        }
        
        /* Navigation Icons */
        .patient-nav a[href="#"]:nth-child(1)::before { content: "👤"; } /* โปรไฟล์ */
        .patient-nav a[href="#"]:nth-child(2)::before { content: "📋"; } /* บันทึกการดูแล */
        .patient-nav a[href="#"]:nth-child(3)::before { content: "🏥"; } /* บันทึกแผล */
        .patient-nav a[href="#"]:nth-child(4)::before { content: "📊"; } /* ข้อมูลผู้สูงอายุ */
        .patient-nav a[href="#"]:nth-child(5)::before { content: "💚"; } /* ประเมินสุขภาพ */
        .patient-nav a[href="#"]:nth-child(6)::before { content: "💊"; } /* ยา */
        .patient-nav a[href="#"]:nth-child(7)::before { content: "👨‍👩‍👧‍👦"; } /* ครอบครัว */
        .patient-nav a[href="#"]:nth-child(8)::before { content: "🏨"; } /* โรงพยาบาล */
        .patient-nav a[href="#"]:nth-child(9)::before { content: "⚠️"; } /* รายงานเหตุการณ์ */

        /* Overview Section */
        .overview-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .overview-section .card {
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .overview-section .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--primary-green);
        }
        
        .overview-section .card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: var(--text-secondary);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .overview-section .card h3::before {
            font-size: 1.2rem;
        }
        
        .overview-section .card:first-child h3::before {
            content: "🎯";
        }
        
        .overview-section .card:last-child h3::before {
            content: "🔔";
        }

        .risk-level-card .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--high-risk-red);
            text-shadow: 2px 2px 4px rgba(220, 53, 69, 0.2);
        }
        
        .alerts-card .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            text-shadow: 2px 2px 4px rgba(51, 51, 51, 0.2);
        }

        /* Vitals & Care Plan */
        .vitals-careplan-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .vitals-card, .careplan-card {
            position: relative;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
        }
        
        .vitals-card:hover, .careplan-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border-color: var(--primary-green);
        }

        .vitals-card h3, .careplan-card h3 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--dark-green);
        }
        
        .vitals-card h3::before {
            content: "💓";
            font-size: 1.2rem;
        }
        
        .careplan-card h3::before {
            content: "📝";
            font-size: 1.2rem;
        }
        
        .vitals-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .vitals-list li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }
        
        .vitals-list li:last-child {
            border-bottom: none;
        }
        
        .vitals-list li::before {
            margin-right: 8px;
            font-size: 1rem;
        }
        
        .vitals-list li:nth-child(1)::before { content: "💖"; } /* Heart Rate */
        .vitals-list li:nth-child(2)::before { content: "🩸"; } /* Blood Pressure */
        .vitals-list li:nth-child(3)::before { content: "🫁"; } /* Respiration */
        .vitals-list li:nth-child(4)::before { content: "🌡️"; } /* Temperature */
        
        .vitals-list .label {
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .vitals-list .value {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--primary-green);
            text-shadow: 1px 1px 2px rgba(41, 163, 126, 0.1);
        }

        .careplan-card .value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-green);
            padding: 15px;
            background: rgba(41, 163, 126, 0.1);
            border-radius: 8px;
            border-left: 4px solid var(--primary-green);
        }

        /* Tab functionality */
        .tab-content .tab-pane {
            display: none;
        }
        
        .tab-content .tab-pane.active {
            display: block;
        }

        /* Calendar Styles */
        .calendar-container {
            padding: 20px;
            background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
            border-radius: 12px;
            border: 1px solid rgba(41, 163, 126, 0.1);
        }

        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .calendar-nav-btn {
            background: var(--primary-green);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .calendar-nav-btn:hover {
            background: var(--darker-green);
            transform: scale(1.1);
        }

        .calendar-title {
            margin: 0;
            font-size: 1.2rem;
            color: var(--text-primary);
            font-weight: 600;
        }

        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
        }

        .weekday-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.6);
            border: 2px solid transparent;
        }

        .weekday-item:hover {
            background: rgba(41, 163, 126, 0.1);
            transform: translateY(-2px);
        }

        .weekday-item.active {
            background: var(--primary-green);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(41, 163, 126, 0.3);
        }

        .weekday-label {
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 4px;
            opacity: 0.8;
        }

        .weekday-date {
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* Care Records Grid */
        .care-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .care-type-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 15px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
            border-radius: 12px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .care-type-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
            border-color: var(--primary-green);
        }

        .care-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .care-type-item span {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        /* Existing Records Styles */
        .existing-records-section {
            margin-top: 30px;
        }

        .record-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .detail-item {
            display: inline-block;
            background: rgba(41, 163, 126, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.85rem;
            color: var(--text-primary);
            border: 1px solid rgba(41, 163, 126, 0.2);
        }

        .notes {
            width: 100%;
            background: rgba(255, 255, 255, 0.8);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            color: var(--text-secondary);
            border-left: 3px solid var(--primary-green);
            margin-top: 8px;
        }

        .record-type-section {
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.5);
        }

        /* Charts */
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        
        .chart-card h3 {
             margin-top: 0;
             margin-bottom: 20px;
        }
        
        .chart-placeholder {
            width: 100%;
            height: 150px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .heart-rate-chart {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 100"><path d="M0,50 C20,80 40,30 60,60 S100,20 120,50 S160,90 180,60 S220,10 240,40 S280,80 300,50" stroke="%2329a37e" fill="none" stroke-width="2"/><line x1="0" y1="85" x2="300" y2="85" stroke="%23e0e0e0" stroke-width="1"/><text x="5" y="15" font-family="sans-serif" font-size="10" fill="%236c757d">100</text><text x="5" y="48" font-family="sans-serif" font-size="10" fill="%236c757d">60</text><text x="5" y="81" font-family="sans-serif" font-size="10" fill="%236c757d">0</text></svg>');
        }
        
        .blood-pressure-chart {
             background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 100"><path d="M0,30 C50,25 100,35 150,32 S250,40 300,38" stroke="%2329a37e" fill="none" stroke-width="2"/><path d="M0,70 C50,75 100,68 150,72 S250,65 300,68" stroke="%2329a37e" fill="none" stroke-width="2"/><line x1="0" y1="15" x2="300" y2="15" stroke="%23e0e0e0" stroke-width="1"/><line x1="0" y1="50" x2="300" y2="50" stroke="%23e0e0e0" stroke-width="1"/><line x1="0" y1="85" x2="300" y2="85" stroke="%23e0e0e0" stroke-width="1"/><text x="5" y="15" font-family="sans-serif" font-size="10" fill="%236c757d">150</text><text x="5" y="48" font-family="sans-serif" font-size="10" fill="%236c757d">90</text><text x="5" y="81" font-family="sans-serif" font-size="10" fill="%236c757d">40</text></svg>');
        }
        
        /* Footer */
        .footer-bar {
            background-color: var(--dark-green);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            margin-top: auto; /* Push to bottom if content is short */
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        
        /* Mobile Dropdown Navigation */
        .mobile-nav-dropdown {
            display: none;
            position: relative;
        }
        
        .mobile-nav-button {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            box-shadow: 0 2px 10px rgba(41, 163, 126, 0.3);
        }
        
        .mobile-nav-button::after {
            content: "▼";
            transition: transform 0.3s ease;
        }
        
        .mobile-nav-button.active::after {
            transform: rotate(180deg);
        }
        
        .mobile-nav-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            margin-top: 8px;
        }
        
        .mobile-nav-menu.show {
            max-height: 400px;
        }
        
        .mobile-nav-menu a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--text-secondary);
            text-decoration: none;
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .mobile-nav-menu a:last-child {
            border-bottom: none;
        }
        
        .mobile-nav-menu a:hover {
            background: var(--background-color);
            color: var(--primary-green);
        }
        
        .mobile-nav-menu a.active {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
            color: white;
        }
        
        .mobile-nav-menu a::before {
            font-size: 1.1rem;
        }
        
        .mobile-nav-menu a:nth-child(1)::before { content: "👤"; }
        .mobile-nav-menu a:nth-child(2)::before { content: "📋"; }
        .mobile-nav-menu a:nth-child(3)::before { content: "🏥"; }
        .mobile-nav-menu a:nth-child(4)::before { content: "📊"; }
        .mobile-nav-menu a:nth-child(5)::before { content: "💚"; }
        .mobile-nav-menu a:nth-child(6)::before { content: "💊"; }
        .mobile-nav-menu a:nth-child(7)::before { content: "👨‍👩‍👧‍👦"; }
        .mobile-nav-menu a:nth-child(8)::before { content: "🏨"; }
        .mobile-nav-menu a:nth-child(9)::before { content: "⚠️"; }

         /* Responsive */
        @media (max-width: 768px) {
            .main-header {
                flex-direction: column;
                gap: 15px;
            }
            .patient-header-card {
                flex-direction: column;
            }
            .actions-container {
                align-self: flex-start;
                padding-top: 0;
            }
            .patient-details-icons {
                grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
                gap: 10px;
                padding: 10px;
            }
            .icon-circle {
                width: 40px;
                height: 40px;
            }
            .icon-circle .icon {
                font-size: 1.1rem;
            }
            .icon-label {
                font-size: 0.65rem;
                max-width: 50px;
            }
            .overview-section, .charts-section, .vitals-careplan-section {
                grid-template-columns: 1fr;
            }
            
            /* Hide desktop navigation on mobile */
            .patient-nav {
                display: none;
            }
            
            /* Show mobile dropdown on mobile */
            .mobile-nav-dropdown {
                display: block;
                margin: -20px -20px 20px -20px;
                padding: 20px;
                background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
                border-radius: 12px 12px 0 0;
                border-bottom: 2px solid var(--border-color);
            }
        }

    </style>
</head>
<body>
    <div class="dashboard-container">
        <header class="main-header">
            <div class="header-left">
                <div class="logo">AIVORA</div>
                <div class="breadcrumbs">ผู้สูงอายุ > <?php echo htmlspecialchars($elderly['first_name'] . ' ' . $elderly['last_name']); ?></div>
            </div>
            <div class="header-right">
                <?php if (isset($_SESSION['facility_name'])): ?>
                <span class="nursing-home-selector" style="border: none; background: rgba(255,255,255,0.1);">
                    <?php echo htmlspecialchars($_SESSION['facility_name']); ?>
                </span>
                <?php else: ?>
                <select class="nursing-home-selector">
                    <option>Nursing Home</option>
                </select>
                <?php endif; ?>
            </div>
        </header>

        <main class="main-content">
            <div class="patient-main-column">
                
                <div class="patient-header-card">
                    <div class="patient-info-container">
                        <div class="patient-details">
                            <img src="<?php echo htmlspecialchars($profile_image); ?>" alt="<?php echo htmlspecialchars($elderly['first_name'] . ' ' . $elderly['last_name']); ?>" class="patient-avatar">
                            <div class="patient-name-info">
                                <h1><?php echo htmlspecialchars($elderly['first_name'] . ' ' . $elderly['last_name']); ?></h1>
                                <p>อายุ <?php echo $age ? $age : '-'; ?> ปี , ห้อง , เตียง</p>
                            </div>
                            <img src="https://i.imgur.com/yN9V3N7.png" alt="QR Code" class="qr-code">
                        </div>
                        
                        <!-- Mock Layout: Patient Details Icons Section -->
                        <div class="patient-details-icons">
                            <div class="icon-item" data-category="line-report">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #00C300 0%, #009900 100%);">
                                    <span class="icon">💬</span>
                                </div>
                                <span class="icon-label">ส่งรายงาน<br>LINE</span>
                            </div>
                            
                            <div class="icon-item" data-category="vital-signs" onclick="openVitalSignsModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #FF69B4 0%, #E91E63 100%);">
                                    <span class="icon">💓</span>
                                </div>
                                <span class="icon-label">สัญญาณ<br>ชีพ</span>
                            </div>
                            
                            <div class="icon-item" data-category="turning" onclick="openTurningModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">🔄</span>
                                </div>
                                <span class="icon-label">การพลิก<br>ตัว</span>
                            </div>
                            
                            <div class="icon-item" data-category="pressure-sores" onclick="openPressureSoreModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);">
                                    <span class="icon">🔴</span>
                                </div>
                                <span class="icon-label">แผลกด<br>ทับ</span>
                            </div>
                            
                            <div class="icon-item" data-category="weight-height" onclick="openWeightHeightModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #795548 0%, #5D4037 100%);">
                                    <span class="icon">⚖️</span>
                                </div>
                                <span class="icon-label">น้ำหนัก<br>ส่วนสูง</span>
                            </div>
                            
                            <div class="icon-item" data-category="incident-report" onclick="openIncidentReportModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">⚠️</span>
                                </div>
                                <span class="icon-label">รายงาน<br>เหตุการณ์</span>
                            </div>
                            
                            <div class="icon-item" data-category="medication" onclick="openMedicationModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);">
                                    <span class="icon">💊</span>
                                </div>
                                <span class="icon-label">บันทึก<br>การให้ยา</span>
                            </div>
                            
                            <div class="icon-item" data-category="symptoms" onclick="openSymptomsModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);">
                                    <span class="icon">📋</span>
                                </div>
                                <span class="icon-label">บันทึก<br>อาการ</span>
                            </div>
                            
                            <div class="icon-item" data-category="excretion" onclick="openExcretionModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #8BC34A 0%, #689F38 100%);">
                                    <span class="icon">🚽</span>
                                </div>
                                <span class="icon-label">การขับ<br>ถ่าย</span>
                            </div>
                            
                            <div class="icon-item" data-category="hygiene" onclick="openHygieneModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);">
                                    <span class="icon">🧼</span>
                                </div>
                                <span class="icon-label">สุขอนามัย</span>
                            </div>
                            
                            <div class="icon-item" data-category="feeding" onclick="openFeedingModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #FFC107 0%, #FF8F00 100%);">
                                    <span class="icon">🍽️</span>
                                </div>
                                <span class="icon-label">ทาน/ฟีด<br>อาหาร</span>
                            </div>
                            
                            <div class="icon-item" data-category="activities" onclick="openActivitiesModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">🏃‍♀️</span>
                                </div>
                                <span class="icon-label">กิจกรรม</span>
                            </div>
                            
                            <div class="icon-item" data-category="sputum" onclick="openSputumModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #CDDC39 0%, #AFB42B 100%);">
                                    <span class="icon">🫁</span>
                                </div>
                                <span class="icon-label">บันทึก<br>เสมหะ</span>
                            </div>
                            
                            <div class="icon-item" data-category="mental-state" onclick="openMentalStateModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #3F51B5 0%, #303F9F 100%);">
                                    <span class="icon">😊</span>
                                </div>
                                <span class="icon-label">สภาวะจิตใจ<br>อารมณ์</span>
                            </div>
                            
                            <div class="icon-item" data-category="sleep" onclick="openSleepModal()">
                                <div class="icon-circle" style="background: linear-gradient(135deg, #673AB7 0%, #512DA8 100%);">
                                    <span class="icon">💤</span>
                                </div>
                                <span class="icon-label">การนอน<br>หลับ</span>
                            </div>
                        </div>
                        
                        <div class="patient-status-tabs">
                            <span class="status-tab overview">● Overview</span>
                            <span class="status-tab high-risk">▲ High</span>
                        </div>
                    </div>
                    <div class="actions-container">
                        <a href="?page=edit_elderly&id=<?php echo $elderly['id']; ?>" class="action-btn">Edit</a>
                        <button class="action-btn add-note" onclick="addNote(<?php echo $elderly['id']; ?>)">Add Note</button>
                        <button class="action-btn emergency" onclick="emergency(<?php echo $elderly['id']; ?>)">Emergency</button>
                    </div>
                </div>

                <div class="card">
                    <!-- Desktop Navigation -->
                    <nav class="patient-nav">
                        <a href="#" class="nav-tab active" data-tab="profile">โปรไฟล์</a>
                        <a href="#" class="nav-tab" data-tab="care-records">บันทึกการดูแล</a>
                        <a href="#" class="nav-tab" data-tab="wound-records">บันทึกแผล</a>
                        <a href="#" class="nav-tab" data-tab="elderly-info">ข้อมูลผู้สูงอายุ</a>
                        <a href="#" class="nav-tab" data-tab="health-assessment">ประเมินสุขภาพ</a>
                        <a href="#" class="nav-tab" data-tab="medications">ยา</a>
                        <a href="#" class="nav-tab" data-tab="family">ครอบครัว</a>
                        <a href="#" class="nav-tab" data-tab="hospital">โรงพยาบาล</a>
                        <a href="#" class="nav-tab" data-tab="incident-reports">รายงานเหตุการณ์</a>
                    </nav>
                    
                    <!-- Mobile Dropdown Navigation -->
                    <div class="mobile-nav-dropdown">
                        <button class="mobile-nav-button" onclick="toggleMobileNav()">
                            <span>👤 โปรไฟล์</span>
                        </button>
                        <div class="mobile-nav-menu" id="mobileNavMenu">
                            <a href="#" class="nav-tab active" data-tab="profile">โปรไฟล์</a>
                            <a href="#" class="nav-tab" data-tab="care-records">บันทึกการดูแล</a>
                            <a href="#" class="nav-tab" data-tab="wound-records">บันทึกแผล</a>
                            <a href="#" class="nav-tab" data-tab="elderly-info">ข้อมูลผู้สูงอายุ</a>
                            <a href="#" class="nav-tab" data-tab="health-assessment">ประเมินสุขภาพ</a>
                            <a href="#" class="nav-tab" data-tab="medications">ยา</a>
                            <a href="#" class="nav-tab" data-tab="family">ครอบครัว</a>
                            <a href="#" class="nav-tab" data-tab="hospital">โรงพยาบาล</a>
                            <a href="#" class="nav-tab" data-tab="incident-reports">รายงานเหตุการณ์</a>
                        </div>
                    </div>

                    <!-- Tab Content Areas -->
                    <div class="tab-content">
                        <!-- Profile Tab -->
                        <div id="profile-tab" class="tab-pane active" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">Overview</h2>
                            <div class="overview-section">
                                <div class="card">
                                    <h3>▲ ระดับความเสี่ยง</h3>
                                    <div class="risk-level-card">
                                        <span class="value">ปกติ</span>
                                    </div>
                                </div>
                                <div class="card">
                                    <h3>การแจ้งเตือน</h3>
                                    <div class="alerts-card">
                                         <span class="value" id="alerts-count">0 รายการ</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Care Records Tab -->
                        <div id="care-records-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">บันทึกการดูแล</h2>
                            
                            <!-- Calendar Navigation -->
                            <div class="calendar-container card" style="margin-bottom: 20px;">
                                <div class="calendar-header">
                                    <button class="calendar-nav-btn" id="prevWeek">&#8249;</button>
                                    <h3 class="calendar-title" id="calendarTitle">ส.ค. 18 - 24</h3>
                                    <button class="calendar-nav-btn" id="nextWeek">&#8250;</button>
                                </div>
                                <div class="calendar-weekdays">
                                    <div class="weekday-item">
                                        <div class="weekday-label">อ.</div>
                                        <div class="weekday-date" data-date="2024-08-18">18</div>
                                    </div>
                                    <div class="weekday-item active">
                                        <div class="weekday-label">อ.</div>
                                        <div class="weekday-date" data-date="2024-08-19">19</div>
                                    </div>
                                    <div class="weekday-item">
                                        <div class="weekday-label">พ.</div>
                                        <div class="weekday-date" data-date="2024-08-20">20</div>
                                    </div>
                                    <div class="weekday-item">
                                        <div class="weekday-label">พฤ.</div>
                                        <div class="weekday-date" data-date="2024-08-21">21</div>
                                    </div>
                                    <div class="weekday-item">
                                        <div class="weekday-label">ศ.</div>
                                        <div class="weekday-date" data-date="2024-08-22">22</div>
                                    </div>
                                    <div class="weekday-item">
                                        <div class="weekday-label">ส.</div>
                                        <div class="weekday-date" data-date="2024-08-23">23</div>
                                    </div>
                                    <div class="weekday-item">
                                        <div class="weekday-label">อา.</div>
                                        <div class="weekday-date" data-date="2024-08-24">24</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Care Records for Selected Date -->
                            <div class="care-records-content">
                                <div id="selectedDateRecords">
                                    <h4 style="color: var(--text-secondary); margin-bottom: 15px;">
                                        บันทึกการดูแล วันที่ <span id="selectedDateDisplay">19 สิงหาคม 2567</span>
                                    </h4>

                                    <!-- Existing Records for Selected Date -->
                                    <div class="existing-records-section">
                                        <h4 style="color: var(--text-secondary); margin-bottom: 15px; border-bottom: 2px solid var(--border-color); padding-bottom: 8px;">
                                            บันทึกที่มีอยู่
                                        </h4>
                                        <div id="existingRecordsContainer">
                                            <div class="loading-message" id="recordsLoading" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                                                <i class="fas fa-spinner fa-spin"></i> กำลังโหลดข้อมูล...
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Wound Records Tab -->
                        <div id="wound-records-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">บันทึกแผล</h2>
                            <div id="wound-records-content">
                                <div class="loading-message" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                                    กำลังโหลดข้อมูลบันทึกแผล...
                                </div>
                            </div>
                        </div>

                        <!-- Elderly Info Tab -->
                        <div id="elderly-info-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">ข้อมูลผู้สูงอายุ</h2>
                            <div id="elderly-info-content">
                                <div class="card">
                                    <h3>ข้อมูลส่วนตัว</h3>
                                    <div class="info-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                        <div class="info-item">
                                            <label>ชื่อ-นามสกุล:</label>
                                            <span><?php echo htmlspecialchars($elderly['first_name'] . ' ' . $elderly['last_name']); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <label>วันเกิด:</label>
                                            <span><?php echo $elderly['birth_date'] ? date('d/m/Y', strtotime($elderly['birth_date'])) : '-'; ?></span>
                                        </div>
                                        <div class="info-item">
                                            <label>อายุ:</label>
                                            <span><?php echo $age ? $age . ' ปี' : '-'; ?></span>
                                        </div>
                                        <div class="info-item">
                                            <label>เพศ:</label>
                                            <span><?php echo htmlspecialchars($elderly['gender'] ?? '-'); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <label>สถานพยาบาล:</label>
                                            <span><?php echo htmlspecialchars($elderly['facility_name'] ?? '-'); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <label>ห้อง:</label>
                                            <span><?php echo htmlspecialchars($elderly['room_number'] ?? '-'); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Health Assessment Tab -->
                        <div id="health-assessment-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">ประเมินสุขภาพ</h2>
                            <div id="health-assessment-content">
                                <div class="loading-message" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                                    กำลังโหลดข้อมูลการประเมินสุขภาพ...
                                </div>
                            </div>
                        </div>

                        <!-- Medications Tab -->
                        <div id="medications-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">ยา</h2>
                            <div id="medications-content">
                                <div class="loading-message" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                                    กำลังโหลดข้อมูลยา...
                                </div>
                            </div>
                        </div>

                        <!-- Family Tab -->
                        <div id="family-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">ครอบครัว</h2>
                            <div id="family-content">
                                <div class="card">
                                    <h3>ข้อมูลผู้ติดต่อ</h3>
                                    <p style="color: var(--text-secondary);">ข้อมูลครอบครัวและผู้ติดต่อฉุกเฉิน</p>
                                    <div class="loading-message" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                                        กำลังโหลดข้อมูลครอบครัว...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hospital Tab -->
                        <div id="hospital-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">โรงพยาบาล</h2>
                            <div id="hospital-content">
                                <div class="card">
                                    <h3>ประวัติการรักษา</h3>
                                    <div class="loading-message" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                                        กำลังโหลดข้อมูลโรงพยาบาล...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Incident Reports Tab -->
                        <div id="incident-reports-tab" class="tab-pane" style="margin-top: 20px;">
                            <h2 style="font-size: 1.4rem; margin-bottom: 20px;">รายงานเหตุการณ์</h2>
                            <div id="incident-reports-content">
                                <div class="loading-message" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                                    กำลังโหลดข้อมูลรายงานเหตุการณ์...
                                </div>
                            </div>
                        </div>
                    </div>
                
                 <div class="footer-bar">
                    <span>Saus run</span>
                    <span>&gt;</span>
                </div>
            </div>

        </main>
    </div>

    <?php include __DIR__ . '/../includes/vital_signs_modal.php'; ?>
    <?php include __DIR__ . '/../includes/turning_modal.php'; ?>
    <?php include __DIR__ . '/../includes/pressure_sore_modal.php'; ?>
    <?php include __DIR__ . '/../includes/weight_height_modal.php'; ?>
    <?php include __DIR__ . '/../includes/incident_report_modal.php'; ?>
    <?php include __DIR__ . '/../includes/medication_modal.php'; ?>
    <?php include __DIR__ . '/../includes/symptoms_modal.php'; ?>
    <?php include __DIR__ . '/../includes/excretion_modal.php'; ?>
    <?php include __DIR__ . '/../includes/hygiene_modal.php'; ?>
    <?php include __DIR__ . '/../includes/feeding_modal.php'; ?>
    <?php include __DIR__ . '/../includes/activities_modal.php'; ?>
    <?php include __DIR__ . '/../includes/sputum_modal.php'; ?>
    <?php include __DIR__ . '/../includes/mental_state_modal.php'; ?>
    <?php include __DIR__ . '/../includes/sleep_modal.php'; ?>

    <script>
    function addNote(elderlyId) {
        alert('Add Note functionality for elderly ID: ' + elderlyId);
    }

    function emergency(elderlyId) {
        if (confirm('This will trigger an emergency alert for ' + elderlyId + '. Continue?')) {
            alert('Emergency protocol activated for elderly ID: ' + elderlyId);
        }
    }

    // Load latest vital signs on page load
    function loadLatestVitalSigns() {
        const elderlyId = <?php echo $elderly['id']; ?>;
        
        fetch(`api/get_vital_signs.php?elderly_id=${elderlyId}&limit=1`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            if (response.status === 401) {
                console.warn('Authentication required for loading vital signs');
                return { success: false, message: 'Authentication required' };
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                const latest = data.data[0];
                
                // Update display
                document.getElementById('latest-vital-date').textContent = 
                    '(' + new Date(latest.recorded_date + 'T' + latest.recorded_time).toLocaleDateString('th-TH') + ')';
                
                document.getElementById('heart-rate-display').textContent = 
                    latest.heart_rate ? latest.heart_rate + ' bpm' : '-';
                    
                document.getElementById('blood-pressure-display').textContent = 
                    (latest.blood_pressure_systolic && latest.blood_pressure_diastolic) ? 
                    latest.blood_pressure_systolic + '/' + latest.blood_pressure_diastolic + ' mmHg' : '-';
                    
                document.getElementById('respiratory-rate-display').textContent = 
                    latest.respiratory_rate ? latest.respiratory_rate + ' ครั้ง/นาที' : '-';
                    
                document.getElementById('temperature-display').textContent = 
                    latest.temperature ? latest.temperature + '°C' : '-';
                    
                document.getElementById('oxygen-saturation-display').textContent = 
                    latest.oxygen_saturation ? latest.oxygen_saturation + '%' : '-';
            } else if (!data.success) {
                console.log('No vital signs data or auth required:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading vital signs:', error);
        });
    }
    
    // Incident Report Modal Functions
    function openIncidentReportModal() {
        const modal = document.getElementById('incidentReportModal');
        const elderlyId = <?php echo $elderly['id']; ?>;
        
        // เซ็ตค่า elderly_id
        document.getElementById('incident_elderly_id').value = elderlyId;
        
        // เซ็ตวันที่และเวลาปัจจุบัน
        const now = new Date();
        const formattedDateTime = now.getFullYear() + '-' + 
            String(now.getMonth() + 1).padStart(2, '0') + '-' + 
            String(now.getDate()).padStart(2, '0') + 'T' + 
            String(now.getHours()).padStart(2, '0') + ':' + 
            String(now.getMinutes()).padStart(2, '0');
        document.getElementById('incident_datetime').value = formattedDateTime;
        
        modal.style.display = 'block';
    }
    
    function closeIncidentReportModal() {
        const modal = document.getElementById('incidentReportModal');
        modal.style.display = 'none';
        
        // รีเซ็ตฟอร์ม
        document.getElementById('incidentReportForm').reset();
        document.getElementById('imagePreview').innerHTML = '';
    }
    
    // จัดการการอัพโหลดรูปภาพ
    function handleImageUpload() {
        const fileInput = document.getElementById('incident_images');
        const preview = document.getElementById('imagePreview');
        
        fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            preview.innerHTML = '';
            
            files.slice(0, 5).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" alt="Preview ${index + 1}">
                            <button type="button" class="preview-remove" onclick="removeImage(${index})">×</button>
                        `;
                        preview.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    }
    
    function removeImage(index) {
        const fileInput = document.getElementById('incident_images');
        const dt = new DataTransfer();
        const files = Array.from(fileInput.files);
        
        files.forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        fileInput.files = dt.files;
        
        // อัปเดตพรีวิว
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
    }
    
    // ส่งฟอร์มรายงานเหตุการณ์
    function submitIncidentReport(event) {
        event.preventDefault();
        
        const form = document.getElementById('incidentReportForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        submitBtn.disabled = true;
        submitBtn.textContent = 'กำลังบันทึก...';
        
        fetch('api/save_incident_report.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('บันทึกรายงานเหตุการณ์เรียบร้อยแล้ว');
                closeIncidentReportModal();
                // รีเฟรชหน้า หรือ อัปเดต UI ตามต้องการ
                location.reload();
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการส่งข้อมูล');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'บันทึกรายงาน';
        });
    }

    // Medication Modal Functions
    function openMedicationModal() {
        const modal = document.getElementById('medicationModal');
        const elderlyId = <?php echo $elderly['id']; ?>;
        
        // Set elderly_id
        document.getElementById('medication_elderly_id').value = elderlyId;
        
        // Set current date
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('medication_date').value = today;
        
        // Load today's medications
        loadTodayMedications(elderlyId, today);
        
        modal.style.display = 'block';
    }
    
    function loadTodayMedications(elderlyId, date) {
        const summaryElement = document.getElementById('summaryText');
        const todaySection = document.getElementById('todayMedicationsSection');
        const todayList = document.getElementById('todayMedicationsList');
        
        summaryElement.textContent = 'กำลังโหลดข้อมูลการให้ยา...';
        
        fetch(`api/get_today_medications.php?elderly_id=${elderlyId}&date=${date}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            if (response.status === 401) {
                // Don't throw error, just log and show message
                console.warn('Session might have expired, but continuing...');
                summaryElement.textContent = 'ยังไม่มีข้อมูลการให้ยา';
                todaySection.style.display = 'none';
                return { success: false, data: { medications: [], summary: { total_given: 0 } } };
            }
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                const medications = data.data.medications;
                const summary = data.data.summary;
                const dateThai = data.data.date_thai;
                
                // Update summary in header
                if (summary.total_given > 0) {
                    summaryElement.innerHTML = `
                        <div class="medication-summary-stats">
                            <div class="stat-item">
                                <span>วันนี้ให้ยาแล้ว:</span>
                                <span class="stat-number">${summary.total_given}</span>
                                <span>รายการ</span>
                            </div>
                            <div class="stat-item">
                                🌅 เช้า: <span class="stat-number">${summary.morning}</span>
                            </div>
                            <div class="stat-item">
                                ☀️ กลางวัน: <span class="stat-number">${summary.afternoon}</span>
                            </div>
                            <div class="stat-item">
                                🌆 เย็น: <span class="stat-number">${summary.evening}</span>
                            </div>
                            <div class="stat-item">
                                🌙 ก่อนนอน: <span class="stat-number">${summary.bedtime}</span>
                            </div>
                            <div class="stat-item">
                                ⚡ ตามต้องการ: <span class="stat-number">${summary.as_needed}</span>
                            </div>
                        </div>
                    `;
                } else {
                    summaryElement.innerHTML = `
                        <div class="stat-item">
                            <span>📅 ${dateThai}</span>
                            <span style="margin-left: 10px;">ยังไม่มีการให้ยาในวันนี้</span>
                        </div>
                    `;
                }
                
                // Show today's medications section if there are any
                if (medications.length > 0) {
                    todaySection.style.display = 'block';
                    
                    let medicationsHtml = '';
                    medications.forEach(med => {
                        const timeDisplay = med.administration_time ? 
                            new Date('1970-01-01T' + med.administration_time).toLocaleTimeString('th-TH', {
                                hour: '2-digit',
                                minute: '2-digit'
                            }) : '';
                        
                        medicationsHtml += `
                            <div class="medication-item">
                                <div class="medication-info">
                                    <div class="medication-name">${med.name}</div>
                                    <div class="medication-details">
                                        <span>${med.dosage}</span>
                                        <span>•</span>
                                        <span>${med.route_thai}</span>
                                        ${med.notes ? '<span>• ' + med.notes + '</span>' : ''}
                                    </div>
                                </div>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <span class="medication-period">${med.time_period_thai}</span>
                                    ${timeDisplay ? '<span class="medication-time">' + timeDisplay + '</span>' : ''}
                                </div>
                            </div>
                        `;
                    });
                    
                    todayList.innerHTML = medicationsHtml;
                } else {
                    todaySection.style.display = 'none';
                }
                
            } else {
                summaryElement.textContent = 'ไม่สามารถโหลดข้อมูลได้: ' + data.message;
                todaySection.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading today medications:', error);
            // Don't show error to user, just show empty state
            summaryElement.textContent = 'ยังไม่มีข้อมูลการให้ยาในวันนี้';
            if (todaySection) {
                todaySection.style.display = 'none';
            }
        });
    }
    
    function closeMedicationModal() {
        const modal = document.getElementById('medicationModal');
        modal.style.display = 'none';
        
        // Reset form
        document.getElementById('medicationForm').reset();
        
        // Hide all time inputs
        document.querySelectorAll('.time-input').forEach(input => {
            input.style.display = 'none';
        });
        
        // Reset time period cards
        document.querySelectorAll('.time-period-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Hide today's medications section
        const todaySection = document.getElementById('todayMedicationsSection');
        if (todaySection) {
            todaySection.style.display = 'none';
        }
        
        // Reset summary
        const summaryElement = document.getElementById('summaryText');
        if (summaryElement) {
            summaryElement.textContent = 'กำลังโหลด...';
        }
    }
    
    function handleTimePeriodChange() {
        document.querySelectorAll('input[name="time_periods[]"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const timeInput = document.getElementById(this.value + '_time');
                const card = this.closest('.time-period-card');
                
                if (this.checked) {
                    timeInput.style.display = 'block';
                    card.classList.add('selected');
                    
                    // Set default times based on period (optional - user can change)
                    if (!timeInput.value) {
                        switch(this.value) {
                            case 'morning':
                                timeInput.value = '08:00';
                                break;
                            case 'afternoon':
                                timeInput.value = '14:00';
                                break;
                            case 'evening':
                                timeInput.value = '19:00';
                                break;
                            case 'bedtime':
                                timeInput.value = '22:00';
                                break;
                            case 'as_needed':
                                const now = new Date();
                                const currentTime = now.getHours().toString().padStart(2, '0') + ':' + 
                                                   now.getMinutes().toString().padStart(2, '0');
                                timeInput.value = currentTime;
                                break;
                        }
                    }
                } else {
                    timeInput.style.display = 'none';
                    card.classList.remove('selected');
                    timeInput.value = '';
                }
            });
        });
    }
    
    function checkForDuplicateMedication(medicationName, selectedDate, checkedPeriods) {
        // Get current medications data from the displayed list
        const medicationItems = document.querySelectorAll('.medication-item');
        const today = new Date().toISOString().split('T')[0];
        
        // Only check if the selected date is today (when we have loaded data)
        if (selectedDate !== today || medicationItems.length === 0) {
            return false; // No duplicate check needed
        }
        
        const selectedPeriods = Array.from(checkedPeriods).map(checkbox => checkbox.value);
        
        let duplicateFound = false;
        let duplicateDetails = [];
        
        medicationItems.forEach(item => {
            const nameElement = item.querySelector('.medication-name');
            const periodElement = item.querySelector('.medication-period');
            
            if (nameElement && periodElement) {
                const existingName = nameElement.textContent.trim();
                const existingPeriodThai = periodElement.textContent.trim();
                
                // Convert Thai period back to English
                const existingPeriod = convertThaiPeriodToEnglish(existingPeriodThai);
                
                // Check if same medication name and overlapping time period
                if (existingName.toLowerCase() === medicationName.toLowerCase() && 
                    selectedPeriods.includes(existingPeriod)) {
                    duplicateFound = true;
                    duplicateDetails.push(`${existingName} ในช่วง${existingPeriodThai}`);
                }
            }
        });
        
        if (duplicateFound) {
            const confirmMessage = `พบการให้ยาซ้ำ:\n${duplicateDetails.join('\n')}\n\nคุณต้องการบันทึกต่อหรือไม่?`;
            return !confirm(confirmMessage);
        }
        
        return false;
    }
    
    function convertThaiPeriodToEnglish(thaiPeriod) {
        const periodMap = {
            'เช้า': 'morning',
            'กลางวัน': 'afternoon',
            'เย็น': 'evening',
            'ก่อนนอน': 'bedtime',
            'ตามความต้องการ': 'as_needed'
        };
        return periodMap[thaiPeriod] || thaiPeriod;
    }
    
    function submitMedicationForm(event) {
        event.preventDefault();
        
        const form = document.getElementById('medicationForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Validate at least one time period is selected
        const checkedPeriods = document.querySelectorAll('input[name="time_periods[]"]:checked');
        if (checkedPeriods.length === 0) {
            alert('กรุณาเลือกอย่างน้อยหนึ่งช่วงเวลาการให้ยา');
            return;
        }
        
        // Check for duplicate medication in same time period
        const medicationName = document.getElementById('medication_name').value.trim();
        const selectedDate = document.getElementById('medication_date').value;
        
        if (checkForDuplicateMedication(medicationName, selectedDate, checkedPeriods)) {
            return; // Stop if duplicate found
        }
        
        submitBtn.disabled = true;
        submitBtn.textContent = 'กำลังบันทึก...';
        
        fetch('api/save_medication.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('บันทึกการให้ยาเรียบร้อยแล้ว');
                
                // Reload today's medications to show updated data
                const elderlyId = document.getElementById('medication_elderly_id').value;
                const today = new Date().toISOString().split('T')[0];
                loadTodayMedications(elderlyId, today);
                
                // Reset form but keep modal open to show updated data
                document.getElementById('medicationForm').reset();
                document.querySelectorAll('.time-input').forEach(input => {
                    input.style.display = 'none';
                });
                document.querySelectorAll('.time-period-card').forEach(card => {
                    card.classList.remove('selected');
                });
                
                // Reset form fields
                document.getElementById('medication_date').value = today;
                
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการส่งข้อมูล');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'บันทึกการให้ยา';
        });
    }

    // Symptoms Modal Functions
    function openSymptomsModal() {
        const modal = document.getElementById('symptomsModal');
        const elderlyId = <?php echo $elderly['id']; ?>;
        
        // Set elderly_id
        document.getElementById('symptoms_elderly_id').value = elderlyId;
        
        // Set current date
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('symptoms_date').value = today;
        
        // Load previous symptoms
        loadPreviousSymptoms(elderlyId);
        
        modal.style.display = 'block';
    }
    
    function loadPreviousSymptoms(elderlyId) {
        const summaryElement = document.getElementById('symptomsSummaryText');
        const previousSection = document.getElementById('previousSymptomsSection');
        const previousList = document.getElementById('previousSymptomsList');
        
        summaryElement.textContent = 'กำลังโหลดข้อมูลการบันทึกอาการ...';
        
        fetch(`api/get_symptoms.php?elderly_id=${elderlyId}&limit=5`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            if (response.status === 401) {
                throw new Error('กรุณาเข้าสู่ระบบใหม่');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                const symptoms = data.data.symptoms;
                const summary = data.data.summary;
                
                // Update summary
                if (summary.total_records > 0) {
                    summaryElement.innerHTML = `
                        <div class="symptoms-summary-stats">
                            <span>📊 ${summary.total_records} รายการ</span>
                            <span>🟢 ${summary.mild}</span>
                            <span>🟡 ${summary.moderate}</span>
                            <span>🔴 ${summary.severe}</span>
                            <span>🆘 ${summary.critical}</span>
                        </div>
                    `;
                } else {
                    summaryElement.innerHTML = `<span>📋 ยังไม่มีการบันทึกอาการ</span>`;
                }
                
                // Show previous symptoms if any
                if (symptoms.length > 0) {
                    previousSection.style.display = 'block';
                    
                    let symptomsHtml = '';
                    symptoms.forEach(symptom => {
                        const timeDisplay = symptom.time_thai || '';
                        const temperatureDisplay = symptom.temperature ? 
                            `🌡️ ${symptom.temperature}°C` : '';
                        
                        symptomsHtml += `
                            <div class="symptom-item">
                                <div class="symptom-header">
                                    <div class="symptom-date-time">
                                        📅 ${symptom.date_thai}
                                    </div>
                                    <div class="symptom-severity ${symptom.severity}">
                                        ${symptom.severity_thai}
                                    </div>
                                </div>
                                <div class="symptom-description">
                                    ${symptom.symptoms.length > 60 ? symptom.symptoms.substring(0, 60) + '...' : symptom.symptoms}
                                </div>
                                <div class="symptom-details">
                                    ${temperatureDisplay ? `${temperatureDisplay}` : ''}
                                    ${symptom.notes ? ` • ${symptom.notes.length > 30 ? symptom.notes.substring(0, 30) + '...' : symptom.notes}` : ''}
                                </div>
                            </div>
                        `;
                    });
                    
                    previousList.innerHTML = symptomsHtml;
                } else {
                    previousSection.style.display = 'none';
                }
                
            } else {
                summaryElement.textContent = 'ไม่สามารถโหลดข้อมูลได้: ' + data.message;
                previousSection.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading symptoms:', error);
            summaryElement.textContent = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
            previousSection.style.display = 'none';
        });
    }
    
    function closeSymptomsModal() {
        const modal = document.getElementById('symptomsModal');
        modal.style.display = 'none';
        
        // Reset form
        document.getElementById('symptomsForm').reset();
        
        // Clear image preview
        document.getElementById('symptomsImagePreview').innerHTML = '';
        
        // Hide previous symptoms section
        const previousSection = document.getElementById('previousSymptomsSection');
        if (previousSection) {
            previousSection.style.display = 'none';
        }
        
        // Reset summary
        const summaryElement = document.getElementById('symptomsSummaryText');
        if (summaryElement) {
            summaryElement.textContent = 'กำลังโหลด...';
        }
    }
    
    function handleSymptomsImageUpload() {
        const fileInput = document.getElementById('symptoms_images');
        const preview = document.getElementById('symptomsImagePreview');
        
        fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            preview.innerHTML = '';
            
            files.slice(0, 5).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" alt="Preview ${index + 1}">
                            <button type="button" class="preview-remove" onclick="removeSymptomsImage(${index})">×</button>
                        `;
                        preview.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    }
    
    function removeSymptomsImage(index) {
        const fileInput = document.getElementById('symptoms_images');
        const dt = new DataTransfer();
        const files = Array.from(fileInput.files);
        
        files.forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        fileInput.files = dt.files;
        
        // Update preview
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
    }
    
    function submitSymptomsForm(event) {
        event.preventDefault();
        
        const form = document.getElementById('symptomsForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        submitBtn.disabled = true;
        submitBtn.textContent = 'กำลังบันทึก...';
        
        fetch('api/save_symptoms.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('บันทึกอาการเรียบร้อยแล้ว');
                closeSymptomsModal();
                // อาจต้องรีเฟรชหน้าหรืออัปเดต UI ตามต้องการ
                location.reload();
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการส่งข้อมูล');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'บันทึกอาการ';
        });
    }

    // Tab Navigation Functions
    function initializeTabs() {
        // Add click event listeners to all nav-tab elements
        const navTabs = document.querySelectorAll('.nav-tab');
        navTabs.forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                const tabName = this.getAttribute('data-tab');
                switchTab(tabName);
            });
        });
    }

    function switchTab(tabName) {
        // Remove active class from all nav tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Hide all tab panes
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        
        // Activate selected tab
        document.querySelectorAll(`[data-tab="${tabName}"]`).forEach(tab => {
            tab.classList.add('active');
        });
        
        // Show selected tab pane
        const targetPane = document.getElementById(`${tabName}-tab`);
        if (targetPane) {
            targetPane.classList.add('active');
        }
        
        // Update mobile nav button text
        const activeTab = document.querySelector('.nav-tab.active');
        if (activeTab) {
            const mobileButton = document.querySelector('.mobile-nav-button span');
            if (mobileButton) {
                const icon = getTabIcon(tabName);
                mobileButton.textContent = `${icon} ${activeTab.textContent}`;
            }
        }
    }

    function getTabIcon(tabName) {
        const icons = {
            'profile': '👤',
            'care-records': '📋',
            'wound-records': '🏥',
            'elderly-info': '📊',
            'health-assessment': '💚',
            'medications': '💊',
            'family': '👨‍👩‍👧‍👦',
            'hospital': '🏨',
            'incident-reports': '⚠️'
        };
        return icons[tabName] || '📋';
    }

    // Calendar Functions
    function initializeCalendar() {
        let currentWeekStart = new Date(2024, 7, 18); // August 18, 2024
        
        // Add event listeners for calendar navigation
        document.getElementById('prevWeek').addEventListener('click', () => {
            currentWeekStart.setDate(currentWeekStart.getDate() - 7);
            updateCalendar(currentWeekStart);
        });
        
        document.getElementById('nextWeek').addEventListener('click', () => {
            currentWeekStart.setDate(currentWeekStart.getDate() + 7);
            updateCalendar(currentWeekStart);
        });
        
        // Add click handlers to weekday items
        document.querySelectorAll('.weekday-item').forEach(item => {
            item.addEventListener('click', function() {
                selectDate(this);
            });
        });
        
        // Set initial selected date and load records
        updateSelectedDate('2024-08-19', '19 สิงหาคม 2567');
        loadCareRecordsForDate('2024-08-19');
    }

    function updateCalendar(weekStart) {
        const monthNames = ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 
                           'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'];
        const dayLabels = ['อ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.', 'อา.'];
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        
        // Update calendar title
        const titleElement = document.getElementById('calendarTitle');
        if (titleElement) {
            titleElement.textContent = `${monthNames[weekStart.getMonth()]} ${weekStart.getDate()} - ${weekEnd.getDate()}`;
        }
        
        // Update weekday items
        const weekdayItems = document.querySelectorAll('.weekday-item');
        weekdayItems.forEach((item, index) => {
            const currentDate = new Date(weekStart);
            currentDate.setDate(currentDate.getDate() + index);
            
            const dateStr = currentDate.toISOString().split('T')[0];
            const dateDisplay = currentDate.getDate();
            
            item.querySelector('.weekday-label').textContent = dayLabels[index];
            item.querySelector('.weekday-date').textContent = dateDisplay;
            item.querySelector('.weekday-date').setAttribute('data-date', dateStr);
        });
    }

    function selectDate(weekdayItem) {
        // Remove active class from all weekday items
        document.querySelectorAll('.weekday-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to selected item
        weekdayItem.classList.add('active');
        
        // Get date info
        const dateElement = weekdayItem.querySelector('.weekday-date');
        const selectedDate = dateElement.getAttribute('data-date');
        const displayDate = formatThaiDate(selectedDate);
        
        // Update selected date display
        updateSelectedDate(selectedDate, displayDate);
        
        // Load care records for selected date
        loadCareRecordsForDate(selectedDate);
    }

    function formatThaiDate(dateStr) {
        const months = ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
                       'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'];
        
        const date = new Date(dateStr);
        const day = date.getDate();
        const month = months[date.getMonth()];
        const year = date.getFullYear() + 543; // Convert to Buddhist year
        
        return `${day} ${month} ${year}`;
    }

    function updateSelectedDate(date, displayDate) {
        const displayElement = document.getElementById('selectedDateDisplay');
        if (displayElement) {
            displayElement.textContent = displayDate;
        }
    }

    function loadCareRecordsForDate(date) {
        const elderlyId = <?php echo $elderly['id']; ?>;
        const container = document.getElementById('existingRecordsContainer');
        const loadingMessage = document.getElementById('recordsLoading');
        
        // Show loading message
        loadingMessage.style.display = 'block';
        container.innerHTML = `
            <div class="loading-message" style="text-align: center; padding: 20px; color: var(--text-secondary);">
                <i class="fas fa-spinner fa-spin"></i> กำลังโหลดข้อมูล...
            </div>
        `;
        
        fetch(`api/get_care_records_by_date.php?elderly_id=${elderlyId}&date=${date}`, {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Cache-Control': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.status === 401) {
                throw new Error('กรุณาเข้าสู่ระบบใหม่');
            }
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                displayCareRecords(data.records, data.total_records);
            } else {
                container.innerHTML = `
                    <div class="error-message" style="text-align: center; padding: 20px; color: var(--high-risk-red);">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading care records:', error);
            container.innerHTML = `
                <div class="error-message" style="text-align: center; padding: 20px; color: var(--high-risk-red);">
                    <i class="fas fa-exclamation-triangle"></i> เกิดข้อผิดพลาดในการโหลดข้อมูล<br>
                    <small style="color: var(--text-secondary); margin-top: 8px; display: block;">${error.message}</small>
                </div>
            `;
        });
    }

    function displayCareRecords(records, totalCount) {
        const container = document.getElementById('existingRecordsContainer');
        
        if (totalCount === 0) {
            container.innerHTML = `
                <div class="no-records-message" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                    <i class="fas fa-clipboard-list" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p style="margin: 0; font-size: 1.1rem;">ยังไม่มีการบันทึกในวันนี้</p>
                </div>
            `;
            return;
        }

        let html = '';
        const recordTypes = {
            vital_signs: { name: 'สัญญาณชีพ', icon: '🩺', color: '#e74c3c' },
            turning: { name: 'การพลิกตัว', icon: '🔄', color: '#3498db' },
            pressure_sore: { name: 'แผลกดทับ', icon: '🩹', color: '#e67e22' },
            weight_height: { name: 'น้ำหนักและส่วนสูง', icon: '⚖️', color: '#9b59b6' },
            incident: { name: 'รายงานเหตุการณ์', icon: '⚠️', color: '#f39c12' },
            medication: { name: 'บันทึกการให้ยา', icon: '💊', color: '#1abc9c' },
            symptoms: { name: 'บันทึกอาการ', icon: '🤒', color: '#e74c3c' },
            excretion: { name: 'การขับถ่าย', icon: '🚽', color: '#34495e' },
            hygiene: { name: 'สุขอนามัย', icon: '🧼', color: '#16a085' },
            feeding: { name: 'ทาน/ฟีดอาหาร', icon: '🍽️', color: '#27ae60' },
            activities: { name: 'กิจกรรม', icon: '🏃', color: '#f1c40f' },
            sputum: { name: 'คุณสมบัติของเสมหะ', icon: '🫁', color: '#95a5a6' },
            mental_state: { name: 'สภาวะจิตใจ/อารมณ์', icon: '🧠', color: '#8e44ad' },
            sleep: { name: 'การนอนหลับ', icon: '😴', color: '#2c3e50' }
        };

        // Group records by type and display them
        Object.keys(recordTypes).forEach(type => {
            if (records[type] && records[type].length > 0) {
                const typeInfo = recordTypes[type];
                html += `
                    <div class="record-type-section" style="margin-bottom: 25px;">
                        <h5 style="color: ${typeInfo.color}; margin-bottom: 12px; display: flex; align-items: center; gap: 8px; font-size: 1.1rem;">
                            <span style="font-size: 1.2rem;">${typeInfo.icon}</span>
                            ${typeInfo.name} (${records[type].length} รายการ)
                        </h5>
                        <div class="records-list">
                `;
                
                records[type].forEach(record => {
                    const time = new Date(record.recorded_datetime).toLocaleTimeString('th-TH', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    
                    html += `
                        <div class="record-item" style="background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%); border-left: 4px solid ${typeInfo.color}; padding: 15px; margin-bottom: 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 8px;">
                                <span style="font-weight: 600; color: var(--text-primary);">เวลา: ${time}</span>
                                <span style="font-size: 0.9rem; color: var(--text-secondary);">ID: ${record.id}</span>
                            </div>
                            ${generateRecordDetails(record, type)}
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
        });

        container.innerHTML = html;
    }

    function generateRecordDetails(record, type) {
        let details = '';
        
        switch(type) {
            case 'vital_signs':
                details = `
                    <div class="record-details">
                        ${record.temperature ? `<span class="detail-item">🌡️ อุณหภูมิ: ${record.temperature}°C</span>` : ''}
                        ${record.heart_rate ? `<span class="detail-item">❤️ ชีพจร: ${record.heart_rate} ครั้ง/นาที</span>` : ''}
                        ${record.blood_pressure_systolic && record.blood_pressure_diastolic ? `<span class="detail-item">🩸 ความดัน: ${record.blood_pressure_systolic}/${record.blood_pressure_diastolic} mmHg</span>` : ''}
                        ${record.respiratory_rate ? `<span class="detail-item">🫁 การหายใจ: ${record.respiratory_rate} ครั้ง/นาที</span>` : ''}
                        ${record.oxygen_saturation ? `<span class="detail-item">💨 ออกซิเจน: ${record.oxygen_saturation}%</span>` : ''}
                        ${record.blood_sugar ? `<span class="detail-item">🍯 น้ำตาล: ${record.blood_sugar} mg/dL</span>` : ''}
                        ${record.additional_notes ? `<div class="notes">📝 หมายเหตุ: ${record.additional_notes}</div>` : ''}
                    </div>
                `;
                break;
            case 'symptoms':
                details = `
                    <div class="record-details">
                        ${record.symptoms ? `<span class="detail-item">😷 อาการ: ${record.symptoms}</span>` : ''}
                        ${record.severity ? `<span class="detail-item">⚡ ระดับความรุนแรง: ${record.severity}</span>` : ''}
                        ${record.temperature ? `<span class="detail-item">🌡️ อุณหภูมิ: ${record.temperature}°C</span>` : ''}
                        ${record.action_taken ? `<span class="detail-item">🏥 การดำเนินการ: ${record.action_taken}</span>` : ''}
                        ${record.additional_notes ? `<div class="notes">📝 หมายเหตุ: ${record.additional_notes}</div>` : ''}
                    </div>
                `;
                break;
            case 'medication':
                details = `
                    <div class="record-details">
                        ${record.medication_name ? `<span class="detail-item">💊 ชื่อยา: ${record.medication_name}</span>` : ''}
                        ${record.dosage ? `<span class="detail-item">📏 ปริมาณ: ${record.dosage}</span>` : ''}
                        ${record.administration_method ? `<span class="detail-item">🔄 วิธีการให้: ${record.administration_method}</span>` : ''}
                        ${record.given_by ? `<span class="detail-item">👨‍⚕️ ผู้ให้ยา: ${record.given_by}</span>` : ''}
                        ${record.reaction ? `<span class="detail-item">⚡ ปฏิกิริยา: ${record.reaction}</span>` : ''}
                        ${record.notes ? `<div class="notes">📝 หมายเหตุ: ${record.notes}</div>` : ''}
                    </div>
                `;
                break;
            default:
                // Generic details for other record types
                details = `
                    <div class="record-details">
                        ${Object.keys(record).filter(key => 
                            !['id', 'elderly_id', 'recorded_datetime', 'recorded_date', 'recorded_time'].includes(key) && 
                            record[key] && record[key] !== ''
                        ).map(key => {
                            if (key === 'notes' || key === 'additional_notes') {
                                return `<div class="notes">📝 ${key}: ${record[key]}</div>`;
                            } else {
                                return `<span class="detail-item">${key}: ${record[key]}</span>`;
                            }
                        }).join('')}
                    </div>
                `;
        }
        
        return details;
    }

    // Mobile Navigation Functions
    function toggleMobileNav() {
        const button = document.querySelector('.mobile-nav-button');
        const menu = document.getElementById('mobileNavMenu');
        
        button.classList.toggle('active');
        menu.classList.toggle('show');
    }

    // Tab Content Loading Functions
    function loadTabContent(tabName) {
        const elderlyId = <?php echo $elderly['id']; ?>;
        
        switch(tabName) {
            case 'care-records':
                // Already handled by existing calendar system
                break;
            case 'wound-records':
                loadWoundRecords(elderlyId);
                break;
            case 'health-assessment':
                loadHealthAssessment(elderlyId);
                break;
            case 'medications':
                loadMedicationsHistory(elderlyId);
                break;
            case 'incident-reports':
                loadIncidentReports(elderlyId);
                break;
            // Add other tab loading functions as needed
        }
    }

    function loadWoundRecords(elderlyId) {
        const container = document.getElementById('wound-records-content');
        
        fetch(`api/get_pressure_sore_records.php?elderly_id=${elderlyId}`, {
            method: 'GET',
            credentials: 'include',
            headers: { 'Cache-Control': 'no-cache' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.records) {
                displayWoundRecords(data.records, container);
            } else {
                container.innerHTML = `
                    <div class="card">
                        <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                            ไม่มีข้อมูลบันทึกแผล
                        </p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading wound records:', error);
            container.innerHTML = `
                <div class="card">
                    <p style="text-align: center; color: var(--high-risk-red); padding: 20px;">
                        เกิดข้อผิดพลาดในการโหลดข้อมูล
                    </p>
                </div>
            `;
        });
    }

    function displayWoundRecords(records, container) {
        if (records.length === 0) {
            container.innerHTML = `
                <div class="card">
                    <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                        ไม่มีข้อมูลบันทึกแผล
                    </p>
                </div>
            `;
            return;
        }

        let html = '<div class="cards-grid" style="display: grid; gap: 20px;">';
        
        records.forEach(record => {
            html += `
                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px;">
                        <h4 style="margin: 0; color: var(--primary-green);">
                            บันทึกแผลกดทับ
                        </h4>
                        <span style="color: var(--text-secondary); font-size: 0.9rem;">
                            ${new Date(record.recorded_datetime).toLocaleDateString('th-TH')}
                        </span>
                    </div>
                    <div class="record-details">
                        ${record.wound_location ? `<span class="detail-item">📍 ตำแหน่งแผล: ${record.wound_location}</span>` : ''}
                        ${record.wound_stage ? `<span class="detail-item">📊 ระดับแผล: ${record.wound_stage}</span>` : ''}
                        ${record.wound_size ? `<span class="detail-item">📏 ขนาดแผล: ${record.wound_size}</span>` : ''}
                        ${record.treatment ? `<span class="detail-item">💊 การรักษา: ${record.treatment}</span>` : ''}
                        ${record.notes ? `<div class="notes">📝 หมายเหตุ: ${record.notes}</div>` : ''}
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
    }

    function loadHealthAssessment(elderlyId) {
        const container = document.getElementById('health-assessment-content');
        
        // Load vital signs data for health assessment
        fetch(`api/get_vital_signs.php?elderly_id=${elderlyId}&limit=10`, {
            method: 'GET',
            credentials: 'include',
            headers: { 'Cache-Control': 'no-cache' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                displayHealthAssessment(data.data, container);
            } else {
                container.innerHTML = `
                    <div class="card">
                        <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                            ไม่มีข้อมูลการประเมินสุขภาพ
                        </p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading health assessment:', error);
            container.innerHTML = `
                <div class="card">
                    <p style="text-align: center; color: var(--high-risk-red); padding: 20px;">
                        เกิดข้อผิดพลาดในการโหลดข้อมูล
                    </p>
                </div>
            `;
        });
    }

    function displayHealthAssessment(vitalSigns, container) {
        let html = `
            <div class="vitals-careplan-section">
                <div class="vitals-card card">
                    <h3>🩺 สัญญาณชีพล่าสุด</h3>
        `;
        
        if (vitalSigns.length > 0) {
            const latest = vitalSigns[0];
            html += `
                <ul class="vitals-list">
                    <li>
                        <span class="label">❤️ ชีพจร</span>
                        <span class="value">${latest.heart_rate || '-'} bpm</span>
                    </li>
                    <li>
                        <span class="label">🩸 ความดันโลหิต</span>
                        <span class="value">${latest.blood_pressure_systolic && latest.blood_pressure_diastolic ? 
                            latest.blood_pressure_systolic + '/' + latest.blood_pressure_diastolic : '-'} mmHg</span>
                    </li>
                    <li>
                        <span class="label">🫁 การหายใจ</span>
                        <span class="value">${latest.respiratory_rate || '-'} ครั้ง/นาที</span>
                    </li>
                    <li>
                        <span class="label">🌡️ อุณหภูมิร่างกาย</span>
                        <span class="value">${latest.temperature || '-'}°C</span>
                    </li>
                    <li>
                        <span class="label">💨 ออกซิเจนในเลือด</span>
                        <span class="value">${latest.oxygen_saturation || '-'}%</span>
                    </li>
                </ul>
                <p style="text-align: center; color: var(--text-secondary); margin-top: 15px; font-size: 0.9rem;">
                    บันทึกล่าสุด: ${new Date(latest.recorded_datetime).toLocaleDateString('th-TH')} 
                    ${new Date(latest.recorded_datetime).toLocaleTimeString('th-TH', {hour: '2-digit', minute: '2-digit'})}
                </p>
            `;
        } else {
            html += `<p style="text-align: center; color: var(--text-secondary); padding: 20px;">ยังไม่มีข้อมูลสัญญาณชีพ</p>`;
        }
        
        html += `
                </div>
                <div class="careplan-card card">
                    <h3>📋 แผนการดูแล</h3>
                    <div class="value">
                        การดูแลตามแผนการดูแลมาตรฐาน
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }

    function loadMedicationsHistory(elderlyId) {
        const container = document.getElementById('medications-content');
        
        fetch(`api/get_medications.php?elderly_id=${elderlyId}&limit=20`, {
            method: 'GET',
            credentials: 'include',
            headers: { 'Cache-Control': 'no-cache' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                displayMedicationsHistory(data.data, container);
            } else {
                container.innerHTML = `
                    <div class="card">
                        <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                            ไม่มีข้อมูลประวัติการให้ยา
                        </p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading medications:', error);
            container.innerHTML = `
                <div class="card">
                    <p style="text-align: center; color: var(--high-risk-red); padding: 20px;">
                        เกิดข้อผิดพลาดในการโหลดข้อมูล
                    </p>
                </div>
            `;
        });
    }

    function displayMedicationsHistory(medications, container) {
        if (medications.length === 0) {
            container.innerHTML = `
                <div class="card">
                    <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                        ไม่มีข้อมูลประวัติการให้ยา
                    </p>
                </div>
            `;
            return;
        }

        let html = '<div class="medications-history">';
        
        medications.forEach(med => {
            html += `
                <div class="card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                        <h4 style="margin: 0; color: var(--primary-green);">💊 ${med.name}</h4>
                        <span style="color: var(--text-secondary); font-size: 0.9rem;">
                            ${new Date(med.administration_datetime).toLocaleDateString('th-TH')}
                            ${new Date(med.administration_datetime).toLocaleTimeString('th-TH', {hour: '2-digit', minute: '2-digit'})}
                        </span>
                    </div>
                    <div class="record-details">
                        ${med.dosage ? `<span class="detail-item">📏 ปริมาณ: ${med.dosage}</span>` : ''}
                        ${med.administration_method ? `<span class="detail-item">🔄 วิธีการให้: ${med.administration_method}</span>` : ''}
                        ${med.time_period ? `<span class="detail-item">⏰ ช่วงเวลา: ${med.time_period}</span>` : ''}
                        ${med.given_by ? `<span class="detail-item">👨‍⚕️ ผู้ให้ยา: ${med.given_by}</span>` : ''}
                        ${med.notes ? `<div class="notes">📝 หมายเหตุ: ${med.notes}</div>` : ''}
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
    }

    function loadIncidentReports(elderlyId) {
        const container = document.getElementById('incident-reports-content');
        
        fetch(`api/get_incident_reports.php?elderly_id=${elderlyId}`, {
            method: 'GET',
            credentials: 'include',
            headers: { 'Cache-Control': 'no-cache' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.reports) {
                displayIncidentReports(data.reports, container);
            } else {
                container.innerHTML = `
                    <div class="card">
                        <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                            ไม่มีรายงานเหตุการณ์
                        </p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading incident reports:', error);
            container.innerHTML = `
                <div class="card">
                    <p style="text-align: center; color: var(--high-risk-red); padding: 20px;">
                        เกิดข้อผิดพลาดในการโหลดข้อมูล
                    </p>
                </div>
            `;
        });
    }

    function displayIncidentReports(reports, container) {
        if (reports.length === 0) {
            container.innerHTML = `
                <div class="card">
                    <p style="text-align: center; color: var(--text-secondary); padding: 20px;">
                        ไม่มีรายงานเหตุการณ์
                    </p>
                </div>
            `;
            return;
        }

        let html = '<div class="incident-reports-list">';
        
        reports.forEach(report => {
            const severityColors = {
                low: '#28a745',
                medium: '#ffc107', 
                high: '#fd7e14',
                critical: '#dc3545'
            };
            
            html += `
                <div class="card" style="margin-bottom: 15px; border-left: 4px solid ${severityColors[report.severity] || '#6c757d'};">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                        <h4 style="margin: 0; color: var(--primary-green);">⚠️ ${report.incident_type}</h4>
                        <div style="text-align: right;">
                            <div style="color: var(--text-secondary); font-size: 0.9rem;">
                                ${new Date(report.incident_datetime).toLocaleDateString('th-TH')}
                                ${new Date(report.incident_datetime).toLocaleTimeString('th-TH', {hour: '2-digit', minute: '2-digit'})}
                            </div>
                            <span style="background: ${severityColors[report.severity] || '#6c757d'}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem;">
                                ${report.severity}
                            </span>
                        </div>
                    </div>
                    <div class="record-details">
                        ${report.location ? `<span class="detail-item">📍 สถานที่: ${report.location}</span>` : ''}
                        ${report.witnesses ? `<span class="detail-item">👥 ผู้เห็นเหตุการณ์: ${report.witnesses}</span>` : ''}
                        ${report.action_taken ? `<span class="detail-item">🏥 การดำเนินการ: ${report.action_taken}</span>` : ''}
                        ${report.description ? `<div class="notes">📝 รายละเอียด: ${report.description}</div>` : ''}
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
    }

    // Initialize everything when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeTabs();
        initializeCalendar();
        
        // Initialize modal image upload handlers
        if (typeof handleImageUpload === 'function') handleImageUpload();
        if (typeof handleTimePeriodChange === 'function') handleTimePeriodChange();
        if (typeof handleSymptomsImageUpload === 'function') handleSymptomsImageUpload();
        
        // Load initial vital signs
        loadLatestVitalSigns();
    });

    // Handle tab switching with content loading
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('nav-tab')) {
            e.preventDefault();
            const tabName = e.target.getAttribute('data-tab');
            
            // Switch tab display
            switchTab(tabName);
            
            // Load tab content if needed
            if (tabName !== 'profile' && tabName !== 'elderly-info') {
                loadTabContent(tabName);
            }
            
            // Close mobile menu if open
            const mobileMenu = document.getElementById('mobileNavMenu');
            if (mobileMenu.classList.contains('show')) {
                toggleMobileNav();
            }
        }
    });

    </script>
</body>
</html>
