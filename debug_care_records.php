<?php
define('AIVORA_SECURITY', true);
session_start();

// Enhanced error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/logs/debug_errors.log');

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';

// Function to log debug information
function debugLog($message, $data = null) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] DEBUG: $message";
    if ($data !== null) {
        $logEntry .= " | Data: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    $logEntry .= "\n";
    file_put_contents(__DIR__ . '/logs/debug_errors.log', $logEntry, FILE_APPEND);
    echo "<div style='background:#f0f0f0; padding:10px; margin:5px; border:1px solid #ccc;'>";
    echo "<strong>$message</strong>";
    if ($data !== null) {
        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    }
    echo "</div>";
}

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Care Records Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .test-form { background: white; padding: 20px; border: 1px solid #ddd; margin: 10px 0; }
        input, select, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; }
        input[type="submit"] { width: auto; background: #4caf50; color: white; }
    </style>
</head>
<body>
    <h1>🔧 Care Records Debug Tool</h1>
    
    <?php
    debugLog("Debug tool started");
    
    // Test 1: Check database connection
    echo "<div class='debug-section'>";
    echo "<h2>1. Database Connection Test</h2>";
    try {
        if (isset($conn) && $conn && !$conn->connect_error) {
            debugLog("Database connection successful", [
                'host' => $conn->host_info,
                'server_version' => $conn->server_version
            ]);
        } else {
            debugLog("Database connection failed", [
                'error' => $conn ? $conn->connect_error : 'Connection object not found'
            ]);
        }
    } catch (Exception $e) {
        debugLog("Database connection exception", ['error' => $e->getMessage()]);
    }
    echo "</div>";
    
    // Test 2: Check session and user
    echo "<div class='debug-section'>";
    echo "<h2>2. Session and User Test</h2>";
    debugLog("Session data", [
        'session_id' => session_id(),
        'user_role' => $_SESSION['user_role'] ?? 'not set',
        'user_id' => $_SESSION['user_id'] ?? 'not set',
        'facility_id' => $_SESSION['facility_id'] ?? 'not set',
        'username' => $_SESSION['username'] ?? 'not set'
    ]);
    echo "</div>";
    
    // Test 3: Check required tables
    echo "<div class='debug-section'>";
    echo "<h2>3. Database Tables Check</h2>";
    $required_tables = [
        'users', 'elderly', 'facilities', 'schedules', 'events',
        'care_line_notifications', 'care_vital_signs', 'care_turning_records',
        'care_pressure_ulcers', 'care_weight_height', 'care_incident_reports',
        'care_medication_records', 'care_symptom_records', 'care_excretion_records',
        'care_hygiene_records', 'care_feeding_records', 'care_activity_records',
        'care_sputum_records', 'care_mental_state_records', 'care_sleep_records'
    ];
    
    $missing_tables = [];
    $existing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                $existing_tables[] = $table;
            } else {
                $missing_tables[] = $table;
            }
        } catch (Exception $e) {
            $missing_tables[] = "$table (Error: " . $e->getMessage() . ")";
        }
    }
    
    debugLog("Table status", [
        'existing' => $existing_tables,
        'missing' => $missing_tables
    ]);
    echo "</div>";
    
    // Test 4: Check API endpoints
    echo "<div class='debug-section'>";
    echo "<h2>4. API Endpoints Check</h2>";
    $api_files = [
        'api/save_care_record.php',
        'api/save_vital_signs.php',
        'api/get_vital_signs.php',
        'api/check_today_vital_signs.php'
    ];
    
    foreach ($api_files as $api_file) {
        if (file_exists(__DIR__ . '/' . $api_file)) {
            debugLog("API file exists: $api_file");
        } else {
            debugLog("API file MISSING: $api_file");
        }
    }
    echo "</div>";
    
    // Test 5: Test care record saving
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_care_record'])) {
        echo "<div class='debug-section'>";
        echo "<h2>5. Care Record Save Test</h2>";
        
        debugLog("Testing care record save with data", $_POST);
        
        // Test the save_care_record.php endpoint
        try {
            $postData = [
                'care_type' => $_POST['care_type'],
                'elderly_id' => $_POST['elderly_id'],
                'notes' => $_POST['notes'] ?? 'Debug test entry'
            ];
            
            // Add specific fields based on care type
            switch ($_POST['care_type']) {
                case 'vital_signs':
                    $postData['temperature'] = $_POST['temperature'] ?? '';
                    $postData['heart_rate'] = $_POST['heart_rate'] ?? '';
                    $postData['bp_systolic'] = $_POST['bp_systolic'] ?? '';
                    $postData['bp_diastolic'] = $_POST['bp_diastolic'] ?? '';
                    break;
                case 'medication_records':
                    $postData['medication_name'] = $_POST['medication_name'] ?? 'Test Medicine';
                    $postData['dosage'] = $_POST['dosage'] ?? '1 tablet';
                    break;
                // Add other cases as needed
            }
            
            debugLog("Prepared POST data", $postData);
            
            // Simulate the API call
            $_POST = array_merge($_POST, $postData);
            
            // Include and test the API
            ob_start();
            include __DIR__ . '/api/save_care_record.php';
            $output = ob_get_clean();
            
            debugLog("API output", ['output' => $output]);
            
        } catch (Exception $e) {
            debugLog("Care record save test failed", ['error' => $e->getMessage()]);
        }
        echo "</div>";
    }
    ?>
    
    <!-- Test Form -->
    <div class="debug-section">
        <h2>6. Test Care Record Saving</h2>
        <form method="POST" class="test-form">
            <input type="hidden" name="test_care_record" value="1">
            
            <label>Care Type:</label>
            <select name="care_type" required>
                <option value="">Select Care Type</option>
                <option value="vital_signs">Vital Signs</option>
                <option value="medication_records">Medication</option>
                <option value="symptom_records">Symptoms</option>
                <option value="line_notifications">LINE Notification</option>
            </select>
            
            <label>Elderly ID:</label>
            <input type="number" name="elderly_id" value="7" required>
            
            <label>Temperature (if vital signs):</label>
            <input type="number" name="temperature" step="0.1" placeholder="36.5">
            
            <label>Heart Rate (if vital signs):</label>
            <input type="number" name="heart_rate" placeholder="72">
            
            <label>Blood Pressure Systolic:</label>
            <input type="number" name="bp_systolic" placeholder="120">
            
            <label>Blood Pressure Diastolic:</label>
            <input type="number" name="bp_diastolic" placeholder="80">
            
            <label>Medication Name (if medication):</label>
            <input type="text" name="medication_name" placeholder="Medicine name">
            
            <label>Dosage (if medication):</label>
            <input type="text" name="dosage" placeholder="1 tablet">
            
            <label>Notes:</label>
            <textarea name="notes" placeholder="Test notes">Debug test from care record debug tool</textarea>
            
            <input type="submit" value="Test Save Care Record">
        </form>
    </div>
    
    <!-- Direct API Test -->
    <div class="debug-section">
        <h2>7. Direct API Test</h2>
        <button onclick="testAPI()">Test API Directly</button>
        <div id="api-result"></div>
    </div>
    
    <script>
    function testAPI() {
        const formData = new FormData();
        formData.append('care_type', 'vital_signs');
        formData.append('elderly_id', '7');
        formData.append('temperature', '36.5');
        formData.append('heart_rate', '72');
        formData.append('notes', 'Direct API test from debug tool');
        
        fetch('api/save_care_record.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            
            document.getElementById('api-result').innerHTML = 
                '<strong>Status:</strong> ' + response.status + '<br>' +
                '<strong>Status Text:</strong> ' + response.statusText + '<br>';
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text(); // Get as text first to see raw response
        })
        .then(data => {
            document.getElementById('api-result').innerHTML += 
                '<strong>Raw Response:</strong><pre>' + data + '</pre>';
            
            try {
                const jsonData = JSON.parse(data);
                document.getElementById('api-result').innerHTML += 
                    '<strong>Parsed JSON:</strong><pre>' + JSON.stringify(jsonData, null, 2) + '</pre>';
            } catch (e) {
                document.getElementById('api-result').innerHTML += 
                    '<strong>JSON Parse Error:</strong> ' + e.message;
            }
        })
        .catch(error => {
            console.error('Fetch Error:', error);
            document.getElementById('api-result').innerHTML += 
                '<strong style="color:red;">Error:</strong> ' + error.message;
        });
    }
    </script>
    
    <div class="debug-section">
        <h2>8. Error Log Monitoring</h2>
        <button onclick="location.reload()">Refresh Debug Info</button>
        <button onclick="clearLogs()">Clear Debug Logs</button>
        
        <?php if (file_exists(__DIR__ . '/logs/debug_errors.log')): ?>
            <h3>Recent Debug Log Entries:</h3>
            <pre style="background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;">
<?php echo htmlspecialchars(file_get_contents(__DIR__ . '/logs/debug_errors.log')); ?>
            </pre>
        <?php endif; ?>
    </div>
    
    <script>
    function clearLogs() {
        fetch('?clear_logs=1', { method: 'GET' })
            .then(() => location.reload());
    }
    </script>
    
    <?php
    // Handle log clearing
    if (isset($_GET['clear_logs'])) {
        file_put_contents(__DIR__ . '/logs/debug_errors.log', '');
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    }
    
    debugLog("Debug tool completed");
    ?>
</body>
</html>