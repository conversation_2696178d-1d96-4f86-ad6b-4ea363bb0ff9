<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Incident Report Modal -->
<div id="incidentReportModal" class="modal" style="display: none;">
    <div class="modal-content incident-report-modal">
        <div class="modal-header">
            <span class="close" onclick="closeIncidentReportModal()">&times;</span>
            <h2>⚠️ รายงานเหตุการณ์</h2>
        </div>
        <form id="incidentReportForm" class="modal-form">
            <input type="hidden" id="incident_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="incident_name">ชื่อเหตุการณ์ <span class="required">*</span></label>
                <input type="text" id="incident_name" name="incident_name" required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="incident_type">ประเภทเหตุการณ์ <span class="required">*</span></label>
                    <select id="incident_type" name="incident_type" required>
                        <option value="">-- เลือกประเภท --</option>
                        <option value="fall">การล้ม</option>
                        <option value="medication_error">ข้อผิดพลาดการให้ยา</option>
                        <option value="injury">การบาดเจ็บ</option>
                        <option value="behavioral">พฤติกรรม</option>
                        <option value="wandering">การหลงเดิน</option>
                        <option value="choking">การสำลัก</option>
                        <option value="seizure">ชัก</option>
                        <option value="allergic_reaction">แพ้</option>
                        <option value="equipment_malfunction">อุปกรณ์ขัดข้อง</option>
                        <option value="visitor_related">เกี่ยวกับผู้เยี่ยม</option>
                        <option value="staff_related">เกี่ยวกับเจ้าหน้าที่</option>
                        <option value="other">อื่นๆ</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="severity">ความรุนแรง <span class="required">*</span></label>
                    <select id="severity" name="severity" required>
                        <option value="minor">เล็กน้อย</option>
                        <option value="moderate">ปานกลาง</option>
                        <option value="serious">รุนแรง</option>
                        <option value="critical">วิกฤติ</option>
                        <option value="fatal">เสียชีวิต</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="incident_datetime">วันที่และเวลาเกิดเหตุ <span class="required">*</span></label>
                    <input type="datetime-local" id="incident_datetime" name="incident_datetime" required>
                </div>
                <div class="form-group">
                    <label for="incident_location">สถานที่เกิดเหตุ <span class="required">*</span></label>
                    <input type="text" id="incident_location" name="incident_location" required placeholder="เช่น ห้องน้ำ, ห้องนอน, โรงอาหาร">
                </div>
            </div>

            <div class="form-group">
                <label for="incident_description">รายละเอียดเหตุการณ์ <span class="required">*</span></label>
                <textarea id="incident_description" name="incident_description" rows="4" required placeholder="อธิบายสิ่งที่เกิดขึ้นอย่างละเอียด..."></textarea>
            </div>

            <div class="form-group">
                <label for="additional_description">คำอธิบายเพิ่มเติม</label>
                <textarea id="additional_description" name="additional_description" rows="3" placeholder="ข้อมูลเพิ่มเติมที่เกี่ยวข้อง..."></textarea>
            </div>

            <div class="form-group">
                <label>การแจ้งเตือน</label>
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="notify_manager" name="notify_manager" value="1">
                        <span class="checkmark"></span>
                        แจ้งผู้จัดการ
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="notify_family" name="notify_family" value="1">
                        <span class="checkmark"></span>
                        แจ้งญาติ
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="notify_doctor" name="notify_doctor" value="1">
                        <span class="checkmark"></span>
                        แจ้งแพทย์
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label for="immediate_action">การแก้ไขที่ได้ทำไปแล้ว <span class="required">*</span></label>
                <textarea id="immediate_action" name="immediate_action" rows="3" required placeholder="อธิบายการดำเนินการเบื้องต้นที่ได้ทำไปแล้ว..."></textarea>
            </div>

            <div class="form-group">
                <label for="improvement_plan">แนวทางการปรับปรุงให้ดีขึ้น</label>
                <textarea id="improvement_plan" name="improvement_plan" rows="3" placeholder="แผนการป้องกันไม่ให้เกิดเหตุการณ์ซ้ำ..."></textarea>
            </div>

            <div class="form-group">
                <label for="incident_images">รูปภาพประกอบ</label>
                <input type="file" id="incident_images" name="incident_images[]" multiple accept="image/*" class="file-input">
                <div class="file-upload-area" onclick="document.getElementById('incident_images').click()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">คลิกเพื่ออัพโหลดรูปภาพ หรือลากไฟล์มาวางที่นี่</div>
                    <div class="upload-info">รองรับไฟล์ JPG, PNG, GIF (สูงสุด 5 ไฟล์)</div>
                </div>
                <div id="imagePreview" class="image-preview"></div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn-secondary" onclick="closeIncidentReportModal()">ยกเลิก</button>
                <button type="submit" class="btn-primary">บันทึกรายงาน</button>
            </div>
        </form>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.incident-report-modal {
    background-color: #fefefe;
    margin: 2% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.modal-form {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.required {
    color: #FF5722;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Sarabun', sans-serif;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #FF5722;
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 400 !important;
    margin-bottom: 0 !important;
    gap: 8px;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.file-input {
    display: none;
}

.file-upload-area {
    border: 2px dashed #e1e5e9;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.file-upload-area:hover {
    border-color: #FF5722;
    background-color: #fff5f3;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.6;
}

.upload-text {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.upload-info {
    font-size: 0.85rem;
    color: #666;
}

.image-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.preview-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e1e5e9;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #FF5722;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.btn-primary,
.btn-secondary {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 87, 34, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .incident-report-modal {
        width: 95%;
        margin: 5% auto;
        max-height: 95vh;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .modal-form {
        padding: 20px;
    }
    
    .checkbox-group {
        flex-direction: column;
        gap: 10px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>