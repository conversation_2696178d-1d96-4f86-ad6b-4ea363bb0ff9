<?php
// API สำหรับบันทึกการพลิกตัว
session_start();
header('Content-Type: application/json; charset=utf-8');

// กำหนดค่าคงที่สำหรับความปลอดภัย
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

try {
    // เชื่อมต่อฐานข้อมูล
    require_once __DIR__ . '/../config/database_simple.php';
    
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้: ' . (isset($conn) ? $conn->connect_error : 'Connection not established'));
    }
    
    // ตรวจสอบ POST method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('ต้องใช้ POST method');
    }
    
    // ตรวจสอบข้อมูลพื้นฐาน
    if (!isset($_POST['elderly_id']) || empty($_POST['elderly_id'])) {
        throw new Exception('ไม่พบรหัสผู้สูงอายุ');
    }
    
    $elderly_id = (int)$_POST['elderly_id'];
    if ($elderly_id <= 0) {
        throw new Exception('รหัสผู้สูงอายุไม่ถูกต้อง');
    }
    
    // ข้อมูลการพลิกตัว
    $turning_position = trim($_POST['turning_position'] ?? '');
    $turning_time = trim($_POST['turning_time'] ?? '');
    $skin_condition = trim($_POST['skin_condition'] ?? '');
    $pressure_relief = trim($_POST['pressure_relief'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    // ตรวจสอบข้อมูลที่จำเป็น
    if (empty($turning_position)) {
        throw new Exception('กรุณาเลือกท่าที่พลิก');
    }
    
    if (empty($turning_time)) {
        throw new Exception('กรุณาระบุเวลาการพลิกตัว');
    }
    
    // วันที่และเวลา
    $recorded_date = $_POST['recorded_date'] ?? date('Y-m-d');
    $recorded_time = $_POST['recorded_time'] ?? date('H:i:s');
    
    // ตรวจสอบโหมดการทำงาน
    $form_mode = $_POST['form_mode'] ?? 'create';
    $record_id = !empty($_POST['record_id']) ? (int)$_POST['record_id'] : null;
    
    // ใช้ข้อมูล user จาก session หรือค่าเริ่มต้น
    $user_id = $_SESSION['user_id'] ?? 1;
    $facility_id = $_SESSION['facility_id'] ?? 1;
    
    // ตรวจสอบว่าตาราง care_turning_records มีอยู่หรือไม่
    $table_check = $conn->query("SHOW TABLES LIKE 'care_turning_records'");
    if ($table_check->num_rows == 0) {
        // สร้างตารางถ้ายังไม่มี
        $create_table_sql = "CREATE TABLE IF NOT EXISTS care_turning_records (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            elderly_id INT(11) NOT NULL,
            user_id INT(11) DEFAULT 1,
            facility_id INT(11) DEFAULT 1,
            turning_position VARCHAR(50) NOT NULL COMMENT 'ท่าที่พลิก: ซ้าย, ขวา, หงาย, คว่ำ',
            turning_time VARCHAR(20) NOT NULL COMMENT 'เวลาการพลิก: ทุก2ชม, ทุก4ชม, ตามอาการ',
            skin_condition VARCHAR(100) NULL COMMENT 'สภาพผิวหนัง: ปกติ, แดง, บวม, แผล',
            pressure_relief VARCHAR(100) NULL COMMENT 'การบรรเทาแรงกด: หมอน, เบาะลม, เปลี่ยนท่า',
            notes TEXT NULL COMMENT 'หมายเหตุเพิ่มเติม',
            recorded_at DATETIME NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_elderly_id (elderly_id),
            INDEX idx_recorded_at (recorded_at),
            INDEX idx_turning_position (turning_position)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if (!$conn->query($create_table_sql)) {
            throw new Exception('ไม่สามารถสร้างตารางได้: ' . $conn->error);
        }
    }

    $recorded_datetime = $recorded_date . ' ' . $recorded_time;

    // ตรวจสอบโหมดการทำงาน - 1 record per day pattern
    if ($form_mode === 'edit' && $record_id) {
        // โหมดแก้ไข - อัปเดตข้อมูลที่มีอยู่
        $sql = "UPDATE care_turning_records SET 
                turning_position = ?, turning_time = ?, skin_condition = ?, 
                pressure_relief = ?, notes = ?, recorded_at = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND elderly_id = ?";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียม SQL สำหรับอัปเดต: ' . $conn->error);
        }
        
        $stmt->bind_param(
            'ssssssii', 
            $turning_position, $turning_time, $skin_condition,
            $pressure_relief, $notes, $recorded_datetime, $record_id, $elderly_id
        );
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'อัปเดตการพลิกตัวสำเร็จ',
                    'mode' => 'edit',
                    'record_id' => $record_id,
                    'data' => [
                        'elderly_id' => $elderly_id,
                        'turning_position' => $turning_position,
                        'turning_time' => $turning_time,
                        'skin_condition' => $skin_condition,
                        'pressure_relief' => $pressure_relief,
                        'recorded_at' => $recorded_datetime,
                        'user_id' => $user_id
                    ]
                ]);
            } else {
                throw new Exception('ไม่พบข้อมูลที่ต้องการอัปเดตหรือไม่มีการเปลี่ยนแปลง');
            }
        } else {
            throw new Exception('ไม่สามารถอัปเดตข้อมูลได้: ' . $stmt->error);
        }
    } else {
        // โหมดสร้างใหม่ - ตรวจสอบ one record per day ก่อน
        $today = date('Y-m-d', strtotime($recorded_datetime));
        
        // ตรวจสอบว่าวันนี้มีข้อมูลแล้วหรือไม่
        $check_sql = "SELECT id FROM care_turning_records WHERE elderly_id = ? AND DATE(recorded_at) = ?";
        $check_stmt = $conn->prepare($check_sql);
        if (!$check_stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการตรวจสอบข้อมูลซ้ำ: ' . $conn->error);
        }
        
        $check_stmt->bind_param('is', $elderly_id, $today);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            // มีข้อมูลวันนี้แล้ว - อัปเดตแทนการสร้างใหม่
            $existing_record = $check_result->fetch_assoc();
            $existing_id = $existing_record['id'];
            
            $update_sql = "UPDATE care_turning_records SET 
                    turning_position = ?, turning_time = ?, skin_condition = ?, 
                    pressure_relief = ?, notes = ?, recorded_at = ?, 
                    user_id = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ? AND elderly_id = ?";
            
            $update_stmt = $conn->prepare($update_sql);
            if (!$update_stmt) {
                throw new Exception('เกิดข้อผิดพลาดในการเตรียม SQL สำหรับอัปเดต: ' . $conn->error);
            }
            
            $update_stmt->bind_param(
                'ssssssiil', 
                $turning_position, $turning_time, $skin_condition,
                $pressure_relief, $notes, $recorded_datetime, $user_id, $existing_id, $elderly_id
            );
            
            if ($update_stmt->execute()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'อัปเดตการพลิกตัวในวันเดียวกันสำเร็จ (1 record per day)',
                    'mode' => 'update_existing',
                    'record_id' => $existing_id,
                    'data' => [
                        'elderly_id' => $elderly_id,
                        'turning_position' => $turning_position,
                        'turning_time' => $turning_time,
                        'skin_condition' => $skin_condition,
                        'pressure_relief' => $pressure_relief,
                        'recorded_at' => $recorded_datetime,
                        'user_id' => $user_id
                    ]
                ]);
            } else {
                throw new Exception('ไม่สามารถอัปเดตข้อมูลที่มีอยู่ได้: ' . $update_stmt->error);
            }
        } else {
            // ไม่มีข้อมูลวันนี้ - สร้างใหม่
            $sql = "INSERT INTO care_turning_records (
                elderly_id, user_id, facility_id, 
                turning_position, turning_time, skin_condition, 
                pressure_relief, notes, recorded_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception('เกิดข้อผิดพลาดในการเตรียม SQL สำหรับสร้างใหม่: ' . $conn->error);
            }
            
            $stmt->bind_param(
                'iiissssss', 
                $elderly_id, $user_id, $facility_id,
                $turning_position, $turning_time, $skin_condition,
                $pressure_relief, $notes, $recorded_datetime
            );
            
            if ($stmt->execute()) {
                $record_id = $conn->insert_id;
                
                echo json_encode([
                    'success' => true,
                    'message' => 'บันทึกการพลิกตัวใหม่สำเร็จ',
                    'mode' => 'create',
                    'record_id' => $record_id,
                    'data' => [
                        'elderly_id' => $elderly_id,
                        'turning_position' => $turning_position,
                        'turning_time' => $turning_time,
                        'skin_condition' => $skin_condition,
                        'pressure_relief' => $pressure_relief,
                        'recorded_at' => $recorded_datetime,
                        'user_id' => $user_id
                    ]
                ]);
            } else {
                throw new Exception('ไม่สามารถบันทึกข้อมูลใหม่ได้: ' . $stmt->error);
            }
        }
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'post_data' => $_POST,
            'session_data' => [
                'user_id' => $_SESSION['user_id'] ?? 'ไม่มี',
                'facility_id' => $_SESSION['facility_id'] ?? 'ไม่มี'
            ]
        ]
    ]);
}
?>