<!-- Symptoms Recording Modal -->
<div id="symptomsModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <div>
                <h2>📋 บันทึกอาการ</h2>
                <div id="symptomsSummary" class="symptoms-summary">
                    <span id="symptomsSummaryText">กำลังโหลด...</span>
                </div>
            </div>
            <span class="close" onclick="closeSymptomsModal()">&times;</span>
        </div>
        
        <!-- Previous Symptoms Section -->
        <div id="previousSymptomsSection" class="previous-symptoms-section" style="display: none;">
            <h3>📝 การบันทึกอาการก่อนหน้า</h3>
            <div id="previousSymptomsList" class="previous-symptoms-list">
                <!-- จะถูกโหลดด้วย JavaScript -->
            </div>
        </div>
        
        <form id="symptomsForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" id="symptoms_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="symptoms_date">วันที่บันทึก:</label>
                <input type="date" id="symptoms_date" name="record_date" required>
            </div>

            <div class="form-group">
                <label for="symptoms_time">เวลา (ไม่บังคับ):</label>
                <input type="time" id="symptoms_time" name="record_time">
            </div>

            <div class="form-group">
                <label for="symptoms_description">อาการที่พบ:</label>
                <textarea id="symptoms_description" name="symptoms" rows="4" placeholder="บรรยายอาการที่พบ เช่น ปวดหัว ไข้ เหนื่อยง่าย คลื่นไส้" required></textarea>
            </div>

            <div class="form-group">
                <label for="symptoms_severity">ระดับความรุนแรง:</label>
                <div class="severity-grid">
                    <div class="severity-card" data-severity="mild">
                        <input type="radio" id="severity_mild" name="severity" value="mild" checked>
                        <label for="severity_mild" class="severity-label">
                            <div class="severity-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                <span class="icon">😌</span>
                            </div>
                            <div class="severity-text">เบา</div>
                            <div class="severity-desc">ไม่รุนแรง</div>
                        </label>
                    </div>
                    
                    <div class="severity-card" data-severity="moderate">
                        <input type="radio" id="severity_moderate" name="severity" value="moderate">
                        <label for="severity_moderate" class="severity-label">
                            <div class="severity-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                <span class="icon">😐</span>
                            </div>
                            <div class="severity-text">ปานกลาง</div>
                            <div class="severity-desc">ต้องติดตาม</div>
                        </label>
                    </div>
                    
                    <div class="severity-card" data-severity="severe">
                        <input type="radio" id="severity_severe" name="severity" value="severe">
                        <label for="severity_severe" class="severity-label">
                            <div class="severity-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                <span class="icon">😰</span>
                            </div>
                            <div class="severity-text">รุนแรง</div>
                            <div class="severity-desc">ต้องดูแลเร่งด่วน</div>
                        </label>
                    </div>
                    
                    <div class="severity-card" data-severity="critical">
                        <input type="radio" id="severity_critical" name="severity" value="critical">
                        <label for="severity_critical" class="severity-label">
                            <div class="severity-icon" style="background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);">
                                <span class="icon">😨</span>
                            </div>
                            <div class="severity-text">วิกฤต</div>
                            <div class="severity-desc">ต้องการการรักษาทันที</div>
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="symptoms_temperature">อุณหภูมิร่างกาย (°C):</label>
                <input type="number" id="symptoms_temperature" name="temperature" step="0.1" min="30" max="45" placeholder="36.5">
            </div>

            <div class="form-group">
                <label for="symptoms_notes">หมายเหตุเพิ่มเติม:</label>
                <textarea id="symptoms_notes" name="notes" rows="3" placeholder="รายละเอียดเพิ่มเติม การรักษาเบื้องต้น หรือข้อสังเกต"></textarea>
            </div>

            <div class="form-group">
                <label for="symptoms_images">รูปภาพประกอบ (ไม่บังคับ):</label>
                <input type="file" id="symptoms_images" name="symptoms_images[]" multiple accept="image/*" class="file-input">
                <div class="file-upload-area" onclick="document.getElementById('symptoms_images').click()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">
                        <strong>คลิกเพื่อเลือกรูปภาพ</strong><br>
                        <small>รองรับ JPG, PNG, GIF (สูงสุด 5 รูป)</small>
                    </div>
                </div>
                <div id="symptomsImagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label for="symptoms_recorded_by">ผู้บันทึก:</label>
                <input type="text" id="symptoms_recorded_by" name="recorded_by_name" value="<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>" readonly>
            </div>

            <div class="modal-actions">
                <button type="button" onclick="closeSymptomsModal()" class="btn-cancel">ยกเลิก</button>
                <button type="submit" class="btn-save">บันทึกอาการ</button>
            </div>
        </form>
    </div>
</div>

<style>
.symptoms-summary {
    font-size: 0.9rem;
    margin-top: 5px;
    opacity: 0.9;
}

.symptoms-summary-stats {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    font-size: 0.85rem;
}

.symptoms-summary-stats span {
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(255,255,255,0.7);
}

.previous-symptoms-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.previous-symptoms-section h3 {
    margin: 0 0 15px 0;
    color: #856404;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.previous-symptoms-list {
    display: grid;
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.symptom-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.symptom-item:hover {
    border-color: #607D8B;
    box-shadow: 0 2px 8px rgba(96, 125, 139, 0.1);
}

.symptom-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.symptom-date-time {
    font-weight: 600;
    color: #2c3e50;
}

.symptom-severity {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.symptom-severity.mild { background: #4CAF50; }
.symptom-severity.moderate { background: #FF9800; }
.symptom-severity.severe { background: #FF5722; }
.symptom-severity.critical { background: #F44336; }

.symptom-description {
    font-size: 0.85rem;
    color: #2c3e50;
    margin-bottom: 6px;
    line-height: 1.3;
    font-weight: 500;
}

.symptom-details {
    font-size: 0.75rem;
    color: #6c757d;
    line-height: 1.2;
}


.severity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.severity-card {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.severity-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.severity-card input[type="radio"] {
    display: none;
}

.severity-card input[type="radio"]:checked + .severity-label {
    background: rgba(0,0,0,0.05);
}

.severity-card input[type="radio"]:checked + .severity-label .severity-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.severity-label {
    display: block;
    text-align: center;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.severity-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    transition: all 0.3s ease;
}

.severity-icon .icon {
    font-size: 1.5rem;
    filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
}

.severity-text {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.severity-desc {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.2;
}

.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.file-upload-area:hover {
    border-color: #607D8B;
    background: rgba(96, 125, 139, 0.05);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    color: #6c757d;
}

.upload-text {
    color: #6c757d;
}

.upload-text strong {
    color: #607D8B;
}

.file-input {
    display: none;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.preview-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-remove:hover {
    background: #c82333;
}

@media (max-width: 768px) {
    .severity-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .severity-icon {
        width: 40px;
        height: 40px;
    }
    
    .severity-icon .icon {
        font-size: 1.2rem;
    }
    
    .image-preview {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }
    
    .preview-item {
        width: 80px;
        height: 80px;
    }
}
</style>