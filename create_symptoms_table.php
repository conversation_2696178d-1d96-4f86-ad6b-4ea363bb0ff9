<?php
// สร้างตาราง symptoms_records สำหรับบันทึกอาการ
define('AIVORA_SECURITY', true);

require_once 'config/database.php';

// สร้างตาราง symptoms_records
$create_table_sql = "
CREATE TABLE IF NOT EXISTS symptoms_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    elderly_id INT NOT NULL,
    record_date DATE NOT NULL,
    record_time TIME DEFAULT NULL,
    symptoms TEXT NOT NULL,
    severity ENUM('mild', 'moderate', 'severe', 'critical') DEFAULT 'mild',
    temperature DECIMAL(4,1) DEFAULT NULL,
    notes TEXT DEFAULT NULL,
    image_path VARCHAR(500) DEFAULT NULL,
    recorded_by INT NOT NULL,
    recorded_by_name VARCHAR(100) NOT NULL,
    recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_datetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_elderly_date (elderly_id, record_date),
    INDEX idx_severity (severity),
    INDEX idx_recorded_datetime (recorded_datetime),
    INDEX idx_elderly_symptoms (elderly_id, symptoms(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
";

try {
    if ($conn->query($create_table_sql)) {
        echo "✅ สร้างตาราง symptoms_records สำเร็จ\n";
        
        // ตรวจสอบโครงสร้างตาราง
        $describe = $conn->query("DESCRIBE symptoms_records");
        if ($describe) {
            echo "\n📋 โครงสร้างตาราง symptoms_records:\n";
            echo str_pad("Field", 25) . str_pad("Type", 30) . str_pad("Null", 8) . "Default\n";
            echo str_repeat("-", 70) . "\n";
            
            while ($row = $describe->fetch_assoc()) {
                echo str_pad($row['Field'], 25) . 
                     str_pad($row['Type'], 30) . 
                     str_pad($row['Null'], 8) . 
                     $row['Default'] . "\n";
            }
        }
        
        // สร้างตัวอย่างข้อมูลสำหรับทดสอบ (ถ้าตารางว่าง)
        $count_check = $conn->query("SELECT COUNT(*) as total FROM symptoms_records");
        if ($count_check) {
            $count = $count_check->fetch_assoc()['total'];
            if ($count == 0) {
                echo "\n➕ เพิ่มข้อมูลตัวอย่างสำหรับทดสอบ...\n";
                
                $sample_data = [
                    [1, date('Y-m-d'), '14:30:00', 'ปวดหัว เหนื่อยง่าย', 'mild', 36.8, 'ผู้ป่วยบอกว่าปวดหัวเล็กน้อย', 1, 'Admin User'],
                    [1, date('Y-m-d', strtotime('-1 day')), '09:15:00', 'ไข้ มีน้ำมูก', 'moderate', 38.2, 'ไข้สูง ควรติดตาม', 1, 'Admin User']
                ];
                
                $insert_sql = "INSERT INTO symptoms_records (elderly_id, record_date, record_time, symptoms, severity, temperature, notes, recorded_by, recorded_by_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_sql);
                
                foreach ($sample_data as $data) {
                    $stmt->bind_param('issssdsis', ...$data);
                    if ($stmt->execute()) {
                        echo "   ✓ เพิ่มข้อมูลตัวอย่าง: {$data[3]}\n";
                    }
                }
            } else {
                echo "\n📊 ตารางมีข้อมูลอยู่แล้ว: $count รายการ\n";
            }
        }
        
    } else {
        echo "❌ เกิดข้อผิดพลาดในการสร้างตาราง: " . $conn->error . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

$conn->close();
echo "\n✅ เสร็จสิ้นการสร้างตาราง symptoms_records\n";
?>