<?php
// ป้องกันการเรียกใช้ไฟล์โดยตรงผ่าน URL
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'test_user';

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

try {
    echo "🌙 Direct Sleep System Test\n";
    echo "===========================\n";
    
    // ทดสอบการบันทึกโดยตรง
    $elderly_id = 6;
    $record_date = date('Y-m-d');
    $record_time = '22:30';
    $sleep_quality = 'นอนหลับสนิทดี';
    $additional_notes = 'ทดสอบการบันทึกโดยตรง';
    $recorded_by = $_SESSION['user_id'];
    $recorded_by_name = $_SESSION['username'];
    $recorded_datetime = date('Y-m-d H:i:s');
    
    // ตรวจสอบว่ามีข้อมูลวันนี้แล้วหรือไม่
    $check_sql = "SELECT id FROM care_sleep WHERE elderly_id = ? AND record_date = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("is", $elderly_id, $record_date);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        // อัพเดทข้อมูลเดิม
        $existing_row = $check_result->fetch_assoc();
        $update_sql = "UPDATE care_sleep SET 
                          record_time = ?, sleep_quality = ?, additional_notes = ?,
                          recorded_by = ?, recorded_by_name = ?, updated_at = ?
                       WHERE id = ?";
        
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ssssssi", $record_time, $sleep_quality, $additional_notes, 
                                 $recorded_by, $recorded_by_name, $recorded_datetime, 
                                 $existing_row['id']);
        
        if ($update_stmt->execute()) {
            echo "✅ อัพเดทข้อมูลการนอนหลับสำเร็จ (ID: {$existing_row['id']})\n";
            $sleep_id = $existing_row['id'];
        } else {
            throw new Exception('ไม่สามารถอัพเดทข้อมูลได้: ' . $update_stmt->error);
        }
    } else {
        // บันทึกข้อมูลใหม่
        $insert_sql = "INSERT INTO care_sleep (
                          elderly_id, record_date, record_time, sleep_quality,
                          additional_notes, recorded_by, recorded_by_name, recorded_datetime
                       ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("isssssss", $elderly_id, $record_date, $record_time, $sleep_quality,
                                 $additional_notes, $recorded_by, $recorded_by_name, $recorded_datetime);
        
        if ($insert_stmt->execute()) {
            $sleep_id = $conn->insert_id;
            echo "✅ บันทึกข้อมูลการนอนหลับสำเร็จ (ID: $sleep_id)\n";
        } else {
            throw new Exception('ไม่สามารถบันทึกข้อมูลได้: ' . $insert_stmt->error);
        }
    }
    
    // ดึงข้อมูลที่บันทึกกลับมาตรวจสอบ
    $select_sql = "SELECT * FROM care_sleep WHERE id = ?";
    $select_stmt = $conn->prepare($select_sql);
    $select_stmt->bind_param("i", $sleep_id);
    $select_stmt->execute();
    $result = $select_stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        echo "\n📋 ข้อมูลที่บันทึก:\n";
        echo "   วันที่: " . $row['record_date'] . "\n";
        echo "   เวลา: " . $row['record_time'] . "\n";
        echo "   คุณภาพการนอน: " . $row['sleep_quality'] . "\n";
        echo "   หมายเหตุ: " . $row['additional_notes'] . "\n";
        echo "   ผู้บันทึก: " . $row['recorded_by_name'] . "\n";
        echo "   วันเวลาที่บันทึก: " . $row['recorded_datetime'] . "\n";
    }
    
    // ดึงสถิติการนอนหลับ
    $stats_sql = "SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN sleep_quality = 'นอนหลับสนิทดี' THEN 1 END) as excellent_count,
                    COUNT(CASE WHEN sleep_quality = 'นอนไม่ค่อยหลับ' THEN 1 END) as poor_count,
                    COUNT(CASE WHEN sleep_quality = 'นอนไม่หลับ' THEN 1 END) as bad_count,
                    COUNT(CASE WHEN sleep_quality = 'หลับๆตื่นๆ' THEN 1 END) as interrupted_count,
                    COUNT(CASE WHEN sleep_quality = 'อื่นๆ' THEN 1 END) as other_count
                  FROM care_sleep 
                  WHERE elderly_id = ?";
    
    $stats_stmt = $conn->prepare($stats_sql);
    $stats_stmt->bind_param("i", $elderly_id);
    $stats_stmt->execute();
    $stats_result = $stats_stmt->get_result();
    
    if ($stats = $stats_result->fetch_assoc()) {
        echo "\n📊 สถิติการนอนหลับของผู้สูงอายุ ID $elderly_id:\n";
        echo "   ทั้งหมด: " . $stats['total_records'] . " ครั้ง\n";
        echo "   😴 นอนหลับสนิทดี: " . $stats['excellent_count'] . " ครั้ง\n";
        echo "   😪 นอนไม่ค่อยหลับ: " . $stats['poor_count'] . " ครั้ง\n";
        echo "   😵 นอนไม่หลับ: " . $stats['bad_count'] . " ครั้ง\n";
        echo "   🔄 หลับๆตื่นๆ: " . $stats['interrupted_count'] . " ครั้ง\n";
        echo "   ❓ อื่นๆ: " . $stats['other_count'] . " ครั้ง\n";
    }
    
    // ทดสอบการดึงข้อมูลล่าสุด
    $latest_sql = "SELECT * FROM care_sleep 
                   WHERE elderly_id = ? 
                   ORDER BY record_date DESC, record_time DESC 
                   LIMIT 5";
    
    $latest_stmt = $conn->prepare($latest_sql);
    $latest_stmt->bind_param("i", $elderly_id);
    $latest_stmt->execute();
    $latest_result = $latest_stmt->get_result();
    
    echo "\n📅 ประวัติการนอนหลับล่าสุด:\n";
    while ($row = $latest_result->fetch_assoc()) {
        $date_thai = date('d/m/Y', strtotime($row['record_date']));
        $time_thai = $row['record_time'] ? date('H:i', strtotime($row['record_time'])) : '';
        
        echo "   $date_thai $time_thai - {$row['sleep_quality']}";
        if (!empty($row['additional_notes'])) {
            $notes = strlen($row['additional_notes']) > 30 ? 
                     substr($row['additional_notes'], 0, 30) . '...' : 
                     $row['additional_notes'];
            echo " ($notes)";
        }
        echo "\n";
    }
    
    echo "\n🎉 ระบบการนอนหลับทำงานได้ถูกต้อง!\n";
    echo "✨ Features ที่ทำงาน:\n";
    echo "   ✅ บันทึกข้อมูลการนอนหลับ\n";
    echo "   ✅ จำกัดหนึ่งรายการต่อวัน (UNIQUE constraint)\n";
    echo "   ✅ อัพเดทข้อมูลเมื่อบันทึกซ้ำ\n";
    echo "   ✅ คำนวณสถิติการนอนหลับ\n";
    echo "   ✅ ดึงประวัติการนอนหลับ\n";
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    exit(1);
}
?>