-- สร้างตารางสำหรับบันทึกสภาวะจิตใจ/อารมณ์
CREATE TABLE IF NOT EXISTS care_mental_state (
    id INT PRIMARY KEY AUTO_INCREMENT,
    elderly_id INT NOT NULL,
    record_date DATE NOT NULL,
    record_time TIME NULL,
    
    -- สภาวะจิตใจ (เลือกได้หลายข้อ - เก็บเป็น JSON หรือ SET)
    mental_conditions SET(
        'ตื่นตัวดี',
        'รู้สึกตัวดี', 
        'อารมณ์แจ่มใส',
        'ตอบสนองดี',
        'ไม่ค่อยตื่นตัว',
        'ซึมลง',
        'สับสน',
        'มีอาการเพ้อ',
        'ไม่ตอบสนอง',
        'โวยวาย',
        'อื่นๆ'
    ) NOT NULL,
    
    -- คำอธิบายเพิ่มเติม
    additional_notes TEXT NULL,
    
    -- ข้อมูลการบันทึก
    recorded_by INT NOT NULL,
    recorded_by_name VARCHAR(255) NOT NULL,
    recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- อัพเดทล่าสุด
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes
    INDEX idx_elderly_date (elderly_id, record_date),
    INDEX idx_record_date (record_date),
    INDEX idx_recorded_datetime (recorded_datetime)
);