<?php
// ป้องกันการเรียกใช้ไฟล์โดยตรงผ่าน URL
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'test_user';

// Mock POST data for testing
$_POST = [
    'elderly_id' => 6,
    'record_date' => '2024-01-15',
    'record_time' => '14:30',
    'mental_conditions' => ['ตื่นตัวดี', 'อารมณ์แจ่มใส'],
    'additional_notes' => 'ทดสอบการบันทึกข้อมูล',
    'recorded_by_name' => 'test_user'
];

echo "🔍 Debug Mental State Save Process\n";
echo "==================================\n";

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

try {
    // ตรวจสอบค่าที่ได้รับ
    echo "📥 ข้อมูลที่ได้รับ:\n";
    foreach ($_POST as $key => $value) {
        if (is_array($value)) {
            echo "  $key: [" . implode(', ', $value) . "]\n";
        } else {
            echo "  $key: $value\n";
        }
    }
    
    // รับและตรวจสอบข้อมูลจากฟอร์ม
    $elderly_id = filter_input(INPUT_POST, 'elderly_id', FILTER_VALIDATE_INT);
    $record_date = filter_input(INPUT_POST, 'record_date', FILTER_SANITIZE_STRING);
    $record_time = filter_input(INPUT_POST, 'record_time', FILTER_SANITIZE_STRING);
    $mental_conditions = $_POST['mental_conditions'] ?? [];
    $additional_notes = filter_input(INPUT_POST, 'additional_notes', FILTER_SANITIZE_STRING);
    $recorded_by_name = filter_input(INPUT_POST, 'recorded_by_name', FILTER_SANITIZE_STRING);
    
    echo "\n🔄 ข้อมูลหลังการ filter:\n";
    echo "  elderly_id: $elderly_id\n";
    echo "  record_date: $record_date\n";
    echo "  record_time: $record_time\n";
    echo "  mental_conditions: [" . implode(', ', $mental_conditions) . "]\n";
    echo "  additional_notes: $additional_notes\n";
    echo "  recorded_by_name: $recorded_by_name\n";
    
    // ตรวจสอบและทำความสะอาดข้อมูล mental_conditions
    $valid_conditions = [
        'ตื่นตัวดี', 'รู้สึกตัวดี', 'อารมณ์แจ่มใส', 'ตอบสนองดี',
        'ไม่ค่อยตื่นตัว', 'ซึมลง', 'สับสน', 'มีอาการเพ้อ', 
        'ไม่ตอบสนอง', 'โวยวาย', 'อื่นๆ'
    ];
    
    echo "\n✅ ค่าที่อนุญาต:\n";
    foreach ($valid_conditions as $i => $condition) {
        echo "  " . ($i + 1) . ". '$condition' (length: " . strlen($condition) . ")\n";
    }
    
    $cleaned_conditions = [];
    echo "\n🧹 การทำความสะอาดข้อมูล:\n";
    foreach ($mental_conditions as $condition) {
        $original = $condition;
        $condition = trim($condition);
        
        echo "  Original: '$original' (length: " . strlen($original) . ")\n";
        echo "  Trimmed: '$condition' (length: " . strlen($condition) . ")\n";
        
        // Debug: แสดง byte values
        echo "  Bytes: ";
        for ($i = 0; $i < strlen($condition); $i++) {
            echo ord($condition[$i]) . " ";
        }
        echo "\n";
        
        if (in_array($condition, $valid_conditions)) {
            $cleaned_conditions[] = $condition;
            echo "  ✅ Valid - Added to cleaned array\n";
        } else {
            echo "  ❌ Invalid - Not in valid conditions\n";
            
            // ลองหาค่าที่ใกล้เคียง
            foreach ($valid_conditions as $valid) {
                if (similar_text($condition, $valid, $percent) && $percent > 80) {
                    echo "    Similar to '$valid' (${percent}%)\n";
                }
            }
        }
        echo "  ---\n";
    }
    
    echo "\n🎯 ผลลัพธ์สุดท้าย:\n";
    echo "  Cleaned conditions: [" . implode(', ', $cleaned_conditions) . "]\n";
    echo "  Count: " . count($cleaned_conditions) . "\n";
    
    if (empty($cleaned_conditions)) {
        echo "❌ ไม่มีสภาวะที่ถูกต้อง\n";
    } else {
        // แปลง array เป็น string สำหรับ SET field
        $conditions_string = implode(',', $cleaned_conditions);
        echo "  Conditions string for DB: '$conditions_string'\n";
        
        // ทดสอบ query
        echo "\n💾 ทดสอบการบันทึก...\n";
        $sql = "INSERT INTO care_mental_state (
                    elderly_id, record_date, record_time, mental_conditions,
                    additional_notes, recorded_by, recorded_by_name, recorded_datetime
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error);
        }
        
        $recorded_by = $_SESSION['user_id'];
        $recorded_datetime = date('Y-m-d H:i:s');
        
        echo "  SQL prepared successfully\n";
        echo "  Parameters:\n";
        echo "    elderly_id: $elderly_id\n";
        echo "    record_date: $record_date\n";
        echo "    record_time: $record_time\n";
        echo "    conditions_string: $conditions_string\n";
        echo "    additional_notes: $additional_notes\n";
        echo "    recorded_by: $recorded_by\n";
        echo "    recorded_by_name: $recorded_by_name\n";
        echo "    recorded_datetime: $recorded_datetime\n";
        
        $stmt->bind_param(
            "isssssss",
            $elderly_id,
            $record_date,
            $record_time,
            $conditions_string,
            $additional_notes,
            $recorded_by,
            $recorded_by_name,
            $recorded_datetime
        );
        
        if ($stmt->execute()) {
            echo "✅ บันทึกข้อมูลสำเร็จ! ID: " . $conn->insert_id . "\n";
        } else {
            echo "❌ เกิดข้อผิดพลาดในการบันทึก: " . $stmt->error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
}
?>