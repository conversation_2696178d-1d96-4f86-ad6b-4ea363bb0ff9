<?php
// Use same session configuration as main app
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', '1');
    ini_set('session.cookie_secure', (isset($_SERVER['HTTPS']) ? '1' : '0'));
    ini_set('session.cookie_samesite', 'Strict');
    ini_set('session.use_strict_mode', '1');
    ini_set('session.cookie_lifetime', '0');
    ini_set('session.gc_maxlifetime', '3600');
    ini_set('session.name', 'AIVORA_SESSION');
    session_start();
}

require_once '../config/database.php';

header('Content-Type: application/json');

// Basic authentication check
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'กรุณาเข้าสู่ระบบใหม่']);
    exit();
}

if (!isset($_GET['elderly_id']) || !isset($_GET['date'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ครบถ้วน']);
    exit();
}

$elderly_id = intval($_GET['elderly_id']);
$date = $_GET['date'];

// Helper function to safely query tables
function safeTableQuery($conn, $tableName, $sql, $params, $types) {
    try {
        // Check if table exists
        $table_check = $conn->query("SHOW TABLES LIKE '$tableName'");
        if ($table_check->num_rows == 0) {
            return []; // Table doesn't exist, return empty array
        }
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Failed to prepare statement for table $tableName: " . $conn->error);
            return [];
        }
        
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        if (!$stmt->execute()) {
            error_log("Failed to execute query for table $tableName: " . $stmt->error);
            $stmt->close();
            return [];
        }
        
        $result = $stmt->get_result();
        $data = $result->fetch_all(MYSQLI_ASSOC);
        $stmt->close();
        
        return $data;
        
    } catch (Exception $e) {
        error_log("Error querying table $tableName: " . $e->getMessage());
        return [];
    }
}

try {
    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริง
    $sql_check = "SELECT id FROM elderly WHERE id = ?";
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        throw new Exception('เกิดข้อผิดพลาดในการตรวจสอบข้อมูล');
    }
    
    $stmt_check->bind_param("i", $elderly_id);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้สูงอายุ']);
        exit();
    }
    $stmt_check->close();

    $records = [];

    // 1. สัญญาณชีพ (care_vital_signs)
    $sql = "SELECT 'vital_signs' as type, id, recorded_at as recorded_datetime, temperature, heart_rate, 
               blood_pressure_systolic, blood_pressure_diastolic, respiratory_rate, 
               oxygen_saturation, blood_sugar, notes as additional_notes
        FROM care_vital_signs 
        WHERE elderly_id = ? AND DATE(recorded_at) = ?
        ORDER BY recorded_at DESC";
    $records['vital_signs'] = safeTableQuery($conn, 'care_vital_signs', $sql, [$elderly_id, $date], "is");

    // 2. บันทึกการให้ยา (medication_records)
    $sql = "SELECT 'medication' as type, id, recorded_datetime, 
               medication_name, medication_dosage as dosage, medication_route, 
               administration_time, given_by, notes as additional_notes
        FROM medication_records 
        WHERE elderly_id = ? AND DATE(medication_date) = ?
        ORDER BY recorded_datetime DESC";
    $records['medication'] = safeTableQuery($conn, 'medication_records', $sql, [$elderly_id, $date], "is");

    // 3. บันทึกอาการ (symptoms_records)
    $sql = "SELECT 'symptoms' as type, id, recorded_datetime, symptoms, severity, 
               temperature, notes as additional_notes
        FROM symptoms_records 
        WHERE elderly_id = ? AND DATE(record_date) = ?
        ORDER BY recorded_datetime DESC";
    $records['symptoms'] = safeTableQuery($conn, 'symptoms_records', $sql, [$elderly_id, $date], "is");

    // 4. การขับถ่าย (excretion_records)
    $sql = "SELECT 'excretion' as type, id, recorded_datetime, bowel_movement, 
               bowel_consistency, bowel_color, urine_output, urine_color, 
               urine_method, notes as additional_notes
        FROM excretion_records 
        WHERE elderly_id = ? AND DATE(recorded_datetime) = ?
        ORDER BY recorded_datetime DESC";
    $records['excretion'] = safeTableQuery($conn, 'excretion_records', $sql, [$elderly_id, $date], "is");

    // 5. สุขอนามัย (hygiene_records)
    $sql = "SELECT 'hygiene' as type, id, recorded_datetime, bath_type, oral_care, 
               nail_care, hair_care, skin_condition, notes as additional_notes
        FROM hygiene_records 
        WHERE elderly_id = ? AND DATE(recorded_datetime) = ?
        ORDER BY recorded_datetime DESC";
    $records['hygiene'] = safeTableQuery($conn, 'hygiene_records', $sql, [$elderly_id, $date], "is");

    // 6. ทาน/ฟีดอาหาร (feeding_records)
    $sql = "SELECT 'feeding' as type, id, recorded_datetime, meal_type, food_type, 
               amount_consumed, feeding_method, assistance_level, appetite, 
               swallowing_ability, notes as additional_notes
        FROM feeding_records 
        WHERE elderly_id = ? AND DATE(recorded_datetime) = ?
        ORDER BY recorded_datetime DESC";
    $records['feeding'] = safeTableQuery($conn, 'feeding_records', $sql, [$elderly_id, $date], "is");

    // 7. การนอนหลับ (sleep_records)
    $sql = "SELECT 'sleep' as type, id, recorded_datetime, bedtime, wake_time, 
               total_sleep_hours, sleep_quality, disturbances, nap_time, 
               nap_duration_minutes, notes as additional_notes
        FROM sleep_records 
        WHERE elderly_id = ? AND DATE(recorded_datetime) = ?
        ORDER BY recorded_datetime DESC";
    $records['sleep'] = safeTableQuery($conn, 'sleep_records', $sql, [$elderly_id, $date], "is");

    // 8. กิจกรรม (activities_records)
    $sql = "SELECT 'activities' as type, id, recorded_datetime, activity_type, 
               duration_minutes, participation_level, mood_during_activity, 
               physical_response, notes as additional_notes
        FROM activities_records 
        WHERE elderly_id = ? AND DATE(recorded_datetime) = ?
        ORDER BY recorded_datetime DESC";
    $records['activities'] = safeTableQuery($conn, 'activities_records', $sql, [$elderly_id, $date], "is");

    // 9. การพลิกตัว (care_turning_records)
    $sql = "SELECT 'turning' as type, id, recorded_at as recorded_datetime, turning_position, 
               turning_time, skin_condition, notes as additional_notes
        FROM care_turning_records 
        WHERE elderly_id = ? AND DATE(recorded_at) = ?
        ORDER BY recorded_at DESC";
    $records['turning'] = safeTableQuery($conn, 'care_turning_records', $sql, [$elderly_id, $date], "is");

    // 10. แผลกดทับ (care_pressure_sores)
    $sql = "SELECT 'pressure_sore' as type, id, recorded_at as recorded_datetime, sore_location as location, 
               sore_stage as stage, sore_size_length, sore_size_width, sore_size_depth, 
               wound_condition, treatment_applied, notes as additional_notes
        FROM care_pressure_sores 
        WHERE elderly_id = ? AND DATE(recorded_at) = ?
        ORDER BY recorded_at DESC";
    $records['pressure_sore'] = safeTableQuery($conn, 'care_pressure_sores', $sql, [$elderly_id, $date], "is");

    // 11. น้ำหนักและส่วนสูง (care_weight_height)
    $sql = "SELECT 'weight_height' as type, id, recorded_at as recorded_datetime, weight, height, 
               bmi, notes as additional_notes
        FROM care_weight_height 
        WHERE elderly_id = ? AND DATE(recorded_at) = ?
        ORDER BY recorded_at DESC";
    $records['weight_height'] = safeTableQuery($conn, 'care_weight_height', $sql, [$elderly_id, $date], "is");

    // 12. รายงานเหตุการณ์ (care_incident_reports)
    $sql = "SELECT 'incident' as type, id, recorded_at as recorded_datetime, 
               incident_type, incident_description as description, action_taken, severity, 
               notes as additional_notes
        FROM care_incident_reports 
        WHERE elderly_id = ? AND DATE(recorded_at) = ?
        ORDER BY recorded_at DESC";
    $records['incident'] = safeTableQuery($conn, 'care_incident_reports', $sql, [$elderly_id, $date], "is");

    // 13. คุณสมบัติของเสมหะ (sputum_records)
    $sql = "SELECT 'sputum' as type, id, recorded_datetime, sputum_color, 
               sputum_consistency, sputum_amount, blood_present, 
               cough_frequency, notes as additional_notes
        FROM sputum_records 
        WHERE elderly_id = ? AND DATE(recorded_datetime) = ?
        ORDER BY recorded_datetime DESC";
    $records['sputum'] = safeTableQuery($conn, 'sputum_records', $sql, [$elderly_id, $date], "is");

    // 14. สภาวะจิตใจ/อารมณ์ (mental_state_records)
    $sql = "SELECT 'mental_state' as type, id, recorded_datetime, mood, 
               cognitive_function, behavioral_changes, social_interaction, 
               sleep_pattern, notes as additional_notes
        FROM mental_state_records 
        WHERE elderly_id = ? AND DATE(recorded_datetime) = ?
        ORDER BY recorded_datetime DESC";
    $records['mental_state'] = safeTableQuery($conn, 'mental_state_records', $sql, [$elderly_id, $date], "is");

    // Count total records for the date
    $total_count = 0;
    foreach ($records as $record_type) {
        $total_count += count($record_type);
    }

    echo json_encode([
        'success' => true,
        'date' => $date,
        'elderly_id' => $elderly_id,
        'total_records' => $total_count,
        'records' => $records
    ]);

} catch (Exception $e) {
    error_log("Database error in get_care_records_by_date.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'เกิดข้อผิดพลาดในการดึงข้อมูล',
        'debug' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>