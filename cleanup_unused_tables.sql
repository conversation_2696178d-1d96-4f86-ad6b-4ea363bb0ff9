-- คำสั่ง SQL สำหรับลบตารางที่ไม่ได้ใช้งาน
-- ตารางเหล่านี้ไม่มีข้อมูลและไม่พบการใช้งานในโค้ด

-- ตารางที่เกี่ยวกับ accessories (ไม่ได้ใช้)
DROP TABLE IF EXISTS accessory_assignments;
DROP TABLE IF EXISTS accessories;

-- ตารางที่เกี่ยวกับ activities (มี activity_log ที่ใช้แทน)
DROP TABLE IF EXISTS activity_participants;
DROP TABLE IF EXISTS activities;

-- ตารางที่เกี่ยวกับ events (ไม่ได้ใช้)
DROP TABLE IF EXISTS events;

-- ตารางที่เกี่ยวกับ facility details (ไม่ได้ใช้)
DROP TABLE IF EXISTS facility_departments;
DROP TABLE IF EXISTS facility_documents;
DROP TABLE IF EXISTS facility_equipment;
DROP TABLE IF EXISTS facility_rooms;
DROP TABLE IF EXISTS facility_staff_schedules;

-- ตารางที่เกี่ยวกับ incidents (มี incident_reports แทน)
DROP TABLE IF EXISTS incidents;

-- ตารางที่เกี่ยวกับ inventory/stock (ไม่ได้ใช้)
DROP TABLE IF EXISTS stock_transactions;
DROP TABLE IF EXISTS stock;
DROP TABLE IF EXISTS inventory;

-- ตารางที่เกี่ยวกับ invoices (ไม่ได้ใช้)
DROP TABLE IF EXISTS invoice_items;
DROP TABLE IF EXISTS invoices;

-- ตารางที่เกี่ยวกับ schedules (ไม่ได้ใช้)
DROP TABLE IF EXISTS schedules;

-- ตารางที่เกี่ยวกับ security logs (มีตัวเดียวเพียงพอ)
-- เก็บ security_logs และลบ security_log
DROP TABLE IF EXISTS security_log;

-- ตารางที่ duplicate activity_logs
-- เก็บ activity_log และลบ activity_logs
DROP TABLE IF EXISTS activity_logs;

-- สรุปตารางที่ลบ: 15 ตาราง
-- เหลือตารางที่ใช้งานจริง: 23 ตาราง