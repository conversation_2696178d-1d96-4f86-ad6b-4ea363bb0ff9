<?php
/**
 * Comprehensive Vital Signs System Debugger
 * This script will identify and fix issues with the vital signs saving functionality
 */
define('AIVORA_SECURITY', true);

// Start output buffering to capture any output
ob_start();

echo "=== VITAL SIGNS SYSTEM DEBUGGER ===\n\n";

try {
    // 1. Test database connection
    echo "1. Testing Database Connection...\n";
    require_once __DIR__ . '/config/database.php';
    
    if (isset($conn) && $conn instanceof mysqli) {
        if ($conn->connect_error) {
            echo "❌ Database connection failed: " . $conn->connect_error . "\n";
            exit;
        } else {
            echo "✅ Database connection successful\n";
            echo "   Host: " . DB_HOST . "\n";
            echo "   Database: " . DB_NAME . "\n";
        }
    } else {
        echo "❌ Database connection variable not set\n";
        exit;
    }
    
    // 2. Check if elderly_vital_signs table exists
    echo "\n2. Checking Table Structure...\n";
    $table_check = $conn->query("SHOW TABLES LIKE 'elderly_vital_signs'");
    if ($table_check->num_rows == 0) {
        echo "❌ Table 'elderly_vital_signs' does not exist. Creating it now...\n";
        
        $create_table_sql_file = __DIR__ . '/config/create_vital_signs_table.sql';
        if (file_exists($create_table_sql_file)) {
            $create_table_sql = file_get_contents($create_table_sql_file);
            if ($conn->multi_query($create_table_sql)) {
                // Clear any pending results
                while ($conn->more_results() && $conn->next_result()) {
                    if ($result = $conn->store_result()) {
                        $result->free();
                    }
                }
                echo "✅ Table created successfully\n";
            } else {
                echo "❌ Failed to create table: " . $conn->error . "\n";
                exit;
            }
        } else {
            echo "❌ SQL file not found at: " . $create_table_sql_file . "\n";
            exit;
        }
    } else {
        echo "✅ Table 'elderly_vital_signs' exists\n";
    }
    
    // 3. Check table structure
    $structure = $conn->query("DESCRIBE elderly_vital_signs");
    echo "\n   Table Structure:\n";
    while ($row = $structure->fetch_assoc()) {
        echo "   - {$row['Field']}: {$row['Type']} ({$row['Null']}, {$row['Key']})\n";
    }
    
    // 4. Test session functionality
    echo "\n3. Testing Session System...\n";
    
    // Initialize session like the API does
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', '1');
        ini_set('session.cookie_secure', (isset($_SERVER['HTTPS']) ? '1' : '0'));
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.use_strict_mode', '1');
        ini_set('session.cookie_lifetime', '0');
        ini_set('session.gc_maxlifetime', '3600');
        ini_set('session.name', 'AIVORA_SESSION');
        session_start();
    }
    
    echo "   Session Status: " . session_status() . "\n";
    echo "   Session ID: " . session_id() . "\n";
    echo "   Session Data: " . print_r($_SESSION, true) . "\n";
    
    if (isset($_SESSION['user_id'])) {
        echo "✅ User session exists (ID: {$_SESSION['user_id']})\n";
    } else {
        echo "❌ No user session found\n";
    }
    
    // 5. Check users table for valid user
    echo "\n4. Testing User Data...\n";
    $user_check = $conn->query("SELECT id, username, name, role, facility_id FROM users LIMIT 1");
    if ($user_check && $user_check->num_rows > 0) {
        echo "✅ Users table has data\n";
        $sample_user = $user_check->fetch_assoc();
        echo "   Sample User ID: " . $sample_user['id'] . "\n";
        echo "   Username: " . $sample_user['username'] . "\n";
        echo "   Role: " . $sample_user['user_role'] . "\n";
    } else {
        echo "❌ No users found in database\n";
    }
    
    // 6. Check elderly table
    echo "\n5. Testing Elderly Data...\n";
    $elderly_check = $conn->query("SELECT id, first_name, last_name FROM elderly LIMIT 1");
    if ($elderly_check && $elderly_check->num_rows > 0) {
        echo "✅ Elderly table has data\n";
        $sample_elderly = $elderly_check->fetch_assoc();
        echo "   Sample Elderly ID: " . $sample_elderly['id'] . "\n";
        echo "   Name: " . $sample_elderly['first_name'] . " " . $sample_elderly['last_name'] . "\n";
    } else {
        echo "❌ No elderly records found in database\n";
    }
    
    // 7. Test direct vital signs insert
    echo "\n6. Testing Direct Vital Signs Insert...\n";
    
    // Get sample data
    $elderly_result = $conn->query("SELECT id FROM elderly LIMIT 1");
    $user_result = $conn->query("SELECT id FROM users LIMIT 1");
    
    if ($elderly_result->num_rows > 0 && $user_result->num_rows > 0) {
        $elderly_row = $elderly_result->fetch_assoc();
        $user_row = $user_result->fetch_assoc();
        
        $test_elderly_id = $elderly_row['id'];
        $test_user_id = $user_row['id'];
        
        echo "   Testing with Elderly ID: $test_elderly_id, User ID: $test_user_id\n";
        
        // Prepare test data
        $sql = "INSERT INTO elderly_vital_signs (
            elderly_id, recorded_date, recorded_time,
            temperature, blood_pressure_systolic, blood_pressure_diastolic, 
            heart_rate, respiratory_rate, oxygen_saturation, blood_sugar,
            additional_notes, recorded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $test_date = date('Y-m-d');
            $test_time = date('H:i:s');
            $temperature = 36.5;
            $systolic = 120;
            $diastolic = 80;
            $heart_rate = 72;
            $respiratory_rate = 16;
            $oxygen_saturation = 98;
            $blood_sugar = 100;
            $notes = 'Test record from debugger';
            
            $stmt->bind_param(
                'issdiiiiisi', 
                $test_elderly_id, $test_date, $test_time,
                $temperature, $systolic, $diastolic,
                $heart_rate, $respiratory_rate, $oxygen_saturation, $blood_sugar,
                $notes, $test_user_id
            );
            
            if ($stmt->execute()) {
                $record_id = $conn->insert_id;
                echo "✅ Test insert successful! Record ID: $record_id\n";
                
                // Clean up test record
                $conn->query("DELETE FROM elderly_vital_signs WHERE id = $record_id");
                echo "   Test record cleaned up\n";
            } else {
                echo "❌ Test insert failed: " . $stmt->error . "\n";
            }
        } else {
            echo "❌ Failed to prepare statement: " . $conn->error . "\n";
        }
    } else {
        echo "❌ Cannot test insert - missing elderly or user data\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();

// Output as plain text for easy reading
header('Content-Type: text/plain; charset=utf-8');
echo $output;

echo "\n=== DEBUGGING COMPLETE ===\n";
echo "If you see any ❌ symbols above, those are the issues that need to be fixed.\n";
echo "Check the browser's network tab for any 401/403/500 errors when submitting the form.\n";
?>