-- Create elderly_vital_signs table for storing patient vital signs records
CREATE TABLE IF NOT EXISTS elderly_vital_signs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    elderly_id INT NOT NULL,
    recorded_date DATE NOT NULL,
    recorded_time TIME NOT NULL,
    temperature DECIMAL(4,1) NULL COMMENT 'Temperature in Celsius',
    blood_pressure_systolic INT NULL COMMENT 'Systolic blood pressure (mmHg)',
    blood_pressure_diastolic INT NULL COMMENT 'Diastolic blood pressure (mmHg)',
    heart_rate INT NULL COMMENT 'Heart rate (bpm)',
    respiratory_rate INT NULL COMMENT 'Respiratory rate (breaths per minute)',
    oxygen_saturation INT NULL COMMENT 'Oxygen saturation (%)',
    blood_sugar INT NULL COMMENT 'Blood sugar level (mg/dL)',
    additional_notes TEXT NULL COMMENT 'Additional notes',
    recorded_by INT NOT NULL COMMENT 'User ID who recorded the data',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_elderly_date (elderly_id, recorded_date),
    INDEX idx_recorded_date (recorded_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Elderly vital signs records for our vital signs form';