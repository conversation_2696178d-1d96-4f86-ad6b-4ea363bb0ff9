<?php
// Login and test medication form
define('AIVORA_SECURITY', true);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';
require_once 'includes/auth.php';

// Auto-login for testing
$auto_login = false;
$login_message = '';

if (isset($_POST['do_login']) || isset($_GET['auto_login'])) {
    // Use default admin credentials or from POST
    $username = $_POST['username'] ?? 'admin';
    $password = $_POST['password'] ?? 'password123';
    
    $login_result = loginUser($username, $password);
    
    if ($login_result['status'] === 'success') {
        $auto_login = true;
        $login_message = '<p class="success">✓ เข้าสู่ระบบสำเร็จ</p>';
    } else {
        $login_message = '<p class="error">✗ ' . $login_result['message'] . '</p>';
    }
}

// Check if logged in
$is_logged_in = isLoggedIn();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Medication Form with Login</title>
    <meta charset='UTF-8'>
    <style>
        body { font-family: 'Sarabun', sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 4px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        button { padding: 10px 20px; background: #9C27B0; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #7B1FA2; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        .login-form { background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>💊 ทดสอบระบบบันทึกการให้ยา (พร้อม Login)</h1>
        
        <?php echo $login_message; ?>
        
        <?php if (!$is_logged_in): ?>
        <div class='test-section'>
            <h2>🔐 กรุณาเข้าสู่ระบบก่อน</h2>
            <div class="login-form">
                <form method="POST">
                    <div>
                        <label>Username: </label>
                        <input type="text" name="username" value="admin" placeholder="admin">
                    </div>
                    <div>
                        <label>Password: </label>
                        <input type="password" name="password" value="password123" placeholder="password123">
                    </div>
                    <button type="submit" name="do_login">เข้าสู่ระบบ</button>
                </form>
                <p class="info">หรือใช้ค่า default: admin / password123</p>
            </div>
        </div>
        <?php else: ?>
        
        <div class='test-section'>
            <h2>✅ สถานะการเข้าสู่ระบบ</h2>
            <p class='success'>เข้าสู่ระบบแล้ว</p>
            <pre><?php 
            echo "User ID: " . $_SESSION['user_id'] . "\n";
            echo "Username: " . $_SESSION['username'] . "\n";
            echo "Role: " . $_SESSION['user_role'] . "\n";
            echo "Facility ID: " . $_SESSION['facility_id'] . "\n";
            echo "Session Time Remaining: " . getSessionTimeRemaining() . " seconds\n";
            ?></pre>
            <form method="POST" action="logout.php" style="display: inline;">
                <button type="submit">ออกจากระบบ</button>
            </form>
        </div>
        
        <div class='test-section'>
            <h2>📊 ตรวจสอบฐานข้อมูล</h2>
            <?php
            $check = $conn->query("SHOW TABLES LIKE 'medication_records'");
            if ($check && $check->num_rows > 0) {
                echo "<p class='success'>✓ ตาราง medication_records มีอยู่</p>";
                
                $count = $conn->query("SELECT COUNT(*) as total FROM medication_records");
                if ($count) {
                    $total = $count->fetch_assoc()['total'];
                    echo "<p class='info'>จำนวนข้อมูลในตาราง: $total รายการ</p>";
                }
                
                // Show last 5 records
                $last_records = $conn->query("SELECT * FROM medication_records ORDER BY id DESC LIMIT 5");
                if ($last_records && $last_records->num_rows > 0) {
                    echo "<h3>ข้อมูลล่าสุด 5 รายการ:</h3>";
                    echo "<table border='1' cellpadding='5' style='width:100%; border-collapse: collapse;'>";
                    echo "<tr><th>ID</th><th>Elderly ID</th><th>ชื่อยา</th><th>ขนาด</th><th>วันที่</th><th>ช่วงเวลา</th><th>ผู้ให้</th></tr>";
                    while ($row = $last_records->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>{$row['id']}</td>";
                        echo "<td>{$row['elderly_id']}</td>";
                        echo "<td>{$row['medication_name']}</td>";
                        echo "<td>{$row['medication_dosage']}</td>";
                        echo "<td>{$row['medication_date']}</td>";
                        echo "<td>{$row['time_period']}</td>";
                        echo "<td>{$row['given_by']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "<p class='error'>✗ ไม่พบตาราง medication_records</p>";
            }
            ?>
        </div>
        
        <div class='test-section'>
            <h2>🧪 ทดสอบ API</h2>
            <div>
                <label>Elderly ID: </label>
                <input type="number" id="test_elderly_id" value="1" min="1">
            </div>
            <button onclick='testSaveAPI()'>ทดสอบบันทึกยา</button>
            <button onclick='testGetAPI()'>ดึงข้อมูลยาวันนี้</button>
            <div id='test-result'></div>
        </div>
        
        <div class='test-section'>
            <h2>🔗 ลิงก์ทดสอบ</h2>
            <p>เปิดหน้า elderly_detail.php และคลิกที่ไอคอน 💊 บันทึกการให้ยา</p>
            <a href='index.php?page=elderly_detail&id=1' target='_blank'>
                <button>เปิดหน้า Elderly Detail (ID=1)</button>
            </a>
            <a href='index.php?page=elderly' target='_blank'>
                <button>เปิดหน้ารายชื่อผู้สูงอายุ</button>
            </a>
        </div>
        
        <?php endif; ?>
    </div>
    
    <script>
    function testSaveAPI() {
        const elderlyId = document.getElementById('test_elderly_id').value;
        const formData = new FormData();
        formData.append('elderly_id', elderlyId);
        formData.append('medication_date', new Date().toISOString().split('T')[0]);
        formData.append('medication_name', 'ยาพาราเซตามอล');
        formData.append('medication_dosage', '500mg');
        formData.append('medication_route', 'oral');
        formData.append('medication_notes', 'ทดสอบระบบ ' + new Date().toLocaleTimeString('th-TH'));
        formData.append('given_by', '<?php echo $_SESSION['username'] ?? 'Test'; ?>');
        formData.append('time_periods[]', 'morning');
        formData.append('morning_time', '08:00');
        
        document.getElementById('test-result').innerHTML = '<p class="info">กำลังส่งข้อมูล...</p>';
        
        fetch('api/save_medication.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            const resultDiv = document.getElementById('test-result');
            if (data.success) {
                resultDiv.innerHTML = '<p class="success">✓ บันทึกข้อมูลสำเร็จ</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                // Reload page after 2 seconds to show new data
                setTimeout(() => location.reload(), 2000);
            } else {
                resultDiv.innerHTML = '<p class="error">✗ เกิดข้อผิดพลาด: ' + data.message + '</p>';
            }
        })
        .catch(error => {
            document.getElementById('test-result').innerHTML = '<p class="error">✗ Error: ' + error + '</p>';
        });
    }
    
    function testGetAPI() {
        const elderlyId = document.getElementById('test_elderly_id').value;
        const today = new Date().toISOString().split('T')[0];
        
        document.getElementById('test-result').innerHTML = '<p class="info">กำลังดึงข้อมูล...</p>';
        
        fetch('api/get_today_medications.php?elderly_id=' + elderlyId + '&date=' + today, {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            const resultDiv = document.getElementById('test-result');
            if (data.success) {
                let html = '<p class="success">✓ ดึงข้อมูลสำเร็จ</p>';
                if (data.data.medications && data.data.medications.length > 0) {
                    html += '<h4>ยาที่ให้วันนี้:</h4>';
                    html += '<ul>';
                    data.data.medications.forEach(med => {
                        html += `<li>${med.name} - ${med.dosage} - ${med.time_period_thai}</li>`;
                    });
                    html += '</ul>';
                }
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = '<p class="error">✗ เกิดข้อผิดพลาด: ' + data.message + '</p>';
            }
        })
        .catch(error => {
            document.getElementById('test-result').innerHTML = '<p class="error">✗ Error: ' + error + '</p>';
        });
    }
    </script>
</body>
</html>

<?php
if (isset($conn)) {
    $conn->close();
}
?>