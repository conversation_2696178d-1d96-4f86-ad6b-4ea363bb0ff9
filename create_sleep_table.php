<?php
// ป้องกันการเรียกใช้ไฟล์โดยตรงผ่าน URL
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

try {
    // อ่านไฟล์ SQL
    $sql = file_get_contents(__DIR__ . '/config/create_sleep_table.sql');
    
    if ($sql === false) {
        throw new Exception('ไม่สามารถอ่านไฟล์ SQL ได้');
    }
    
    // รันคำสั่ง SQL (แยกเป็น statements)
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            if ($conn->query($statement) === TRUE) {
                // Success - continue
            } else {
                throw new Exception("เกิดข้อผิดพลาด: " . $conn->error);
            }
        }
    }
    
    echo "✅ สร้างตาราง care_sleep สำเร็จ\n";
    echo "✅ สร้างตาราง care_sleep_images สำเร็จ\n";
    echo "📝 ตารางพร้อมสำหรับบันทึกการนอนหลับ\n";
    
    // ตรวจสอบโครงสร้างตาราง care_sleep
    $result = $conn->query("DESCRIBE care_sleep");
    if ($result) {
        echo "\n📊 โครงสร้างตาราง care_sleep:\n";
        echo "┌─────────────────────┬──────────────────┬──────────┐\n";
        echo "│ ฟิลด์               │ ประเภท           │ ข้อมูล   │\n";
        echo "├─────────────────────┼──────────────────┼──────────┤\n";
        
        while ($row = $result->fetch_assoc()) {
            printf("│ %-19s │ %-16s │ %-8s │\n", 
                $row['Field'], 
                substr($row['Type'], 0, 16), 
                $row['Null'] === 'YES' ? 'NULL' : 'NOT NULL'
            );
        }
        echo "└─────────────────────┴──────────────────┴──────────┘\n";
    }
    
    // ตรวจสอบ ENUM values
    $enum_check = $conn->query("SHOW COLUMNS FROM care_sleep LIKE 'sleep_quality'");
    if ($enum_check && $enum_check->num_rows > 0) {
        $enum_info = $enum_check->fetch_assoc();
        echo "\n🎯 ค่าที่อนุญาตใน sleep_quality:\n";
        echo $enum_info['Type'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    exit(1);
}
?>