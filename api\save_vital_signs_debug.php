<?php
/**
 * Debug version of vital signs saving API
 * This version has simplified authentication for debugging purposes
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

define('AIVORA_SECURITY', true);

// Set content type and CORS headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    // Include database connection
    require_once __DIR__ . '/../config/database.php';
    
    // Ensure global database connection is available
    global $conn;
    if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้: ' . (isset($conn) ? $conn->connect_error : 'Connection not established'));
    }
    
    // Simplified session check - just check if user_id exists
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        // For debugging, let's try to get any user from database
        $user_result = $conn->query("SELECT id, username, facility_id FROM users LIMIT 1");
        if ($user_result && $user_result->num_rows > 0) {
            $user_data = $user_result->fetch_assoc();
            $_SESSION['user_id'] = $user_data['id'];
            $_SESSION['username'] = $user_data['username'];
            $_SESSION['facility_id'] = $user_data['facility_id'];
            $_SESSION['user_role'] = 'admin'; // Set default role for debugging
            $_SESSION['last_activity'] = time();
        } else {
            throw new Exception('ไม่พบข้อมูลผู้ใช้ในระบบ');
        }
    }
    
    // Check POST method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('ต้องใช้ POST method');
    }
    
    // Check basic required data
    if (!isset($_POST['elderly_id']) || empty($_POST['elderly_id'])) {
        throw new Exception('ไม่พบรหัสผู้สูงอายุ');
    }
    
    $elderly_id = (int)$_POST['elderly_id'];
    if ($elderly_id <= 0) {
        throw new Exception('รหัสผู้สูงอายุไม่ถูกต้อง');
    }
    
    // Check if elderly exists
    $elderly_check = $conn->prepare("SELECT id FROM elderly WHERE id = ?");
    $elderly_check->bind_param('i', $elderly_id);
    $elderly_check->execute();
    if ($elderly_check->get_result()->num_rows === 0) {
        throw new Exception('ไม่พบข้อมูลผู้สูงอายุที่ระบุ (ID: ' . $elderly_id . ')');
    }
    
    // Get vital signs data - with proper data types for database schema
    $temperature = !empty($_POST['temperature']) ? (float)$_POST['temperature'] : null;
    $heart_rate = !empty($_POST['heart_rate']) ? (int)$_POST['heart_rate'] : null;
    $blood_pressure_systolic = !empty($_POST['blood_pressure_systolic']) ? (int)$_POST['blood_pressure_systolic'] : null;
    $blood_pressure_diastolic = !empty($_POST['blood_pressure_diastolic']) ? (int)$_POST['blood_pressure_diastolic'] : null;
    $respiratory_rate = !empty($_POST['respiratory_rate']) ? (int)$_POST['respiratory_rate'] : null;
    $oxygen_saturation = !empty($_POST['oxygen_saturation']) ? (int)$_POST['oxygen_saturation'] : null; // INT as per table schema
    $blood_sugar = !empty($_POST['blood_sugar']) ? (int)$_POST['blood_sugar'] : null; // INT as per table schema
    $notes = trim($_POST['additional_notes'] ?? '');
    
    // Check that at least one vital sign is provided
    if (!$temperature && !$heart_rate && !$blood_pressure_systolic && !$respiratory_rate && !$oxygen_saturation && !$blood_sugar) {
        throw new Exception('กรุณากรอกสัญญาณชีพอย่างน้อย 1 รายการ');
    }
    
    // Date and time
    $recorded_date = $_POST['recorded_date'] ?? date('Y-m-d');
    $recorded_time = $_POST['recorded_time'] ?? date('H:i:s');
    
    // Validate date format
    if (!DateTime::createFromFormat('Y-m-d', $recorded_date)) {
        throw new Exception('รูปแบบวันที่ไม่ถูกต้อง');
    }
    
    if (!DateTime::createFromFormat('H:i:s', $recorded_time) && !DateTime::createFromFormat('H:i', $recorded_time)) {
        // If time is in H:i format, convert to H:i:s
        if (DateTime::createFromFormat('H:i', $recorded_time)) {
            $recorded_time = $recorded_time . ':00';
        } else {
            throw new Exception('รูปแบบเวลาไม่ถูกต้อง');
        }
    }
    
    // Use session user_id
    $user_id = $_SESSION['user_id'];
    
    // Check if table exists, create if needed
    $table_check = $conn->query("SHOW TABLES LIKE 'elderly_vital_signs'");
    if ($table_check->num_rows == 0) {
        $create_table_sql = "
        CREATE TABLE elderly_vital_signs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            elderly_id INT NOT NULL,
            recorded_date DATE NOT NULL,
            recorded_time TIME NOT NULL,
            temperature DECIMAL(4,1) NULL COMMENT 'Temperature in Celsius',
            blood_pressure_systolic INT NULL COMMENT 'Systolic blood pressure (mmHg)',
            blood_pressure_diastolic INT NULL COMMENT 'Diastolic blood pressure (mmHg)',
            heart_rate INT NULL COMMENT 'Heart rate (bpm)',
            respiratory_rate INT NULL COMMENT 'Respiratory rate (breaths per minute)',
            oxygen_saturation INT NULL COMMENT 'Oxygen saturation (%)',
            blood_sugar INT NULL COMMENT 'Blood sugar level (mg/dL)',
            additional_notes TEXT NULL COMMENT 'Additional notes',
            recorded_by INT NOT NULL COMMENT 'User ID who recorded the data',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_elderly_date (elderly_id, recorded_date),
            INDEX idx_recorded_date (recorded_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Elderly vital signs records'";
        
        if (!$conn->query($create_table_sql)) {
            throw new Exception('ไม่สามารถสร้างตารางได้: ' . $conn->error);
        }
    }
    
    // Check mode - create or edit
    $form_mode = $_POST['form_mode'] ?? 'create';
    $vital_sign_id = !empty($_POST['vital_sign_id']) ? (int)$_POST['vital_sign_id'] : null;
    
    if ($form_mode === 'edit' && $vital_sign_id) {
        // Edit mode - update existing record
        $sql = "UPDATE elderly_vital_signs SET 
                temperature = ?, blood_pressure_systolic = ?, blood_pressure_diastolic = ?, 
                heart_rate = ?, respiratory_rate = ?, oxygen_saturation = ?, blood_sugar = ?,
                additional_notes = ?, recorded_date = ?, recorded_time = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND elderly_id = ?";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียม SQL สำหรับอัปเดต: ' . $conn->error);
        }
        
        $stmt->bind_param(
            'diiiiiissii', 
            $temperature, $blood_pressure_systolic, $blood_pressure_diastolic,
            $heart_rate, $respiratory_rate, $oxygen_saturation, $blood_sugar,
            $notes, $recorded_date, $recorded_time, $vital_sign_id, $elderly_id
        );
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'อัปเดตสัญญาณชีพสำเร็จ',
                    'mode' => 'edit',
                    'record_id' => $vital_sign_id
                ]);
            } else {
                throw new Exception('ไม่พบข้อมูลที่ต้องการอัปเดตหรือไม่มีการเปลี่ยนแปลง');
            }
        } else {
            throw new Exception('ไม่สามารถอัปเดตข้อมูลได้: ' . $stmt->error);
        }
    } else {
        // Create mode - insert new record
        $sql = "INSERT INTO elderly_vital_signs (
            elderly_id, recorded_date, recorded_time,
            temperature, blood_pressure_systolic, blood_pressure_diastolic, 
            heart_rate, respiratory_rate, oxygen_saturation, blood_sugar,
            additional_notes, recorded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียม SQL สำหรับสร้างใหม่: ' . $conn->error);
        }
        
        $stmt->bind_param(
            'issdiiiiiisi', 
            $elderly_id, $recorded_date, $recorded_time,
            $temperature, $blood_pressure_systolic, $blood_pressure_diastolic,
            $heart_rate, $respiratory_rate, $oxygen_saturation, $blood_sugar,
            $notes, $user_id
        );
        
        if ($stmt->execute()) {
            $record_id = $conn->insert_id;
            
            echo json_encode([
                'success' => true,
                'message' => 'บันทึกสัญญาณชีพใหม่สำเร็จ',
                'mode' => 'create',
                'record_id' => $record_id,
                'data' => [
                    'elderly_id' => $elderly_id,
                    'temperature' => $temperature,
                    'heart_rate' => $heart_rate,
                    'blood_pressure' => $blood_pressure_systolic && $blood_pressure_diastolic ? 
                        $blood_pressure_systolic . '/' . $blood_pressure_diastolic : null,
                    'recorded_at' => $recorded_date . ' ' . $recorded_time
                ]
            ]);
        } else {
            throw new Exception('ไม่สามารถบันทึกข้อมูลใหม่ได้: ' . $stmt->error);
        }
    }
    
} catch (Exception $e) {
    // Determine appropriate HTTP response code based on error type
    $error_message = $e->getMessage();
    if (strpos($error_message, 'Session หมดอายุ') !== false || strpos($error_message, 'ไม่ได้เข้าสู่ระบบ') !== false || strpos($error_message, 'ไม่พบข้อมูลผู้ใช้') !== false) {
        http_response_code(401); // Unauthorized
    } elseif (strpos($error_message, 'ไม่มีสิทธิ์') !== false) {
        http_response_code(403); // Forbidden
    } elseif (strpos($error_message, 'เชื่อมต่อฐานข้อมูล') !== false) {
        http_response_code(503); // Service Unavailable
    } elseif (strpos($error_message, 'POST method') !== false) {
        http_response_code(405); // Method Not Allowed
    } elseif (strpos($error_message, 'ไม่พบข้อมูลผู้สูงอายุ') !== false) {
        http_response_code(404); // Not Found
    } else {
        http_response_code(400); // Bad Request for validation errors
    }
    
    echo json_encode([
        'success' => false,
        'message' => $error_message,
        'debug' => [
            'error' => $error_message,
            'file' => basename(__FILE__),
            'line' => $e->getLine(),
            'post_data' => $_POST ?? [],
            'database_connected' => isset($conn) && ($conn instanceof mysqli) && !$conn->connect_error,
            'session_status' => session_status(),
            'session_id' => session_id(),
            'session_data' => $_SESSION ?? [],
            'mysql_error' => isset($conn) && ($conn instanceof mysqli) ? $conn->error : 'ไม่มีการเชื่อมต่อฐานข้อมูล',
            'server_time' => date('Y-m-d H:i:s')
        ]
    ]);
}
?>