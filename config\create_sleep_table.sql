-- สร้างตารางสำหรับบันทึกการนอนหลับ
CREATE TABLE IF NOT EXISTS care_sleep (
    id INT PRIMARY KEY AUTO_INCREMENT,
    elderly_id INT NOT NULL,
    record_date DATE NOT NULL,
    record_time TIME NULL,
    
    -- คุณภาพการนอน
    sleep_quality ENUM(
        'นอนหลับสนิทดี',
        'นอนไม่ค่อยหลับ',
        'นอนไม่หลับ',
        'หลับๆตื่นๆ',
        'อื่นๆ'
    ) NOT NULL,
    
    -- คำอธิบายเพิ่มเติม
    additional_notes TEXT NULL,
    
    -- ข้อมูลการบันทึก
    recorded_by INT NOT NULL,
    recorded_by_name VARCHAR(255) NOT NULL,
    recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- อัพเดทล่าสุด
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes
    INDEX idx_elderly_date (elderly_id, record_date),
    INDEX idx_record_date (record_date),
    INDEX idx_recorded_datetime (recorded_datetime),
    
    -- Unique constraint: หนึ่งผู้สูงอายุต่อหนึ่งวัน
    UNIQUE KEY unique_elderly_date (elderly_id, record_date)
);

-- สร้างตารางสำหรับเก็บรูปภาพการนอนหลับ
CREATE TABLE IF NOT EXISTS care_sleep_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sleep_id INT NOT NULL,
    image_path VARCHAR(500) NOT NULL,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sleep_id) REFERENCES care_sleep(id) ON DELETE CASCADE,
    INDEX idx_sleep_id (sleep_id)
);