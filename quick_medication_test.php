<?php
// quick_medication_test.php - ทดสอบระบบ medication แบบเร็ว

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>ทดสอบระบบ Medication แบบเร็ว</h1>\n";

// สร้าง session ทดสอบหากไม่มี
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['last_activity'] = time();
    echo "<p style='color: blue;'>ⓘ สร้าง session ทดสอบแล้ว</p>\n";
}

try {
    require_once __DIR__ . '/config/database.php';
    
    if (!$conn) {
        throw new Exception("ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
    }
    
    echo "<p style='color: green;'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
    
    // สร้างตาราง medication_records แบบง่าย
    echo "<h2>ขั้นตอนที่ 1: สร้างตาราง medication_records</h2>\n";
    
    if (isset($_GET['create_table']) && $_GET['create_table'] == '1') {
        // ลบตารางเก่าก่อน
        $conn->query("DROP TABLE IF EXISTS medication_records");
        
        // สร้างตารางใหม่แบบง่าย
        $create_sql = "
        CREATE TABLE medication_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            elderly_id INT NOT NULL,
            medication_date DATE NOT NULL,
            medication_name VARCHAR(255) NOT NULL,
            medication_dosage VARCHAR(100) NOT NULL,
            medication_route VARCHAR(50) NOT NULL,
            time_period VARCHAR(50) NOT NULL,
            administration_time TIME,
            notes TEXT,
            given_by VARCHAR(100),
            recorded_by INT,
            recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_elderly_id (elderly_id),
            INDEX idx_medication_date (medication_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if ($conn->query($create_sql)) {
            echo "<p style='color: green;'>✓ สร้างตาราง medication_records สำเร็จ</p>\n";
        } else {
            echo "<p style='color: red;'>✗ เกิดข้อผิดพลาด: " . $conn->error . "</p>\n";
        }
    }
    
    // ตรวจสอบตาราง
    $check_table = $conn->query("SHOW TABLES LIKE 'medication_records'");
    if ($check_table && $check_table->num_rows > 0) {
        echo "<p style='color: green;'>✓ ตาราง medication_records มีอยู่แล้ว</p>\n";
        
        // แสดงโครงสร้าง
        $structure = $conn->query("DESCRIBE medication_records");
        if ($structure) {
            echo "<details><summary>คลิกเพื่อดูโครงสร้างตาราง</summary>\n";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr><th>Field</th><th>Type</th><th>Key</th></tr>\n";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
            echo "</details>\n";
        }
        
    } else {
        echo "<p style='color: orange;'>! ตาราง medication_records ยังไม่มี</p>\n";
        echo "<p><a href='?create_table=1' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>สร้างตาราง</a></p>\n";
    }
    
    // ทดสอบการบันทึกข้อมูล
    if ($check_table && $check_table->num_rows > 0) {
        echo "<h2>ขั้นตอนที่ 2: ทดสอบการบันทึกข้อมูล</h2>\n";
        
        if (isset($_POST['test_insert'])) {
            // ทดสอบการ insert
            $test_data = [
                'elderly_id' => (int)$_POST['elderly_id'],
                'medication_date' => $_POST['medication_date'],
                'medication_name' => $_POST['medication_name'],
                'medication_dosage' => $_POST['medication_dosage'],
                'medication_route' => $_POST['medication_route'],
                'time_period' => $_POST['time_period'],
                'administration_time' => $_POST['administration_time'],
                'notes' => $_POST['notes'],
                'given_by' => $_POST['given_by'],
                'recorded_by' => $_SESSION['user_id']
            ];
            
            $insert_sql = "
            INSERT INTO medication_records 
            (elderly_id, medication_date, medication_name, medication_dosage, medication_route, 
             time_period, administration_time, notes, given_by, recorded_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $conn->prepare($insert_sql);
            $stmt->bind_param(
                'issssssssi',
                $test_data['elderly_id'],
                $test_data['medication_date'],
                $test_data['medication_name'],
                $test_data['medication_dosage'],
                $test_data['medication_route'],
                $test_data['time_period'],
                $test_data['administration_time'],
                $test_data['notes'],
                $test_data['given_by'],
                $test_data['recorded_by']
            );
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ บันทึกข้อมูลสำเร็จ! ID: " . $conn->insert_id . "</p>\n";
            } else {
                echo "<p style='color: red;'>✗ เกิดข้อผิดพลาด: " . $stmt->error . "</p>\n";
            }
        }
        
        // ฟอร์มทดสอบ
        echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 10px 0;'>\n";
        echo "<h3>ฟอร์มทดสอบการบันทึก</h3>\n";
        
        echo "<label>Elderly ID: <input type='number' name='elderly_id' value='1' required></label><br><br>\n";
        echo "<label>วันที่: <input type='date' name='medication_date' value='" . date('Y-m-d') . "' required></label><br><br>\n";
        echo "<label>ชื่อยา: <input type='text' name='medication_name' value='Paracetamol Test' required></label><br><br>\n";
        echo "<label>ขนาดยา: <input type='text' name='medication_dosage' value='500mg' required></label><br><br>\n";
        
        echo "<label>วิธีการให้: \n";
        echo "<select name='medication_route' required>\n";
        echo "<option value='oral'>รับประทาน</option>\n";
        echo "<option value='injection'>ฉีด</option>\n";
        echo "<option value='topical'>ทาผิวหนัง</option>\n";
        echo "</select></label><br><br>\n";
        
        echo "<label>ช่วงเวลา: \n";
        echo "<select name='time_period' required>\n";
        echo "<option value='morning'>เช้า</option>\n";
        echo "<option value='afternoon'>กลางวัน</option>\n";
        echo "<option value='evening'>เย็น</option>\n";
        echo "<option value='bedtime'>ก่อนนอน</option>\n";
        echo "<option value='as_needed'>ตามความต้องการ</option>\n";
        echo "</select></label><br><br>\n";
        
        echo "<label>เวลา: <input type='time' name='administration_time' value='08:00' required></label><br><br>\n";
        echo "<label>หมายเหตุ: <textarea name='notes'>ทดสอบระบบ</textarea></label><br><br>\n";
        echo "<label>ผู้ให้ยา: <input type='text' name='given_by' value='ทดสอบ' required></label><br><br>\n";
        
        echo "<button type='submit' name='test_insert' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>ทดสอบการบันทึก</button>\n";
        echo "</form>\n";
        
        // แสดงข้อมูลที่มีอยู่
        echo "<h3>ข้อมูลที่บันทึกแล้ว:</h3>\n";
        $data_result = $conn->query("SELECT * FROM medication_records ORDER BY recorded_datetime DESC LIMIT 5");
        if ($data_result && $data_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
            echo "<tr style='background: #e9ecef;'>";
            echo "<th>ID</th><th>Elderly ID</th><th>ยา</th><th>ขนาด</th><th>ช่วงเวลา</th><th>เวลา</th><th>บันทึกเมื่อ</th>";
            echo "</tr>\n";
            
            while ($row = $data_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . $row['elderly_id'] . "</td>";
                echo "<td>" . htmlspecialchars($row['medication_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['medication_dosage']) . "</td>";
                echo "<td>" . htmlspecialchars($row['time_period']) . "</td>";
                echo "<td>" . htmlspecialchars($row['administration_time']) . "</td>";
                echo "<td>" . htmlspecialchars($row['recorded_datetime']) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>ยังไม่มีข้อมูล</p>\n";
        }
    }
    
    // ทดสอบ API
    if ($check_table && $check_table->num_rows > 0) {
        echo "<h2>ขั้นตอนที่ 3: ทดสอบ API</h2>\n";
        
        if (isset($_POST['test_api'])) {
            $api_data = [
                'elderly_id' => 1,
                'medication_date' => date('Y-m-d'),
                'medication_name' => 'API Test Drug',
                'medication_dosage' => '250mg',
                'medication_route' => 'oral',
                'time_periods' => ['morning'],
                'morning_time' => '09:00',
                'medication_notes' => 'ทดสอบผ่าน API',
                'given_by' => 'API Test'
            ];
            
            $postdata = http_build_query($api_data);
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => [
                        'Content-Type: application/x-www-form-urlencoded',
                        'Cookie: ' . session_name() . '=' . session_id()
                    ],
                    'content' => $postdata
                ]
            ]);
            
            $response = file_get_contents('http://localhost:8080/aivora/api/save_medication.php', false, $context);
            
            echo "<h4>ผลการทดสอบ API:</h4>\n";
            if ($response !== false) {
                echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
                echo htmlspecialchars($response);
                echo "</pre>\n";
            } else {
                echo "<p style='color: red;'>ไม่สามารถเชื่อมต่อ API ได้</p>\n";
            }
        }
        
        echo "<form method='POST'>\n";
        echo "<button type='submit' name='test_api' style='background: #9C27B0; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>ทดสอบ API</button>\n";
        echo "</form>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<h3>เครื่องมือ:</h3>\n";
echo "<p><a href='check_table_structure.php'>ตรวจสอบโครงสร้างตาราง</a></p>\n";
echo "<p><a href='fix_medication_table.php'>แก้ไขตารางแบบละเอียด</a></p>\n";
echo "<p><a href='?page=elderly_detail&id=1'>ทดสอบหน้า Elderly Detail</a></p>\n";
echo "<p><a href='index.php'>กลับหน้าแรก</a></p>\n";

// เพิ่มลิงก์รีเซ็ต
if (isset($_GET['reset']) && $_GET['reset'] == '1') {
    $conn->query("DROP TABLE IF EXISTS medication_records");
    echo "<script>window.location.href = window.location.pathname;</script>";
}

echo "<p><a href='?reset=1' style='background: #dc3545; color: white; padding: 8px 12px; text-decoration: none; border-radius: 5px; font-size: 12px;'>รีเซ็ตตาราง</a></p>\n";
?>