<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
define('AIVORA_SECURITY', true);

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Include session helper
require_once __DIR__ . '/session_helper.php';
require_once __DIR__ . '/../config/database.php';

// Initialize session with same settings as main app
initializeAPISession();

// ตรวจสอบการเข้าสู่ระบบและสิทธิ์
checkAuthentication();
checkPermissions(['admin', 'facility_admin', 'staff']);

// Get current user info
$currentUser = getCurrentUser();

try {
    // รับพารามิเตอร์
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }

    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและผู้ใช้มีสิทธิ์เข้าถึง
    $isAdmin = $currentUser['user_role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล']);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง']);
        exit();
    }

    // ดึงข้อมูลสัญญาณชีพ - ใช้ตาราง elderly_vital_signs
    $sql = "SELECT vs.*, COALESCE(u.name, u.username, 'ระบบ') as user_name 
            FROM elderly_vital_signs vs 
            LEFT JOIN users u ON vs.recorded_by = u.id 
            WHERE vs.elderly_id = ? 
            ORDER BY vs.recorded_date DESC, vs.recorded_time DESC 
            LIMIT ?";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL']);
        exit();
    }

    $stmt->bind_param("ii", $elderly_id, $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $vital_signs = [];
    while ($row = $result->fetch_assoc()) {
        $vital_signs[] = [
            'id' => $row['id'],
            'recorded_date' => $row['recorded_date'],
            'recorded_time' => $row['recorded_time'],
            'temperature' => $row['temperature'],
            'blood_pressure_systolic' => $row['blood_pressure_systolic'],
            'blood_pressure_diastolic' => $row['blood_pressure_diastolic'],
            'heart_rate' => $row['heart_rate'],
            'respiratory_rate' => $row['respiratory_rate'],
            'oxygen_saturation' => $row['oxygen_saturation'],
            'blood_sugar' => $row['blood_sugar'],
            'additional_notes' => $row['additional_notes'],
            'recorded_by' => $row['user_name'],
            'created_at' => $row['created_at']
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => $vital_signs,
        'total' => count($vital_signs)
    ]);

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in get_vital_signs.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดระบบ']);
}

$conn->close();
?>