<?php
define('AIVORA_SECURITY', true);
session_start();

header('Content-Type: application/json');

// Basic session info
$sessionInfo = [
    'session_id' => session_id(),
    'user_id' => $_SESSION['user_id'] ?? null,
    'username' => $_SESSION['username'] ?? null,
    'user_role' => $_SESSION['user_role'] ?? null,
    'facility_id' => $_SESSION['facility_id'] ?? null,
    'last_activity' => $_SESSION['last_activity'] ?? null,
    'session_time' => time(),
    'is_logged_in' => isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])
];

// Include auth functions to do proper check
try {
    require_once __DIR__ . '/includes/functions.php';
    require_once __DIR__ . '/includes/auth.php';
    
    $sessionInfo['is_authenticated'] = isLoggedIn();
    $sessionInfo['is_expired'] = isSessionExpired();
    $sessionInfo['time_remaining'] = getSessionTimeRemaining();
    
} catch (Exception $e) {
    $sessionInfo['auth_error'] = $e->getMessage();
}

echo json_encode($sessionInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>