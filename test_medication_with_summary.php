<?php
// test_medication_with_summary.php - ทดสอบระบบบันทึกยาพร้อมสรุปรายการ

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>ทดสอบระบบบันทึกยาพร้อมสรุปรายการ</h1>\n";

// สร้าง session ทดสอบหากไม่มี
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['last_activity'] = time();
    echo "<p style='color: blue;'>ⓘ สร้าง session ทดสอบแล้ว</p>\n";
}

try {
    require_once __DIR__ . '/config/database.php';
    
    if (!$conn) {
        throw new Exception("ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
    }
    
    echo "<p style='color: green;'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
    
    // ตรวจสอบและสร้างตารางหากจำเป็น
    $check_table = $conn->query("SHOW TABLES LIKE 'medication_records'");
    if (!$check_table || $check_table->num_rows === 0) {
        echo "<h2>สร้างตาราง medication_records</h2>\n";
        
        $create_sql = "
        CREATE TABLE medication_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            elderly_id INT NOT NULL,
            medication_date DATE NOT NULL,
            medication_name VARCHAR(255) NOT NULL,
            medication_dosage VARCHAR(100) NOT NULL,
            medication_route VARCHAR(50) NOT NULL,
            time_period VARCHAR(50) NOT NULL,
            administration_time TIME,
            notes TEXT,
            given_by VARCHAR(100),
            recorded_by INT,
            recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_elderly_date (elderly_id, medication_date),
            INDEX idx_time_period (time_period)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if ($conn->query($create_sql)) {
            echo "<p style='color: green;'>✓ สร้างตาราง medication_records สำเร็จ</p>\n";
        } else {
            echo "<p style='color: red;'>✗ ไม่สามารถสร้างตารางได้: " . $conn->error . "</p>\n";
        }
    } else {
        echo "<p style='color: green;'>✓ ตาราง medication_records มีอยู่แล้ว</p>\n";
    }
    
    // เพิ่มข้อมูลตัวอย่างสำหรับทดสอบ
    echo "<h2>เพิ่มข้อมูลตัวอย่าง</h2>\n";
    
    if (isset($_GET['add_sample']) && $_GET['add_sample'] == '1') {
        $sample_data = [
            [1, date('Y-m-d'), 'Paracetamol', '500mg', 'oral', 'morning', '08:00:00', 'หลังอาหาร', 'พยาบาลสุดา'],
            [1, date('Y-m-d'), 'Metformin', '850mg', 'oral', 'evening', '19:00:00', 'ก่อนอาหาร', 'พยาบาลสุดา'],
            [1, date('Y-m-d'), 'Aspirin', '81mg', 'oral', 'morning', '08:30:00', 'หลังอาหาร', 'พยาบาลมาลี'],
        ];
        
        $insert_sql = "INSERT INTO medication_records (elderly_id, medication_date, medication_name, medication_dosage, medication_route, time_period, administration_time, notes, given_by, recorded_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insert_sql);
        
        $added = 0;
        foreach ($sample_data as $data) {
            $data[] = $_SESSION['user_id']; // recorded_by
            $stmt->bind_param('issssssssi', ...$data);
            if ($stmt->execute()) {
                $added++;
            }
        }
        
        echo "<p style='color: green;'>✓ เพิ่มข้อมูลตัวอย่าง $added รายการ</p>\n";
    }
    
    // แสดงข้อมูลที่มีอยู่
    echo "<h2>ข้อมูลการให้ยาของวันนี้</h2>\n";
    
    $today_data = $conn->query("
        SELECT * FROM medication_records 
        WHERE medication_date = CURDATE() 
        ORDER BY elderly_id, 
        CASE time_period 
            WHEN 'morning' THEN 1
            WHEN 'afternoon' THEN 2  
            WHEN 'evening' THEN 3
            WHEN 'bedtime' THEN 4
            WHEN 'as_needed' THEN 5
        END,
        administration_time
    ");
    
    if ($today_data && $today_data->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 14px;'>\n";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Elderly ID</th><th>ยา</th><th>ขนาด</th><th>ช่วงเวลา</th><th>เวลา</th><th>ผู้ให้</th><th>หมายเหตุ</th>";
        echo "</tr>\n";
        
        while ($row = $today_data->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['elderly_id'] . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['medication_name']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($row['medication_dosage']) . "</td>";
            echo "<td>" . htmlspecialchars($row['time_period']) . "</td>";
            echo "<td>" . htmlspecialchars($row['administration_time']) . "</td>";
            echo "<td>" . htmlspecialchars($row['given_by']) . "</td>";
            echo "<td>" . htmlspecialchars($row['notes']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // สรุปสถิติ
        $summary_sql = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN time_period = 'morning' THEN 1 ELSE 0 END) as morning,
            SUM(CASE WHEN time_period = 'afternoon' THEN 1 ELSE 0 END) as afternoon,
            SUM(CASE WHEN time_period = 'evening' THEN 1 ELSE 0 END) as evening,
            SUM(CASE WHEN time_period = 'bedtime' THEN 1 ELSE 0 END) as bedtime,
            SUM(CASE WHEN time_period = 'as_needed' THEN 1 ELSE 0 END) as as_needed
        FROM medication_records 
        WHERE medication_date = CURDATE()
        ";
        
        $summary_result = $conn->query($summary_sql);
        if ($summary_result) {
            $summary = $summary_result->fetch_assoc();
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
            echo "<h3>📊 สรุปการให้ยาวันนี้</h3>\n";
            echo "<div style='display: flex; gap: 20px; flex-wrap: wrap;'>\n";
            echo "<div>📋 รวม: <strong>{$summary['total']}</strong> รายการ</div>\n";
            echo "<div>🌅 เช้า: <strong>{$summary['morning']}</strong></div>\n";
            echo "<div>☀️ กลางวัน: <strong>{$summary['afternoon']}</strong></div>\n";
            echo "<div>🌆 เย็น: <strong>{$summary['evening']}</strong></div>\n";
            echo "<div>🌙 ก่อนนอน: <strong>{$summary['bedtime']}</strong></div>\n";
            echo "<div>⚡ ตามต้องการ: <strong>{$summary['as_needed']}</strong></div>\n";
            echo "</div>\n";
            echo "</div>\n";
        }
        
    } else {
        echo "<p>ยังไม่มีข้อมูลการให้ยาในวันนี้</p>\n";
        if ($check_table && $check_table->num_rows > 0) {
            echo "<p><a href='?add_sample=1' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>เพิ่มข้อมูลตัวอย่าง</a></p>\n";
        }
    }
    
    // ทดสอบ API get_today_medications
    echo "<h2>ทดสอบ API get_today_medications</h2>\n";
    
    if (isset($_POST['test_api'])) {
        $elderly_id = (int)$_POST['test_elderly_id'];
        $test_date = $_POST['test_date'];
        
        $api_url = "http://localhost:8080/aivora/api/get_today_medications.php?elderly_id=$elderly_id&date=$test_date";
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'Cookie: ' . session_name() . '=' . session_id()
            ]
        ]);
        
        $response = file_get_contents($api_url, false, $context);
        
        echo "<h4>ผลการทดสอบ API:</h4>\n";
        if ($response !== false) {
            $data = json_decode($response, true);
            
            echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>\n";
            
            if ($data && $data['success']) {
                echo "<p style='color: green;'>✓ API ทำงานสำเร็จ</p>\n";
                echo "<p><strong>ข้อความ:</strong> " . htmlspecialchars($data['message']) . "</p>\n";
                
                if (isset($data['data']['summary'])) {
                    $summary = $data['data']['summary'];
                    echo "<p><strong>สรุป:</strong> ให้ยาแล้ว {$summary['total_given']} รายการ</p>\n";
                    echo "<ul>\n";
                    echo "<li>เช้า: {$summary['morning']}</li>\n";
                    echo "<li>กลางวัน: {$summary['afternoon']}</li>\n";
                    echo "<li>เย็น: {$summary['evening']}</li>\n";
                    echo "<li>ก่อนนอน: {$summary['bedtime']}</li>\n";
                    echo "<li>ตามความต้องการ: {$summary['as_needed']}</li>\n";
                    echo "</ul>\n";
                }
                
                if (isset($data['data']['medications']) && count($data['data']['medications']) > 0) {
                    echo "<h5>รายการยา:</h5>\n";
                    echo "<ul>\n";
                    foreach ($data['data']['medications'] as $med) {
                        echo "<li>{$med['name']} {$med['dosage']} - {$med['time_period_thai']} ({$med['administration_time']})</li>\n";
                    }
                    echo "</ul>\n";
                }
                
            } else {
                echo "<p style='color: red;'>✗ API ส่งกลับข้อผิดพลาด</p>\n";
                echo "<p><strong>ข้อความ:</strong> " . htmlspecialchars($data['message'] ?? 'Unknown error') . "</p>\n";
            }
            
            echo "<details><summary>คลิกเพื่อดู Response ทั้งหมด</summary>\n";
            echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
            echo "</details>\n";
            echo "</div>\n";
            
        } else {
            echo "<p style='color: red;'>ไม่สามารถเชื่อมต่อ API ได้</p>\n";
        }
    }
    
    // ฟอร์มทดสอบ API
    echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 10px 0;'>\n";
    echo "<h4>ทดสอบ API get_today_medications</h4>\n";
    echo "<label>Elderly ID: <input type='number' name='test_elderly_id' value='1' required style='margin-left: 10px;'></label><br><br>\n";
    echo "<label>วันที่: <input type='date' name='test_date' value='" . date('Y-m-d') . "' required style='margin-left: 10px;'></label><br><br>\n";
    echo "<button type='submit' name='test_api' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>ทดสอบ API</button>\n";
    echo "</form>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<h3>ลิงก์ที่เป็นประโยชน์:</h3>\n";
echo "<p><a href='?page=elderly_detail&id=1' style='background: #9C27B0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>ทดสอบหน้า Elderly Detail</a></p>\n";
echo "<p><a href='quick_medication_test.php'>ทดสอบระบบ Medication พื้นฐาน</a></p>\n";
echo "<p><a href='check_table_structure.php'>ตรวจสอบโครงสร้างตาราง</a></p>\n";
echo "<p><a href='index.php'>กลับหน้าแรก</a></p>\n";

// รีเซ็ตข้อมูล
if (isset($_GET['reset']) && $_GET['reset'] == '1') {
    $conn->query("DELETE FROM medication_records WHERE medication_date = CURDATE()");
    echo "<script>window.location.href = window.location.pathname;</script>";
}

echo "<p><a href='?reset=1' style='background: #dc3545; color: white; padding: 8px 12px; text-decoration: none; border-radius: 5px; font-size: 12px;'>ลบข้อมูลวันนี้</a></p>\n";
?>