<?php
// save_hygiene.php - API for saving hygiene records
define('AIVORA_SECURITY', true);

// Set content type and security headers
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Include session helper
require_once __DIR__ . '/session_helper.php';
require_once __DIR__ . '/../config/database.php';

// Initialize session with same settings as main app
initializeAPISession();

// Check POST method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Check authentication and permissions
checkAuthentication();
checkPermissions(['admin', 'facility_admin', 'staff']);

// Get current user info
$currentUser = getCurrentUser();

try {
    /**
     * ROBUST DATA SANITIZATION AND VALIDATION
     * This section handles potential encoding issues and ensures ENUM compatibility
     */
    
    // Function to clean ENUM values from potential invisible characters and encoding issues
    function cleanEnumValue($value, $allowedValues, $default) {
        if (!isset($value) || empty($value)) {
            return $default;
        }
        
        // Remove BOM and other invisible characters
        $cleaned = trim($value);
        $cleaned = preg_replace('/[\x00-\x1F\x7F-\xFF]/', '', $cleaned); // Remove non-printable chars
        $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'UTF-8'); // Ensure UTF-8
        $cleaned = filter_var($cleaned, FILTER_SANITIZE_STRING, FILTER_FLAG_STRIP_LOW | FILTER_FLAG_STRIP_HIGH);
        
        // Additional cleaning for common invisible characters
        $cleaned = str_replace(['\u00A0', '\u200B', '\u200C', '\u200D', '\uFEFF'], '', $cleaned);
        $cleaned = preg_replace('/\s+/', '', $cleaned); // Remove all whitespace
        
        // Validate against allowed values (case-insensitive first, then strict)
        $lowerCleaned = strtolower($cleaned);
        foreach ($allowedValues as $allowed) {
            if (strtolower($allowed) === $lowerCleaned) {
                return $allowed; // Return the exact case-sensitive match
            }
        }
        
        // If no match found, log the issue and return default
        error_log("ENUM Validation Failed - Received: '" . $value . "' (cleaned: '" . $cleaned . "', length: " . strlen($cleaned) . ", hex: " . bin2hex($cleaned) . "), using default: '" . $default . "'");
        return $default;
    }
    
    // Get and validate form data with robust sanitization
    $elderly_id = isset($_POST['elderly_id']) ? (int)$_POST['elderly_id'] : 0;
    $record_date = isset($_POST['record_date']) ? trim($_POST['record_date']) : '';
    $diaper_count = isset($_POST['diaper_count']) ? (int)$_POST['diaper_count'] : 0;
    $diaper_pad_count = isset($_POST['diaper_pad_count']) ? (int)$_POST['diaper_pad_count'] : 0;
    
    // Define valid ENUM values (must match database exactly)
    $valid_bathing = ['self', 'assisted', 'wipe'];
    $valid_care = ['done', 'not_done'];
    
    // Clean and validate ENUM values
    $bathing = cleanEnumValue($_POST['bathing'] ?? null, $valid_bathing, 'self');
    $oral_care = cleanEnumValue($_POST['oral_care'] ?? null, $valid_care, 'done');
    $hair_wash = cleanEnumValue($_POST['hair_wash'] ?? null, $valid_care, 'done');
    
    $other_activities = isset($_POST['other_activities']) && is_array($_POST['other_activities']) 
        ? implode(',', array_map('trim', $_POST['other_activities'])) : '';
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
    $recorded_by_name = isset($_POST['recorded_by_name']) ? trim($_POST['recorded_by_name']) : '';
    
    // Validate required fields
    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }
    
    if (empty($record_date)) {
        echo json_encode(['success' => false, 'message' => 'กรุณาระบุวันที่บันทึก']);
        exit();
    }
    
    // Enhanced debug logging with hex dump
    error_log("SANITIZED VALUES - bathing: '" . $bathing . "' (" . strlen($bathing) . " chars)");
    error_log("SANITIZED VALUES - oral_care: '" . $oral_care . "' (" . strlen($oral_care) . " chars)");
    error_log("SANITIZED VALUES - hair_wash: '" . $hair_wash . "' (" . strlen($hair_wash) . " chars)");
    
    // Final validation check (should always pass after cleaning)
    if (!in_array($bathing, $valid_bathing, true)) {
        error_log("CRITICAL: Sanitized bathing value still invalid: '$bathing'");
        $bathing = 'self';
    }
    
    if (!in_array($oral_care, $valid_care, true)) {
        error_log("CRITICAL: Sanitized oral_care value still invalid: '$oral_care'");
        $oral_care = 'done';
    }
    
    if (!in_array($hair_wash, $valid_care, true)) {
        error_log("CRITICAL: Sanitized hair_wash value still invalid: '$hair_wash'");
        $hair_wash = 'done';
    }
    
    // Check if elderly exists and user has access
    $isAdmin = $currentUser['user_role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id, facility_id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล']);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง']);
        exit();
    }
    
    $elderly_data = $result_check->fetch_assoc();
    $facility_id = $elderly_data['facility_id'];

    // Check if care_hygiene table exists, create if not
    $table_check = $conn->query("SHOW TABLES LIKE 'care_hygiene'");
    if ($table_check->num_rows == 0) {
        $create_table_sql_file = __DIR__ . '/../config/create_hygiene_table.sql';
        if (file_exists($create_table_sql_file)) {
            $create_table_sql = file_get_contents($create_table_sql_file);
            if (!$conn->multi_query($create_table_sql)) {
                throw new Exception('ไม่สามารถสร้างตารางได้: ' . $conn->error);
            }
            // Clear any pending results
            while ($conn->more_results() && $conn->next_result()) {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            }
        }
    }
    
    // Handle image uploads
    $image_paths = [];
    if (isset($_FILES['hygiene_images']) && is_array($_FILES['hygiene_images']['name'])) {
        $upload_dir = __DIR__ . '/../assets/img/elderly/' . $elderly_id . '/hygiene/';
        
        // Create directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_files = 5;
        
        for ($i = 0; $i < min(count($_FILES['hygiene_images']['name']), $max_files); $i++) {
            if ($_FILES['hygiene_images']['error'][$i] === UPLOAD_ERR_OK) {
                $file_type = $_FILES['hygiene_images']['type'][$i];
                if (in_array($file_type, $allowed_types)) {
                    $file_extension = pathinfo($_FILES['hygiene_images']['name'][$i], PATHINFO_EXTENSION);
                    $file_name = 'hygiene_' . date('Y-m-d_H-i-s') . '_' . $i . '.' . $file_extension;
                    $file_path = $upload_dir . $file_name;
                    
                    if (move_uploaded_file($_FILES['hygiene_images']['tmp_name'][$i], $file_path)) {
                        $image_paths[] = 'assets/img/elderly/' . $elderly_id . '/hygiene/' . $file_name;
                    }
                }
            }
        }
    }
    
    $images_json = !empty($image_paths) ? json_encode($image_paths) : null;
    
    // Insert hygiene record
    $sql = "INSERT INTO care_hygiene (
                elderly_id, facility_id, record_date, diaper_count, diaper_pad_count,
                bathing, oral_care, hair_wash, other_activities, notes, images,
                recorded_by, recorded_by_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error]);
        exit();
    }

    // CRITICAL FIX: Correct parameter types for bind_param
    // Parameters: elderly_id(i), facility_id(i), record_date(s), diaper_count(i), diaper_pad_count(i),
    //            bathing(s), oral_care(s), hair_wash(s), other_activities(s), notes(s), images(s),
    //            recorded_by(i), recorded_by_name(s)
    $stmt->bind_param("iisiissssssis", 
        $elderly_id, $facility_id, $record_date, $diaper_count, $diaper_pad_count,
        $bathing, $oral_care, $hair_wash, $other_activities, $notes, $images_json,
        $currentUser['user_id'], $recorded_by_name
    );

    if ($stmt->execute()) {
        $hygiene_id = $conn->insert_id;
        
        echo json_encode([
            'success' => true, 
            'message' => 'บันทึกข้อมูลสุขอนามัยเรียบร้อยแล้ว',
            'hygiene_id' => $hygiene_id,
            'data' => [
                'elderly_id' => $elderly_id,
                'record_date' => $record_date,
                'diaper_count' => $diaper_count,
                'diaper_pad_count' => $diaper_pad_count,
                'bathing' => $bathing,
                'oral_care' => $oral_care,
                'hair_wash' => $hair_wash,
                'other_activities' => $other_activities,
                'notes' => $notes,
                'images' => $image_paths,
                'recorded_by' => $currentUser['user_id'],
                'recorded_by_name' => $recorded_by_name
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error]);
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in save_hygiene.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดระบบ: ' . $e->getMessage()]);
}

$conn->close();
?>