<?php
// fix_medication_table.php - แก้ไขตาราง medication_records ให้ตรงกับโครงสร้างฐานข้อมูล

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

echo "<h1>แก้ไขตาราง Medication Records</h1>\n";

try {
    require_once __DIR__ . '/config/database.php';
    
    if (!$conn) {
        throw new Exception("ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
    }
    
    echo "<p style='color: green;'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
    
    // ตรวจสอบโครงสร้างตาราง elderly ก่อน
    echo "<h2>ขั้นตอนที่ 1: ตรวจสอบตาราง elderly</h2>\n";
    
    $elderly_structure = $conn->query("DESCRIBE elderly");
    if (!$elderly_structure) {
        throw new Exception("ไม่พบตาราง elderly");
    }
    
    $elderly_columns = [];
    while ($row = $elderly_structure->fetch_assoc()) {
        $elderly_columns[] = $row['Field'];
    }
    
    echo "<p>คอลัมน์ในตาราง elderly: " . implode(', ', $elderly_columns) . "</p>\n";
    
    // ตรวจสอบว่า elderly table ใช้คอลัมน์ id หรือชื่ออื่น
    $elderly_id_column = 'id'; // ค่าเริ่มต้น
    if (in_array('id', $elderly_columns)) {
        $elderly_id_column = 'id';
        echo "<p style='color: green;'>✓ ตาราง elderly ใช้คอลัมน์ 'id' เป็น primary key</p>\n";
    } elseif (in_array('elderly_id', $elderly_columns)) {
        $elderly_id_column = 'elderly_id';
        echo "<p style='color: blue;'>ⓘ ตาราง elderly ใช้คอลัมน์ 'elderly_id' เป็น primary key</p>\n";
    } else {
        echo "<p style='color: red;'>⚠ ไม่พบคอลัมน์ primary key ที่คาดหวังในตาราง elderly</p>\n";
    }
    
    // ตรวจสอบตาราง users
    echo "<h2>ขั้นตอนที่ 2: ตรวจสอบตาราง users</h2>\n";
    
    $users_structure = $conn->query("DESCRIBE users");
    if (!$users_structure) {
        echo "<p style='color: orange;'>⚠ ไม่พบตาราง users - จะสร้างตารางโดยไม่มี foreign key ไปยัง users</p>\n";
        $has_users_table = false;
    } else {
        echo "<p style='color: green;'>✓ พบตาราง users</p>\n";
        $has_users_table = true;
    }
    
    // ลบตาราง medication_records เก่าถ้ามี (เพื่อสร้างใหม่ให้ถูกต้อง)
    echo "<h2>ขั้นตอนที่ 3: ลบและสร้างตาราง medication_records ใหม่</h2>\n";
    
    if (isset($_GET['confirm_recreate']) && $_GET['confirm_recreate'] == '1') {
        // ลบตารางเก่า
        $drop_result = $conn->query("DROP TABLE IF EXISTS medication_records");
        if ($drop_result) {
            echo "<p style='color: orange;'>⚠ ลบตาราง medication_records เก่าแล้ว</p>\n";
        }
        
        // สร้างตารางใหม่
        $create_sql = "
        CREATE TABLE medication_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            elderly_id INT NOT NULL,
            medication_date DATE NOT NULL,
            medication_name VARCHAR(255) NOT NULL,
            medication_dosage VARCHAR(100) NOT NULL,
            medication_route ENUM('oral', 'injection', 'topical', 'inhaled', 'sublingual', 'rectal', 'other') NOT NULL,
            time_period ENUM('morning', 'afternoon', 'evening', 'bedtime', 'as_needed') NOT NULL,
            administration_time TIME,
            notes TEXT,
            given_by VARCHAR(100),
            recorded_by INT,
            recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_datetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_elderly_date (elderly_id, medication_date),
            INDEX idx_medication_name (medication_name),
            INDEX idx_time_period (time_period),
            INDEX idx_recorded_datetime (recorded_datetime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if ($conn->query($create_sql)) {
            echo "<p style='color: green;'>✓ สร้างตาราง medication_records ใหม่สำเร็จ</p>\n";
            
            // เพิ่ม foreign key ถ้าเป็นไปได้
            if ($elderly_id_column == 'id') {
                $fk_elderly_sql = "
                ALTER TABLE medication_records 
                ADD CONSTRAINT fk_medication_elderly 
                FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE
                ";
                
                if ($conn->query($fk_elderly_sql)) {
                    echo "<p style='color: green;'>✓ เพิ่ม foreign key ไปยัง elderly table สำเร็จ</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ ไม่สามารถเพิ่ม foreign key ไปยัง elderly: " . $conn->error . "</p>\n";
                }
            }
            
            if ($has_users_table) {
                $fk_users_sql = "
                ALTER TABLE medication_records 
                ADD CONSTRAINT fk_medication_recorded_by 
                FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE SET NULL
                ";
                
                if ($conn->query($fk_users_sql)) {
                    echo "<p style='color: green;'>✓ เพิ่ม foreign key ไปยัง users table สำเร็จ</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ ไม่สามารถเพิ่ม foreign key ไปยัง users: " . $conn->error . "</p>\n";
                }
            }
            
            // แสดงโครงสร้างตารางใหม่
            echo "<h3>โครงสร้างตารางใหม่:</h3>\n";
            $new_structure = $conn->query("DESCRIBE medication_records");
            if ($new_structure) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
                echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
                
                while ($row = $new_structure->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td><strong>" . htmlspecialchars($row['Field']) . "</strong></td>";
                    echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                    echo "</tr>\n";
                }
                echo "</table>\n";
            }
            
            // ทดสอบการ insert ข้อมูลตัวอย่าง
            echo "<h3>ทดสอบการบันทึกข้อมูล:</h3>\n";
            
            // ดึงข้อมูล elderly คนแรก
            $elderly_test = $conn->query("SELECT id FROM elderly LIMIT 1");
            if ($elderly_test && $elderly_test->num_rows > 0) {
                $elderly_id = $elderly_test->fetch_assoc()['id'];
                
                $test_insert = "
                INSERT INTO medication_records 
                (elderly_id, medication_date, medication_name, medication_dosage, medication_route, time_period, administration_time, notes, given_by, recorded_by) 
                VALUES 
                (?, CURDATE(), 'Test Paracetamol', '500mg', 'oral', 'morning', '08:00:00', 'ทดสอบระบบ', 'System Test', 1)
                ";
                
                $stmt = $conn->prepare($test_insert);
                $stmt->bind_param('i', $elderly_id);
                
                if ($stmt->execute()) {
                    echo "<p style='color: green;'>✓ ทดสอบการบันทึกข้อมูลสำเร็จ</p>\n";
                    
                    // ลบข้อมูลทดสอบ
                    $conn->query("DELETE FROM medication_records WHERE medication_name = 'Test Paracetamol'");
                    echo "<p>ลบข้อมูลทดสอบแล้ว</p>\n";
                } else {
                    echo "<p style='color: red;'>✗ ไม่สามารถทดสอบการบันทึกข้อมูลได้: " . $stmt->error . "</p>\n";
                }
            } else {
                echo "<p style='color: orange;'>⚠ ไม่พบข้อมูล elderly สำหรับทดสอบ</p>\n";
            }
            
        } else {
            echo "<p style='color: red;'>✗ ไม่สามารถสร้างตาราง medication_records ได้: " . $conn->error . "</p>\n";
        }
        
    } else {
        // แสดงคำเตือนและขอยืนยัน
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px;'>\n";
        echo "<h3>⚠ คำเตือน</h3>\n";
        echo "<p>การดำเนินการนี้จะ:</p>\n";
        echo "<ul>\n";
        echo "<li>ลบตาราง medication_records เก่า (หากมี)</li>\n";
        echo "<li>สร้างตาราง medication_records ใหม่ที่ตรงกับโครงสร้างฐานข้อมูล</li>\n";
        echo "<li>ข้อมูลการให้ยาเก่า (หากมี) จะถูกลบทั้งหมด</li>\n";
        echo "</ul>\n";
        echo "<p><strong>คุณแน่ใจหรือไม่ที่จะดำเนินการต่อ?</strong></p>\n";
        echo "<p>\n";
        echo "<a href='?confirm_recreate=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ใช่, สร้างตารางใหม่</a>\n";
        echo "<a href='check_table_structure.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ยกเลิก</a>\n";
        echo "</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<h3>เครื่องมือเพิ่มเติม:</h3>\n";
echo "<p><a href='check_table_structure.php'>ตรวจสอบโครงสร้างตาราง</a></p>\n";
echo "<p><a href='fix_medication_session.php'>ทดสอบ Medication API</a></p>\n";
echo "<p><a href='?page=elderly_detail&id=1'>ไปหน้า Elderly Detail</a></p>\n";
echo "<p><a href='index.php'>กลับหน้าแรก</a></p>\n";
?>