<?php
// API ง่ายๆ สำหรับบันทึกสัญญาณชีพ

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

define('AIVORA_SECURITY', true);

// Set content type and CORS headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Include session helper
require_once __DIR__ . '/session_helper.php';

// Initialize session with same settings as main app
initializeAPISession();

try {
    // Include database connection after session is initialized
    require_once __DIR__ . '/../config/database.php';
    
    // Ensure global database connection is available
    global $conn;
    if (!isset($conn) || !($conn instanceof mysqli) || $conn->connect_error) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้: ' . (isset($conn) ? $conn->connect_error : 'Connection not established'));
    }
    
    // ตรวจสอบการเข้าสู่ระบบและสิทธิ์
    if (!function_exists('checkAuthentication')) {
        throw new Exception('Session helper functions not loaded properly');
    }
    
    checkAuthentication();
    checkPermissions(['admin', 'facility_admin', 'staff']);
    
    // Get current user info
    $currentUser = getCurrentUser();
    if (!$currentUser || !isset($currentUser['user_id'])) {
        throw new Exception('ไม่สามารถดึงข้อมูลผู้ใช้ได้');
    }
    
    // ตรวจสอบ POST method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('ต้องใช้ POST method');
    }
    
    // ตรวจสอบข้อมูลพื้นฐาน
    if (!isset($_POST['elderly_id']) || empty($_POST['elderly_id'])) {
        throw new Exception('ไม่พบรหัสผู้สูงอายุ');
    }
    
    $elderly_id = (int)$_POST['elderly_id'];
    if ($elderly_id <= 0) {
        throw new Exception('รหัสผู้สูงอายุไม่ถูกต้อง');
    }
    
    // ข้อมูลสัญญาณชีพ - แก้ไข data types ให้ตรงกับ database schema
    $temperature = !empty($_POST['temperature']) ? (float)$_POST['temperature'] : null;
    $heart_rate = !empty($_POST['heart_rate']) ? (int)$_POST['heart_rate'] : null;
    $blood_pressure_systolic = !empty($_POST['blood_pressure_systolic']) ? (int)$_POST['blood_pressure_systolic'] : null;
    $blood_pressure_diastolic = !empty($_POST['blood_pressure_diastolic']) ? (int)$_POST['blood_pressure_diastolic'] : null;
    $respiratory_rate = !empty($_POST['respiratory_rate']) ? (int)$_POST['respiratory_rate'] : null;
    $oxygen_saturation = !empty($_POST['oxygen_saturation']) ? (int)$_POST['oxygen_saturation'] : null; // แก้ไขเป็น int
    $blood_sugar = !empty($_POST['blood_sugar']) ? (int)$_POST['blood_sugar'] : null; // แก้ไขเป็น int
    $notes = trim($_POST['additional_notes'] ?? '');
    
    // ตรวจสอบว่ามีข้อมูลอย่างน้อย 1 รายการ
    if (!$temperature && !$heart_rate && !$blood_pressure_systolic && !$respiratory_rate && !$oxygen_saturation && !$blood_sugar) {
        throw new Exception('กรุณากรอกสัญญาณชีพอย่างน้อย 1 รายการ');
    }
    
    // วันที่และเวลา
    $recorded_date = $_POST['recorded_date'] ?? date('Y-m-d');
    $recorded_time = $_POST['recorded_time'] ?? date('H:i:s');
    
    // ตรวจสอบโหมดการทำงาน
    $form_mode = $_POST['form_mode'] ?? 'create';
    $vital_sign_id = !empty($_POST['vital_sign_id']) ? (int)$_POST['vital_sign_id'] : null;
    
    // ใช้ข้อมูล user จาก session
    $user_id = $currentUser['user_id'];
    $facility_id = $currentUser['facility_id'];
    
    // ตรวจสอบว่าตาราง elderly_vital_signs มีอยู่หรือไม่
    $table_check = $conn->query("SHOW TABLES LIKE 'elderly_vital_signs'");
    if ($table_check->num_rows == 0) {
        // สร้างตารางถ้ายังไม่มี
        $create_table_sql_file = __DIR__ . '/../config/create_vital_signs_table.sql';
        if (file_exists($create_table_sql_file)) {
            $create_table_sql = file_get_contents($create_table_sql_file);
            if (!$conn->multi_query($create_table_sql)) {
                throw new Exception('ไม่สามารถสร้างตารางได้: ' . $conn->error);
            }
            // Clear any pending results
            while ($conn->more_results() && $conn->next_result()) {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            }
        }
    }

    // ตรวจสอบโหมดการทำงาน - สร้างใหม่หรือแก้ไข
    if ($form_mode === 'edit' && $vital_sign_id) {
        // โหมดแก้ไข - อัปเดตข้อมูลที่มีอยู่
        $sql = "UPDATE elderly_vital_signs SET 
                temperature = ?, blood_pressure_systolic = ?, blood_pressure_diastolic = ?, 
                heart_rate = ?, respiratory_rate = ?, oxygen_saturation = ?, blood_sugar = ?,
                additional_notes = ?, recorded_date = ?, recorded_time = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND elderly_id = ?";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียม SQL สำหรับอัปเดต: ' . $conn->error);
        }
        
        $stmt->bind_param(
            'diiiiiissii', 
            $temperature, $blood_pressure_systolic, $blood_pressure_diastolic,
            $heart_rate, $respiratory_rate, $oxygen_saturation, $blood_sugar,
            $notes, $recorded_date, $recorded_time, $vital_sign_id, $elderly_id
        );
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'อัปเดตสัญญาณชีพสำเร็จ',
                    'mode' => 'edit',
                    'record_id' => $vital_sign_id,
                    'data' => [
                        'elderly_id' => $elderly_id,
                        'temperature' => $temperature,
                        'heart_rate' => $heart_rate,
                        'blood_pressure' => $blood_pressure_systolic && $blood_pressure_diastolic ? 
                            $blood_pressure_systolic . '/' . $blood_pressure_diastolic : null,
                        'recorded_at' => $recorded_date . ' ' . $recorded_time,
                        'user_id' => $user_id
                    ]
                ]);
            } else {
                throw new Exception('ไม่พบข้อมูลที่ต้องการอัปเดตหรือไม่มีการเปลี่ยนแปลง');
            }
        } else {
            throw new Exception('ไม่สามารถอัปเดตข้อมูลได้: ' . $stmt->error);
        }
    } else {
        // โหมดสร้างใหม่ - เพิ่มข้อมูลใหม่
        $sql = "INSERT INTO elderly_vital_signs (
            elderly_id, recorded_date, recorded_time,
            temperature, blood_pressure_systolic, blood_pressure_diastolic, 
            heart_rate, respiratory_rate, oxygen_saturation, blood_sugar,
            additional_notes, recorded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('เกิดข้อผิดพลาดในการเตรียม SQL สำหรับสร้างใหม่: ' . $conn->error);
        }
        
        $stmt->bind_param(
            'issdiiiiiisi', 
            $elderly_id, $recorded_date, $recorded_time,
            $temperature, $blood_pressure_systolic, $blood_pressure_diastolic,
            $heart_rate, $respiratory_rate, $oxygen_saturation, $blood_sugar,
            $notes, $user_id
        );
        
        if ($stmt->execute()) {
            $record_id = $conn->insert_id;
            
            echo json_encode([
                'success' => true,
                'message' => 'บันทึกสัญญาณชีพใหม่สำเร็จ',
                'mode' => 'create',
                'record_id' => $record_id,
                'data' => [
                    'elderly_id' => $elderly_id,
                    'temperature' => $temperature,
                    'heart_rate' => $heart_rate,
                    'blood_pressure' => $blood_pressure_systolic && $blood_pressure_diastolic ? 
                        $blood_pressure_systolic . '/' . $blood_pressure_diastolic : null,
                    'recorded_at' => $recorded_date . ' ' . $recorded_time,
                    'user_id' => $user_id
                ]
            ]);
        } else {
            throw new Exception('ไม่สามารถบันทึกข้อมูลใหม่ได้: ' . $stmt->error);
        }
    }
    
} catch (Exception $e) {
    // Determine appropriate HTTP response code based on error type
    $error_message = $e->getMessage();
    if (strpos($error_message, 'Session หมดอายุ') !== false || strpos($error_message, 'ไม่ได้เข้าสู่ระบบ') !== false) {
        http_response_code(401); // Unauthorized
    } elseif (strpos($error_message, 'ไม่มีสิทธิ์') !== false) {
        http_response_code(403); // Forbidden
    } elseif (strpos($error_message, 'เชื่อมต่อฐานข้อมูล') !== false) {
        http_response_code(503); // Service Unavailable
    } elseif (strpos($error_message, 'POST method') !== false) {
        http_response_code(405); // Method Not Allowed
    } else {
        http_response_code(400); // Bad Request for validation errors
    }
    
    echo json_encode([
        'success' => false,
        'message' => $error_message,
        'debug' => [
            'error' => $error_message,
            'file' => basename(__FILE__),
            'line' => $e->getLine(),
            'post_data' => $_POST ?? [],
            'database_connected' => isset($conn) && ($conn instanceof mysqli) && !$conn->connect_error,
            'session_status' => session_status(),
            'session_data' => [
                'user_id' => isset($currentUser) ? $currentUser['user_id'] : (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'ไม่มี'),
                'facility_id' => isset($currentUser) ? $currentUser['facility_id'] : (isset($_SESSION['facility_id']) ? $_SESSION['facility_id'] : 'ไม่มี'),
                'user_role' => isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'ไม่มี'
            ],
            'mysql_error' => isset($conn) && ($conn instanceof mysqli) ? $conn->error : 'ไม่มีการเชื่อมต่อฐานข้อมูล',
            'server_time' => date('Y-m-d H:i:s')
        ]
    ]);
}
?>