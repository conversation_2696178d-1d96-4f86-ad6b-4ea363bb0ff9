<?php
// ป้องกันการเรียกใช้ไฟล์โดยตรงผ่าน URL
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

try {
    // อ่านไฟล์ SQL
    $sql = file_get_contents(__DIR__ . '/config/create_mental_state_table.sql');
    
    if ($sql === false) {
        throw new Exception('ไม่สามารถอ่านไฟล์ SQL ได้');
    }
    
    // รันคำสั่ง SQL
    if ($conn->query($sql) === TRUE) {
        echo "✅ สร้างตาราง care_mental_state สำเร็จ\n";
        echo "📝 ตารางพร้อมสำหรับบันทึกสภาวะจิตใจ/อารมณ์\n";
    } else {
        throw new Exception("เกิดข้อผิดพลาด: " . $conn->error);
    }
    
    // ตรวจสอบโครงสร้างตาราง
    $result = $conn->query("DESCRIBE care_mental_state");
    if ($result) {
        echo "\n📊 โครงสร้างตาราง care_mental_state:\n";
        echo "┌─────────────────────┬──────────────────┬──────────┐\n";
        echo "│ ฟิลด์               │ ประเภท           │ ข้อมูล   │\n";
        echo "├─────────────────────┼──────────────────┼──────────┤\n";
        
        while ($row = $result->fetch_assoc()) {
            printf("│ %-19s │ %-16s │ %-8s │\n", 
                $row['Field'], 
                $row['Type'], 
                $row['Null'] === 'YES' ? 'NULL' : 'NOT NULL'
            );
        }
        echo "└─────────────────────┴──────────────────┴──────────┘\n";
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    exit(1);
}
?>