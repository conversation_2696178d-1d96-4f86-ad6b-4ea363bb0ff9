<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
define('AIVORA_SECURITY', true);

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Include required files
require_once __DIR__ . '/../config/database_simple.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// Start session using the proper session manager
SessionManager::start();

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit();
}

try {
    // Get current user
    $currentUser = getCurrentUser();
    
    // รับข้อมูลจากฟอร์ม
    $elderly_id = isset($_POST['elderly_id']) ? (int)$_POST['elderly_id'] : 0;
    $weight = !empty($_POST['weight']) ? (float)$_POST['weight'] : null;
    $height = !empty($_POST['height']) ? (float)$_POST['height'] : null;
    $measurement_method = $_POST['measurement_method'] ?? 'standing_scale';
    $measurement_date = $_POST['measurement_date'] ?? date('Y-m-d');
    $notes = trim($_POST['notes'] ?? '');
    $form_mode = $_POST['form_mode'] ?? 'create';
    $record_id = !empty($_POST['record_id']) ? (int)$_POST['record_id'] : 0;

    if ($elderly_id <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบรหัสผู้สูงอายุ'
        ]);
        exit();
    }

    if (!$weight || !$height) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณากรอกน้ำหนักและส่วนสูง'
        ]);
        exit();
    }

    // คำนวณ BMI
    $bmi = null;
    if ($weight && $height) {
        $heightM = $height / 100;
        $bmi = round($weight / ($heightM * $heightM), 2);
    }

    // ตรวจสอบสิทธิ์การเข้าถึงผู้สูงอายุ
    $isAdmin = $currentUser['role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin && !empty($currentUser['facility_id'])) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode([
            'success' => false,
            'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล'
        ]);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง'
        ]);
        exit();
    }

    // บันทึกข้อมูล
    if ($form_mode === 'edit' && $record_id > 0) {
        // โหมดแก้ไข
        $sql = "UPDATE care_weight_height SET weight = ?, height = ?, bmi = ?, measurement_method = ?, measurement_date = ?, notes = ?, recorded_at = NOW() WHERE id = ? AND elderly_id = ?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            echo json_encode([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL'
            ]);
            exit();
        }

        $stmt->bind_param('dddsssii', $weight, $height, $bmi, $measurement_method, $measurement_date, $notes, $record_id, $elderly_id);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'อัปเดตข้อมูลน้ำหนักและส่วนสูงสำเร็จ',
                    'record_id' => $record_id,
                    'calculated_bmi' => $bmi,
                    'mode' => 'updated'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'ไม่พบข้อมูลที่ต้องการแก้ไข หรือข้อมูลไม่เปลี่ยนแปลง'
                ]);
            }
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'ไม่สามารถอัปเดตข้อมูลได้: ' . $stmt->error
            ]);
        }
    } else {
        // โหมดสร้างใหม่
        $sql = "INSERT INTO care_weight_height (elderly_id, user_id, facility_id, weight, height, bmi, measurement_method, measurement_date, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            echo json_encode([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL'
            ]);
            exit();
        }

        $stmt->bind_param('iiidddsss', $elderly_id, $currentUser['id'], $currentUser['facility_id'], $weight, $height, $bmi, $measurement_method, $measurement_date, $notes);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'บันทึกน้ำหนักและส่วนสูงสำเร็จ',
                'record_id' => $conn->insert_id,
                'calculated_bmi' => $bmi,
                'mode' => 'created'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error
            ]);
        }
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in save_weight_height_simple.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดระบบ'
    ]);
}

$conn->close();
?>