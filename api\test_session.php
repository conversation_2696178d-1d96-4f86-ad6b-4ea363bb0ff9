<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
define('AIVORA_SECURITY', true);

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Include session helper
require_once __DIR__ . '/session_helper.php';

// Initialize session with same settings as main app
initializeAPISession();

// Return current session data for debugging
echo json_encode([
    'success' => true,
    'session_status' => session_status(),
    'session_id' => session_id(),
    'session_data' => [
        'user_id' => $_SESSION['user_id'] ?? null,
        'username' => $_SESSION['username'] ?? null,
        'user_name' => $_SESSION['user_name'] ?? null,
        'user_role' => $_SESSION['user_role'] ?? null,
        'facility_id' => $_SESSION['facility_id'] ?? null,
        'facility_name' => $_SESSION['facility_name'] ?? null,
        'last_activity' => $_SESSION['last_activity'] ?? null,
        'time_since_last_activity' => isset($_SESSION['last_activity']) ? (time() - $_SESSION['last_activity']) : null,
        'session_remaining' => isset($_SESSION['last_activity']) ? max(0, 3600 - (time() - $_SESSION['last_activity'])) : null
    ],
    'server_time' => date('Y-m-d H:i:s'),
    'cookies' => $_COOKIE
]);
?>