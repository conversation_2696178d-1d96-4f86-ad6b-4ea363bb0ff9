<?php
// ตรวจสอบโครงสร้างตาราง incident_reports
require_once 'config/database.php';

echo "<h3>ตรวจสอบตาราง incident_reports</h3>";

// ตรวจสอบว่ามีตารางหรือไม่
$check_table = "SHOW TABLES LIKE 'incident_reports'";
$result = $conn->query($check_table);

if ($result->num_rows > 0) {
    echo "<p>✅ พบตาราง incident_reports</p>";
    
    // แสดงโครงสร้างตาราง
    echo "<h4>โครงสร้างตารางปัจจุบัน:</h4>";
    $describe = "DESCRIBE incident_reports";
    $result_desc = $conn->query($describe);
    
    if ($result_desc->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while($row = $result_desc->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>❌ ไม่พบตาราง incident_reports</p>";
    echo "<p>จะต้องสร้างตารางใหม่</p>";
}

$conn->close();
?>