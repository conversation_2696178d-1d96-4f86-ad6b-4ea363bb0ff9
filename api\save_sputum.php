<?php
// save_sputum.php - API for saving sputum records
define('AIVORA_SECURITY', true);

// Set content type and security headers
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Include session helper
require_once __DIR__ . '/session_helper.php';
require_once __DIR__ . '/../config/database.php';

// Initialize session with same settings as main app
initializeAPISession();

// Check POST method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Check authentication and permissions
checkAuthentication();
checkPermissions(['admin', 'facility_admin', 'staff']);

// Get current user info
$currentUser = getCurrentUser();

try {
    // Function to clean ENUM values from potential invisible characters and encoding issues
    function cleanEnumValue($value, $allowedValues, $default) {
        if (!isset($value) || empty($value)) {
            return $default;
        }
        
        // Remove BOM and other invisible characters
        $cleaned = trim($value);
        $cleaned = preg_replace('/[\x00-\x1F\x7F-\xFF]/', '', $cleaned); // Remove non-printable chars
        $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'UTF-8'); // Ensure UTF-8
        $cleaned = filter_var($cleaned, FILTER_SANITIZE_STRING, FILTER_FLAG_STRIP_LOW | FILTER_FLAG_STRIP_HIGH);
        
        // Additional cleaning for common invisible characters
        $cleaned = str_replace(['\u00A0', '\u200B', '\u200C', '\u200D', '\uFEFF'], '', $cleaned);
        
        // Validate against allowed values (case-insensitive first, then strict)
        foreach ($allowedValues as $allowed) {
            if ($allowed === $cleaned) {
                return $allowed; // Return the exact case-sensitive match
            }
        }
        
        // If no match found, log the issue and return default
        error_log("ENUM Validation Failed - Received: '" . $value . "' (cleaned: '" . $cleaned . "', length: " . strlen($cleaned) . "), using default: '" . $default . "'");
        return $default;
    }
    
    // Get and validate form data with robust sanitization
    $elderly_id = isset($_POST['elderly_id']) ? (int)$_POST['elderly_id'] : 0;
    $record_date = isset($_POST['record_date']) ? trim($_POST['record_date']) : '';
    $record_time = isset($_POST['record_time']) ? trim($_POST['record_time']) : null;
    
    // Define valid ENUM values (must match database exactly)
    $valid_sputum_presence = ['มีเสมหะ', 'ไม่มีเสมหะ'];
    $valid_expulsion_methods = ['ขับเสมหะได้ด้วยตัวเอง', 'ดูดเสมหะ', 'ดูดน้ำลาย'];
    $valid_odors = ['ปกติ', 'เหม็น'];
    $valid_colors = ['สีใส', 'สีขาวออกเทา', 'สีเหลือง', 'สีเขียว', 'สีแดง', 'สีน้ำตาล', 'สีดำ'];
    $valid_choking_status = ['ไม่สำลัก', 'สำลัก'];
    
    // Clean and validate ENUM values
    $has_sputum = cleanEnumValue($_POST['has_sputum'] ?? null, $valid_sputum_presence, 'ไม่มีเสมหะ');
    $choking_status = cleanEnumValue($_POST['choking_status'] ?? null, $valid_choking_status, 'ไม่สำลัก');
    
    // Sputum details (only if has sputum)
    $expulsion_method = null;
    $odor = null;
    $color = null;
    
    if ($has_sputum === 'มีเสมหะ') {
        $expulsion_method = cleanEnumValue($_POST['expulsion_method'] ?? null, $valid_expulsion_methods, null);
        $odor = cleanEnumValue($_POST['odor'] ?? null, $valid_odors, null);
        $color = cleanEnumValue($_POST['color'] ?? null, $valid_colors, null);
        
        // Validate required fields when has sputum
        if (!$expulsion_method || !$odor || !$color) {
            echo json_encode(['success' => false, 'message' => 'กรุณากรอกรายละเอียดเสมหะให้ครบถ้วน']);
            exit();
        }
    }
    
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
    $recorded_by_name = isset($_POST['recorded_by_name']) ? trim($_POST['recorded_by_name']) : '';
    
    // Validate required fields
    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }
    
    if (empty($record_date)) {
        echo json_encode(['success' => false, 'message' => 'กรุณาระบุวันที่บันทึก']);
        exit();
    }
    
    // Enhanced debug logging
    error_log("SPUTUM SANITIZED VALUES - has_sputum: '" . $has_sputum . "'");
    error_log("SPUTUM SANITIZED VALUES - choking_status: '" . $choking_status . "'");
    error_log("SPUTUM SANITIZED VALUES - expulsion_method: '" . ($expulsion_method ?? 'NULL') . "'");
    error_log("SPUTUM SANITIZED VALUES - odor: '" . ($odor ?? 'NULL') . "'");
    error_log("SPUTUM SANITIZED VALUES - color: '" . ($color ?? 'NULL') . "'");
    
    // Check if elderly exists and user has access
    $isAdmin = $currentUser['user_role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id, facility_id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล']);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง']);
        exit();
    }
    
    $elderly_data = $result_check->fetch_assoc();
    $facility_id = $elderly_data['facility_id'];

    // Check if care_sputum table exists, create if not
    $table_check = $conn->query("SHOW TABLES LIKE 'care_sputum'");
    if ($table_check->num_rows == 0) {
        $create_table_sql_file = __DIR__ . '/../config/create_sputum_table.sql';
        if (file_exists($create_table_sql_file)) {
            $create_table_sql = file_get_contents($create_table_sql_file);
            if (!$conn->multi_query($create_table_sql)) {
                throw new Exception('ไม่สามารถสร้างตารางได้: ' . $conn->error);
            }
            // Clear any pending results
            while ($conn->more_results() && $conn->next_result()) {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            }
        }
    }
    
    // Handle image uploads
    $image_paths = [];
    if (isset($_FILES['sputum_images']) && is_array($_FILES['sputum_images']['name'])) {
        $upload_dir = __DIR__ . '/../assets/img/elderly/' . $elderly_id . '/sputum/';
        
        // Create directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_files = 5;
        
        for ($i = 0; $i < min(count($_FILES['sputum_images']['name']), $max_files); $i++) {
            if ($_FILES['sputum_images']['error'][$i] === UPLOAD_ERR_OK) {
                $file_type = $_FILES['sputum_images']['type'][$i];
                if (in_array($file_type, $allowed_types)) {
                    $file_extension = pathinfo($_FILES['sputum_images']['name'][$i], PATHINFO_EXTENSION);
                    $file_name = 'sputum_' . date('Y-m-d_H-i-s') . '_' . $i . '.' . $file_extension;
                    $file_path = $upload_dir . $file_name;
                    
                    if (move_uploaded_file($_FILES['sputum_images']['tmp_name'][$i], $file_path)) {
                        $image_paths[] = 'assets/img/elderly/' . $elderly_id . '/sputum/' . $file_name;
                    }
                }
            }
        }
    }
    
    $images_json = !empty($image_paths) ? json_encode($image_paths) : null;
    
    // Insert sputum record
    $sql = "INSERT INTO care_sputum (
                elderly_id, facility_id, record_date, record_time, has_sputum,
                expulsion_method, odor, color, choking_status, notes, images,
                recorded_by, recorded_by_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error]);
        exit();
    }

    // Parameters: elderly_id(i), facility_id(i), record_date(s), record_time(s), has_sputum(s),
    //            expulsion_method(s), odor(s), color(s), choking_status(s), notes(s), images(s),
    //            recorded_by(i), recorded_by_name(s)
    $stmt->bind_param("iisssssssssis", 
        $elderly_id, $facility_id, $record_date, $record_time, $has_sputum,
        $expulsion_method, $odor, $color, $choking_status, $notes, $images_json,
        $currentUser['user_id'], $recorded_by_name
    );

    if ($stmt->execute()) {
        $sputum_id = $conn->insert_id;
        
        echo json_encode([
            'success' => true, 
            'message' => 'บันทึกข้อมูลเสมหะเรียบร้อยแล้ว',
            'sputum_id' => $sputum_id,
            'data' => [
                'elderly_id' => $elderly_id,
                'record_date' => $record_date,
                'record_time' => $record_time,
                'has_sputum' => $has_sputum,
                'expulsion_method' => $expulsion_method,
                'odor' => $odor,
                'color' => $color,
                'choking_status' => $choking_status,
                'notes' => $notes,
                'images' => $image_paths,
                'recorded_by' => $currentUser['user_id'],
                'recorded_by_name' => $recorded_by_name
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error]);
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in save_sputum.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดระบบ: ' . $e->getMessage()]);
}

$conn->close();
?>