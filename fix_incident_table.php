<?php
// แก้ไขตาราง incident_reports ให้ตรงกับ schema
require_once 'config/database.php';

echo "<h3>แก้ไขตาราง incident_reports</h3>";

try {
    // ตรวจสอบว่ามีตารางหรือไม่
    $check_table = "SHOW TABLES LIKE 'incident_reports'";
    $result = $conn->query($check_table);
    
    if ($result->num_rows == 0) {
        echo "<p>สร้างตาราง incident_reports ใหม่...</p>";
        
        // สร้างตารางตาม schema ที่กำหนด
        $create_sql = "CREATE TABLE IF NOT EXISTS incident_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            facility_id INT NOT NULL,
            elderly_id INT NOT NULL,
            user_id INT NOT NULL,
            incident_datetime DATETIME NOT NULL,
            
            incident_type ENUM('fall', 'medication_error', 'injury', 'behavioral', 'wandering', 'choking', 'seizure', 'allergic_reaction', 'equipment_malfunction', 'visitor_related', 'staff_related', 'other') NOT NULL,
            incident_subtype VARCHAR(100),
            
            severity ENUM('minor', 'moderate', 'serious', 'critical', 'fatal') NOT NULL DEFAULT 'minor',
            injury_occurred BOOLEAN DEFAULT FALSE,
            medical_attention_required BOOLEAN DEFAULT FALSE,
            
            incident_location VARCHAR(255) NOT NULL,
            witnesses JSON COMMENT 'รายชื่อผู้พบเห็น',
            
            incident_description TEXT NOT NULL,
            contributing_factors TEXT,
            environmental_factors TEXT,
            
            immediate_action_taken TEXT NOT NULL,
            medical_intervention TEXT,
            family_notified BOOLEAN DEFAULT FALSE,
            family_notification_time DATETIME,
            doctor_notified BOOLEAN DEFAULT FALSE,
            doctor_notification_time DATETIME,
            
            follow_up_required BOOLEAN DEFAULT FALSE,
            follow_up_plan TEXT,
            prevention_measures TEXT,
            policy_review_needed BOOLEAN DEFAULT FALSE,
            
            supervisor_notified BOOLEAN DEFAULT FALSE,
            supervisor_review_completed BOOLEAN DEFAULT FALSE,
            supervisor_comments TEXT,
            reviewed_by INT,
            reviewed_at DATETIME,
            
            status ENUM('draft', 'submitted', 'under_review', 'completed', 'closed') DEFAULT 'draft',
            
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_facility_elderly_date (facility_id, elderly_id, incident_datetime),
            INDEX idx_incident_type (incident_type),
            INDEX idx_severity (severity),
            INDEX idx_status (status),
            FOREIGN KEY (elderly_id) REFERENCES elderly(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
            FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($create_sql) === TRUE) {
            echo "<p>✅ สร้างตาราง incident_reports สำเร็จ</p>";
        } else {
            echo "<p>❌ เกิดข้อผิดพลาดในการสร้างตาราง: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>พบตารางแล้ว กำลังตรวจสอบคอลัมน์...</p>";
        
        // ตรวจสอบและเพิ่มคอลัมน์ที่ขาดหาย
        $columns_to_add = [
            'facility_id' => 'INT NOT NULL AFTER id',
            'incident_subtype' => 'VARCHAR(100) AFTER incident_type',
            'injury_occurred' => 'BOOLEAN DEFAULT FALSE AFTER severity',
            'medical_attention_required' => 'BOOLEAN DEFAULT FALSE AFTER injury_occurred',
            'witnesses' => 'JSON COMMENT "รายชื่อผู้พบเห็น" AFTER incident_location',
            'contributing_factors' => 'TEXT AFTER incident_description',
            'environmental_factors' => 'TEXT AFTER contributing_factors',
            'medical_intervention' => 'TEXT AFTER immediate_action_taken',
            'family_notification_time' => 'DATETIME AFTER family_notified',
            'doctor_notification_time' => 'DATETIME AFTER doctor_notified',
            'prevention_measures' => 'TEXT AFTER follow_up_plan',
            'policy_review_needed' => 'BOOLEAN DEFAULT FALSE AFTER prevention_measures',
            'supervisor_review_completed' => 'BOOLEAN DEFAULT FALSE AFTER supervisor_notified',
            'supervisor_comments' => 'TEXT AFTER supervisor_review_completed',
            'reviewed_by' => 'INT AFTER supervisor_comments',
            'reviewed_at' => 'DATETIME AFTER reviewed_by',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at'
        ];
        
        foreach ($columns_to_add as $column => $definition) {
            // ตรวจสอบว่ามีคอลัมน์หรือไม่
            $check_column = "SHOW COLUMNS FROM incident_reports LIKE '$column'";
            $result_column = $conn->query($check_column);
            
            if ($result_column->num_rows == 0) {
                $alter_sql = "ALTER TABLE incident_reports ADD COLUMN $column $definition";
                if ($conn->query($alter_sql) === TRUE) {
                    echo "<p>✅ เพิ่มคอลัมน์ $column สำเร็จ</p>";
                } else {
                    echo "<p>❌ เกิดข้อผิดพลาดในการเพิ่มคอลัมน์ $column: " . $conn->error . "</p>";
                }
            } else {
                echo "<p>✓ คอลัมน์ $column มีอยู่แล้ว</p>";
            }
        }
        
        // อัปเดต facility_id สำหรับข้อมูลที่มีอยู่แล้ว (ถ้าเป็น NULL)
        $update_facility = "UPDATE incident_reports ir 
                           SET facility_id = (SELECT facility_id FROM elderly e WHERE e.id = ir.elderly_id) 
                           WHERE facility_id IS NULL OR facility_id = 0";
        if ($conn->query($update_facility) === TRUE) {
            echo "<p>✅ อัปเดต facility_id สำเร็จ</p>";
        }
    }
    
    echo "<p><strong>✅ การแก้ไขตารางเสร็จสิ้น</strong></p>";
    echo "<p><a href='check_incident_table.php'>ตรวจสอบโครงสร้างตารางอีกครั้ง</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
}

$conn->close();
?>