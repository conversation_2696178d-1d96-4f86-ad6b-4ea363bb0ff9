<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIVORA - Login Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        .warning { 
            background: #fff3cd; 
            color: #856404; 
            border: 1px solid #ffeaa7; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        button, .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        button:hover, .button:hover {
            background: #0056b3;
        }
        .login-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 AIVORA Authentication Test</h1>
        
        <div id="session-status">
            <h2>Current Session Status</h2>
            <div id="status-result">Loading...</div>
        </div>

        <div class="login-form">
            <h2>Test Login</h2>
            <form id="loginForm" onsubmit="testLogin(event)">
                <label>Username:</label>
                <input type="text" id="username" value="Apaporn3" required>
                
                <label>Password:</label>
                <input type="password" id="password" placeholder="Enter password" required>
                
                <button type="submit">Test Login</button>
            </form>
            <div id="login-result"></div>
        </div>

        <div>
            <h2>Quick Actions</h2>
            <button onclick="checkSession()">Check Session</button>
            <button onclick="testCareRecordAPI()">Test Care Record API</button>
            <a href="index.php" class="button">Go to Main Page</a>
            <a href="?page=elderly_detail&id=7" class="button">Test Elderly Detail</a>
            <a href="debug_care_records.php" class="button">Debug Tool</a>
        </div>

        <div id="test-results">
            <h2>Test Results</h2>
            <div id="results-content">No tests run yet.</div>
        </div>
    </div>

    <script>
        // Check session status on page load
        window.onload = function() {
            checkSession();
        };

        function checkSession() {
            document.getElementById('status-result').innerHTML = 'Checking...';
            
            fetch('api/check_session.php', {
                method: 'GET',
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                let statusClass = data.success ? 'success' : 'error';
                let statusText = data.success ? '✅ Authenticated' : '❌ Not Authenticated';
                
                document.getElementById('status-result').innerHTML = `
                    <div class="${statusClass}">
                        <strong>${statusText}</strong><br>
                        Message: ${data.message}<br>
                        ${data.user ? `User: ${data.user.username} (${data.user.role})` : 'No user data'}<br>
                        ${data.time_remaining ? `Time remaining: ${Math.floor(data.time_remaining / 60)} minutes` : ''}
                    </div>
                `;
            })
            .catch(error => {
                document.getElementById('status-result').innerHTML = `
                    <div class="error">
                        <strong>❌ Error checking session</strong><br>
                        ${error.message}
                    </div>
                `;
            });
        }

        function testLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            document.getElementById('login-result').innerHTML = '<div class="info">Logging in...</div>';
            
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            
            fetch('api/login.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                let resultClass = data.status === 'success' ? 'success' : 'error';
                let resultText = data.status === 'success' ? '✅ Login successful!' : '❌ Login failed';
                
                document.getElementById('login-result').innerHTML = `
                    <div class="${resultClass}">
                        <strong>${resultText}</strong><br>
                        ${data.message}
                    </div>
                `;
                
                if (data.status === 'success') {
                    setTimeout(() => {
                        checkSession();
                    }, 1000);
                }
            })
            .catch(error => {
                document.getElementById('login-result').innerHTML = `
                    <div class="error">
                        <strong>❌ Login error</strong><br>
                        ${error.message}
                    </div>
                `;
            });
        }

        function testCareRecordAPI() {
            document.getElementById('results-content').innerHTML = '<div class="info">Testing Care Record API...</div>';
            
            const formData = new FormData();
            formData.append('care_type', 'vital_signs');
            formData.append('elderly_id', '7');
            formData.append('temperature', '36.5');
            formData.append('heart_rate', '72');
            formData.append('notes', 'Test from login test page');
            
            fetch('api/save_care_record.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                let statusText = response.ok ? '✅ API Response OK' : '❌ API Error';
                let statusClass = response.ok ? 'success' : 'error';
                
                return response.text().then(text => {
                    try {
                        const json = JSON.parse(text);
                        document.getElementById('results-content').innerHTML = `
                            <div class="${statusClass}">
                                <strong>${statusText} (${response.status})</strong><br>
                                Success: ${json.success ? 'Yes' : 'No'}<br>
                                Message: ${json.message}<br>
                                ${json.debug ? '<details><summary>Debug Info</summary><pre>' + JSON.stringify(json.debug, null, 2) + '</pre></details>' : ''}
                            </div>
                        `;
                    } catch (e) {
                        document.getElementById('results-content').innerHTML = `
                            <div class="error">
                                <strong>❌ Invalid JSON Response (${response.status})</strong><br>
                                Raw response: <pre>${text}</pre>
                            </div>
                        `;
                    }
                });
            })
            .catch(error => {
                document.getElementById('results-content').innerHTML = `
                    <div class="error">
                        <strong>❌ Network Error</strong><br>
                        ${error.message}
                    </div>
                `;
            });
        }
    </script>
</body>
</html>