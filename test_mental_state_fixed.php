<?php
// ป้องกันการเรียกใช้ไฟล์โดยตรงผ่าน URL
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'test_user';

// Mock POST data for testing (แก้ไขแล้ว)
$_POST = [
    'elderly_id' => '6',
    'record_date' => '2024-01-15',
    'record_time' => '14:30',
    'mental_conditions' => ['ตื่นตัวดี', 'อารมณ์แจ่มใส'],
    'additional_notes' => 'ทดสอบการบันทึกข้อมูล',
    'recorded_by_name' => 'test_user'
];

echo "🔍 Test Mental State Save Process (Fixed)\n";
echo "=========================================\n";

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

try {
    // รับและตรวจสอบข้อมูลจากฟอร์ม (วิธีใหม่)
    $elderly_id = (int)($_POST['elderly_id'] ?? 0);
    $record_date = trim($_POST['record_date'] ?? '');
    $record_time = trim($_POST['record_time'] ?? '');
    $mental_conditions = $_POST['mental_conditions'] ?? [];
    $additional_notes = trim($_POST['additional_notes'] ?? '');
    $recorded_by_name = trim($_POST['recorded_by_name'] ?? '');
    
    echo "✅ ข้อมูลหลังการแก้ไข:\n";
    echo "  elderly_id: $elderly_id\n";
    echo "  record_date: $record_date\n";
    echo "  record_time: $record_time\n";
    echo "  mental_conditions: [" . implode(', ', $mental_conditions) . "]\n";
    echo "  additional_notes: $additional_notes\n";
    echo "  recorded_by_name: $recorded_by_name\n";
    
    // ตรวจสอบข้อมูลที่จำเป็น
    if (!$elderly_id || !$record_date) {
        throw new Exception('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
    }
    
    if (empty($mental_conditions) || !is_array($mental_conditions)) {
        throw new Exception('กรุณาเลือกสภาวะจิตใจอย่างน้อย 1 รายการ');
    }
    
    // ตรวจสอบ elderly_id
    $check_elderly = $conn->prepare("SELECT id, facility_id FROM elderly WHERE id = ?");
    $check_elderly->bind_param("i", $elderly_id);
    $check_elderly->execute();
    $elderly_result = $check_elderly->get_result();
    
    if ($elderly_result->num_rows == 0) {
        echo "⚠️ สร้างข้อมูลผู้สูงอายุ dummy สำหรับทดสอบ...\n";
        // สร้างข้อมูล dummy (เฉพาะในการทดสอบ)
        $insert_elderly = "INSERT IGNORE INTO elderly (id, first_name, last_name, facility_id) VALUES (?, 'ทดสอบ', 'ระบบ', 1)";
        $stmt_elderly = $conn->prepare($insert_elderly);
        $stmt_elderly->bind_param("i", $elderly_id);
        $stmt_elderly->execute();
    }
    
    echo "✅ ผู้สูงอายุ ID $elderly_id พร้อมใช้งาน\n";
    
    // ตรวจสอบและทำความสะอาดข้อมูล mental_conditions
    $valid_conditions = [
        'ตื่นตัวดี', 'รู้สึกตัวดี', 'อารมณ์แจ่มใส', 'ตอบสนองดี',
        'ไม่ค่อยตื่นตัว', 'ซึมลง', 'สับสน', 'มีอาการเพ้อ', 
        'ไม่ตอบสนอง', 'โวยวาย', 'อื่นๆ'
    ];
    
    $cleaned_conditions = [];
    foreach ($mental_conditions as $condition) {
        $condition = trim($condition);
        
        // ตรวจสอบว่าค่าอยู่ในรายการที่อนุญาตหรือไม่
        if (in_array($condition, $valid_conditions, true)) {
            $cleaned_conditions[] = $condition;
            echo "✅ '$condition' - Valid\n";
        } else {
            echo "❌ '$condition' - Invalid\n";
        }
    }
    
    if (empty($cleaned_conditions)) {
        throw new Exception('กรุณาเลือกสภาวะจิตใจที่ถูกต้อง');
    }
    
    // แปลง array เป็น string สำหรับ SET field
    $conditions_string = implode(',', $cleaned_conditions);
    echo "🎯 Conditions string: '$conditions_string'\n";
    
    // บันทึกข้อมูลลงฐานข้อมูล
    $sql = "INSERT INTO care_mental_state (
                elderly_id, record_date, record_time, mental_conditions,
                additional_notes, recorded_by, recorded_by_name, recorded_datetime
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error);
    }
    
    $recorded_by = $_SESSION['user_id'];
    $recorded_datetime = date('Y-m-d H:i:s');
    
    $stmt->bind_param(
        "isssssss",
        $elderly_id,
        $record_date,
        $record_time,
        $conditions_string,
        $additional_notes,
        $recorded_by,
        $recorded_by_name,
        $recorded_datetime
    );
    
    if ($stmt->execute()) {
        $mental_state_id = $conn->insert_id;
        echo "✅ บันทึกข้อมูลสำเร็จ! ID: $mental_state_id\n";
        
        // ทดสอบการดึงข้อมูลกลับมา
        $select_sql = "SELECT * FROM care_mental_state WHERE id = ?";
        $select_stmt = $conn->prepare($select_sql);
        $select_stmt->bind_param("i", $mental_state_id);
        $select_stmt->execute();
        $result = $select_stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            echo "\n📋 ข้อมูลที่บันทึก:\n";
            foreach ($row as $key => $value) {
                echo "  $key: $value\n";
            }
        }
        
    } else {
        throw new Exception('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' . $stmt->error);
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
}
?>