<?php
// save_symptoms.php - API สำหรับบันทึกอาการ (Fixed version)

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

header('Content-Type: application/json; charset=utf-8');

// เริ่ม session และโหลด auth system
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// โหลด auth functions และ functions.php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Only POST requests are accepted.'
    ]);
    exit;
}

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์การเข้าถึง
if (!hasPermission('elderly')) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'คุณไม่มีสิทธิ์ในการบันทึกข้อมูลอาการ'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

try {
    // รับข้อมูลจากฟอร์ม
    $elderly_id = (int)($_POST['elderly_id'] ?? 0);
    $record_date = trim($_POST['record_date'] ?? '');
    $record_time = trim($_POST['record_time'] ?? '');
    $symptoms = trim($_POST['symptoms'] ?? '');
    $severity = trim($_POST['severity'] ?? '');
    $temperature = floatval($_POST['temperature'] ?? 0);
    $notes = trim($_POST['notes'] ?? '');
    $recorded_by_name = trim($_POST['recorded_by_name'] ?? '') ?: ($_SESSION['username'] ?? 'Unknown');

    // ตรวจสอบข้อมูลที่จำเป็น
    if (!$elderly_id || !$record_date || empty($symptoms) || !$severity) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน',
            'debug' => [
                'elderly_id' => $elderly_id,
                'record_date' => $record_date,
                'symptoms' => $symptoms,
                'severity' => $severity
            ]
        ]);
        exit;
    }

    // ตรวจสอบ severity ที่อนุญาต
    $allowed_severities = ['mild', 'moderate', 'severe', 'critical'];
    if (!in_array($severity, $allowed_severities)) {
        echo json_encode([
            'success' => false,
            'message' => 'ระดับความรุนแรงไม่ถูกต้อง'
        ]);
        exit;
    }

    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและอยู่ในสถานพยาบาลเดียวกัน
    $elderly_check_sql = "SELECT id, facility_id FROM elderly WHERE id = ?";
    $elderly_check_params = [$elderly_id];
    
    if ($_SESSION['user_role'] !== 'admin') {
        $elderly_check_sql .= " AND facility_id = ?";
        $elderly_check_params[] = $_SESSION['facility_id'];
    }
    
    $elderly_check_stmt = $conn->prepare($elderly_check_sql);
    $elderly_check_stmt->bind_param(str_repeat('i', count($elderly_check_params)), ...$elderly_check_params);
    $elderly_check_stmt->execute();
    $elderly_result = $elderly_check_stmt->get_result();
    
    if ($elderly_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือคุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้'
        ]);
        exit;
    }

    // จัดการอัพโหลดรูปภาพ
    $image_paths = [];
    if (isset($_FILES['symptoms_images']) && !empty($_FILES['symptoms_images']['tmp_name'][0])) {
        $upload_dir = __DIR__ . "/../assets/img/symptoms/{$elderly_id}/";
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_files = 5;
        $max_file_size = 5 * 1024 * 1024; // 5MB
        
        for ($i = 0; $i < min(count($_FILES['symptoms_images']['tmp_name']), $max_files); $i++) {
            if ($_FILES['symptoms_images']['error'][$i] === UPLOAD_ERR_OK) {
                $file_tmp = $_FILES['symptoms_images']['tmp_name'][$i];
                $file_type = $_FILES['symptoms_images']['type'][$i];
                $file_size = $_FILES['symptoms_images']['size'][$i];
                
                // ตรวจสอบประเภทไฟล์
                if (!in_array($file_type, $allowed_types)) {
                    continue;
                }
                
                // ตรวจสอบขนาดไฟล์
                if ($file_size > $max_file_size) {
                    continue;
                }
                
                // สร้างชื่อไฟล์ใหม่
                $file_extension = pathinfo($_FILES['symptoms_images']['name'][$i], PATHINFO_EXTENSION);
                $new_filename = 'symptom_' . date('YmdHis') . '_' . $i . '.' . $file_extension;
                $file_path = $upload_dir . $new_filename;
                
                // อัพโหลดไฟล์
                if (move_uploaded_file($file_tmp, $file_path)) {
                    $image_paths[] = "assets/img/symptoms/{$elderly_id}/" . $new_filename;
                }
            }
        }
    }
    
    $image_path_string = !empty($image_paths) ? implode(',', $image_paths) : null;

    // เริ่ม transaction
    $conn->begin_transaction();

    try {
        // เตรียมคำสั่ง SQL สำหรับบันทึกข้อมูล
        $insert_sql = "
        INSERT INTO symptoms_records 
        (elderly_id, record_date, record_time, symptoms, severity, temperature, notes, image_path, recorded_by, recorded_by_name) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";
        
        $insert_stmt = $conn->prepare($insert_sql);
        
        // แปลงเวลาเป็น null ถ้าไม่มี
        $record_time = !empty($record_time) ? $record_time : null;
        
        // แปลง temperature เป็น null ถ้าเป็น 0
        $temperature = $temperature > 0 ? $temperature : null;
        
        $insert_stmt->bind_param(
            'issssdssis',
            $elderly_id,           // int -> i
            $record_date,          // string (date) -> s
            $record_time,          // string (time) -> s
            $symptoms,             // string (text) -> s
            $severity,             // string (enum) -> s
            $temperature,          // decimal -> d
            $notes,                // string (text) -> s
            $image_path_string,    // string (varchar) -> s
            $_SESSION['user_id'],  // int -> i
            $recorded_by_name      // string (varchar) -> s
        );
        
        if (!$insert_stmt->execute()) {
            throw new Exception('ไม่สามารถบันทึกข้อมูลอาการได้: ' . $insert_stmt->error);
        }
        
        $new_record_id = $conn->insert_id;
        
        // commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'บันทึกอาการเรียบร้อยแล้ว',
            'data' => [
                'id' => $new_record_id,
                'elderly_id' => $elderly_id,
                'record_date' => $record_date,
                'symptoms' => $symptoms,
                'severity' => $severity,
                'image_count' => count($image_paths)
            ]
        ]);
        
    } catch (Exception $e) {
        // rollback transaction
        $conn->rollback();
        
        // ลบไฟล์ที่อัพโหลดไปแล้วถ้าเกิดข้อผิดพลาด
        foreach ($image_paths as $path) {
            $full_path = __DIR__ . '/../' . $path;
            if (file_exists($full_path)) {
                unlink($full_path);
            }
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Error in save_symptoms.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>