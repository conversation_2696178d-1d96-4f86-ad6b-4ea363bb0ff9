<!-- Excretion Recording Modal -->
<div id="excretionModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <div>
                <h2>🚽 บันทึกการขับถ่าย</h2>
                <div id="excretionSummary" class="excretion-summary">
                    <span id="excretionSummaryText">กำลังโหลด...</span>
                </div>
            </div>
            <span class="close" onclick="closeExcretionModal()">&times;</span>
        </div>
        
        <!-- Previous Excretion Records Section -->
        <div id="previousExcretionSection" class="previous-excretion-section" style="display: none;">
            <h3>📝 การบันทึกการขับถ่ายก่อนหน้า</h3>
            <div id="previousExcretionList" class="previous-excretion-list">
                <!-- จะถูกโหลดด้วย JavaScript -->
            </div>
        </div>
        
        <form id="excretionForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" id="excretion_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="excretion_date">วันที่บันทึก:</label>
                <input type="date" id="excretion_date" name="record_date" required>
            </div>

            <!-- ปัสสาวะ Section -->
            <div class="form-section">
                <h3>💧 ปัสสาวะ</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="urine_frequency">จำนวนครั้ง:</label>
                        <input type="number" id="urine_frequency" name="urine_frequency" min="0" max="50" placeholder="กี่ครั้ง">
                    </div>
                    <div class="form-group">
                        <label for="urine_volume">ปริมาณ (ml):</label>
                        <input type="number" id="urine_volume" name="urine_volume" min="0" max="5000" placeholder="ปริมาณ ml">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="urine_method">วิธีการขับถ่าย:</label>
                    <div class="urine-method-grid">
                        <div class="method-card">
                            <input type="radio" id="urine_self" name="urine_method" value="self" checked>
                            <label for="urine_self" class="method-label">
                                <div class="method-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">🙋</span>
                                </div>
                                <div class="method-text">ปัสสาวะได้ด้วยตัวเอง</div>
                            </label>
                        </div>
                        
                        <div class="method-card">
                            <input type="radio" id="urine_catheter" name="urine_method" value="catheter">
                            <label for="urine_catheter" class="method-label">
                                <div class="method-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">🩺</span>
                                </div>
                                <div class="method-text">ใส่สายสวนปัสสาวะ</div>
                            </label>
                        </div>
                        
                        <div class="method-card">
                            <input type="radio" id="urine_other" name="urine_method" value="other">
                            <label for="urine_other" class="method-label">
                                <div class="method-icon" style="background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);">
                                    <span class="icon">🔧</span>
                                </div>
                                <div class="method-text">อื่นๆ</div>
                            </label>
                        </div>
                    </div>
                    
                    <div id="urine_other_detail" class="form-group" style="display: none; margin-top: 10px;">
                        <label for="urine_other_text">ระบุวิธีอื่นๆ:</label>
                        <input type="text" id="urine_other_text" name="urine_other_text" placeholder="ระบุวิธีการ">
                    </div>
                </div>
            </div>

            <!-- อุจจาระ Section -->
            <div class="form-section">
                <h3>💩 อุจจาระ</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="stool_frequency">จำนวนครั้ง:</label>
                        <input type="number" id="stool_frequency" name="stool_frequency" min="0" max="20" placeholder="กี่ครั้ง">
                    </div>
                    <div class="form-group">
                        <label for="stool_volume">ปริมาณ (ml):</label>
                        <input type="number" id="stool_volume" name="stool_volume" min="0" max="2000" placeholder="ปริมาณ ml">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="stool_color">สีอุจจาระ:</label>
                    <input type="text" id="stool_color" name="stool_color" placeholder="เช่น น้ำตาล เหลือง ดำ เขียว">
                </div>
                
                <div class="form-group">
                    <label for="stool_consistency">ลักษณะอุจจาระ:</label>
                    <div class="consistency-grid">
                        <div class="consistency-card">
                            <input type="radio" id="stool_normal" name="stool_consistency" value="normal" checked>
                            <label for="stool_normal" class="consistency-label">
                                <div class="consistency-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">✅</span>
                                </div>
                                <div class="consistency-text">ปกติ</div>
                            </label>
                        </div>
                        
                        <div class="consistency-card">
                            <input type="radio" id="stool_liquid" name="stool_consistency" value="liquid">
                            <label for="stool_liquid" class="consistency-label">
                                <div class="consistency-icon" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);">
                                    <span class="icon">💧</span>
                                </div>
                                <div class="consistency-text">เหลว</div>
                            </label>
                        </div>
                        
                        <div class="consistency-card">
                            <input type="radio" id="stool_semi_liquid" name="stool_consistency" value="semi_liquid">
                            <label for="stool_semi_liquid" class="consistency-label">
                                <div class="consistency-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">🌊</span>
                                </div>
                                <div class="consistency-text">กึ่งเหลว</div>
                            </label>
                        </div>
                        
                        <div class="consistency-card">
                            <input type="radio" id="stool_hard" name="stool_consistency" value="hard">
                            <label for="stool_hard" class="consistency-label">
                                <div class="consistency-icon" style="background: linear-gradient(135deg, #795548 0%, #5D4037 100%);">
                                    <span class="icon">🪨</span>
                                </div>
                                <div class="consistency-text">แข็ง</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="excretion_notes">คำอธิบายเพิ่มเติม:</label>
                <textarea id="excretion_notes" name="notes" rows="3" placeholder="รายละเอียดเพิ่มเติม สังเกต หรือข้อสำคัญอื่นๆ"></textarea>
            </div>

            <div class="form-group">
                <label for="excretion_images">รูปภาพประกอบ (ไม่บังคับ):</label>
                <input type="file" id="excretion_images" name="excretion_images[]" multiple accept="image/*" class="file-input">
                <div class="file-upload-area" onclick="document.getElementById('excretion_images').click()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">
                        <strong>คลิกเพื่อเลือกรูปภาพ</strong><br>
                        <small>รองรับ JPG, PNG, GIF (สูงสุด 5 รูป)</small>
                    </div>
                </div>
                <div id="excretionImagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label for="excretion_recorded_by">ผู้บันทึก:</label>
                <input type="text" id="excretion_recorded_by" name="recorded_by_name" value="<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>" readonly>
            </div>

            <div class="modal-actions">
                <button type="button" onclick="closeExcretionModal()" class="btn-cancel">ยกเลิก</button>
                <button type="submit" class="btn-save">บันทึกการขับถ่าย</button>
            </div>
        </form>
    </div>
</div>

<style>
.excretion-summary {
    font-size: 0.9rem;
    margin-top: 5px;
    opacity: 0.9;
}

.excretion-summary-stats {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    font-size: 0.85rem;
}

.excretion-summary-stats span {
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(255,255,255,0.7);
}

.previous-excretion-section {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.previous-excretion-section h3 {
    margin: 0 0 15px 0;
    color: #2e7d32;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.previous-excretion-list {
    display: grid;
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.excretion-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.excretion-item:hover {
    border-color: #8BC34A;
    box-shadow: 0 2px 8px rgba(139, 195, 74, 0.1);
}

.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.urine-method-grid, .consistency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.method-card, .consistency-card {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.method-card:hover, .consistency-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.method-card input[type="radio"], 
.consistency-card input[type="radio"] {
    display: none;
}

.method-card input[type="radio"]:checked + .method-label,
.consistency-card input[type="radio"]:checked + .consistency-label {
    background: rgba(139, 195, 74, 0.1);
}

.method-card input[type="radio"]:checked + .method-label .method-icon,
.consistency-card input[type="radio"]:checked + .consistency-label .consistency-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.method-label, .consistency-label {
    display: block;
    text-align: center;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.method-icon, .consistency-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 6px;
    transition: all 0.3s ease;
}

.method-icon .icon, .consistency-icon .icon {
    font-size: 1.2rem;
    filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
}

.method-text, .consistency-text {
    font-weight: 500;
    font-size: 0.85rem;
    color: var(--text-primary);
    line-height: 1.2;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .urine-method-grid, .consistency-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .method-icon, .consistency-icon {
        width: 30px;
        height: 30px;
    }
    
    .method-text, .consistency-text {
        font-size: 0.75rem;
    }
}
</style>