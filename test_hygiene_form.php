<?php
define('AIVORA_SECURITY', true);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';

// ล็อกอินอัตโนมัติสำหรับทดสอบ
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['last_activity'] = time();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>Test Hygiene Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        form { max-width: 600px; }
        label { display: block; margin: 10px 0 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #00BCD4; color: white; border: none; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .radio-group, .checkbox-group { display: flex; gap: 15px; flex-wrap: wrap; }
        .radio-group label, .checkbox-group label { margin: 0; padding: 5px; font-weight: normal; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
    </style>
</head>
<body>
    <h2>ทดสอบฟอร์มบันทึกสุขอนามัย</h2>
    
    <div class="status">
        <strong>สถานะ Session:</strong> <?= isLoggedIn() ? '✓ ล็อกอินแล้ว' : '✗ ยังไม่ได้ล็อกอิน' ?><br>
        <strong>User ID:</strong> <?= $_SESSION['user_id'] ?? 'ไม่มี' ?><br>
        <strong>Username:</strong> <?= $_SESSION['username'] ?? 'ไม่มี' ?><br>
        <strong>Role:</strong> <?= $_SESSION['user_role'] ?? 'ไม่มี' ?>
    </div>

    <form id="hygieneForm">
        <label>รหัสผู้สูงอายุ:</label>
        <input type="number" name="elderly_id" value="1" required>
        
        <label>วันที่บันทึก:</label>
        <input type="date" name="record_date" value="<?= date('Y-m-d') ?>" required>
        
        <div class="section">
            <h3>👶 เปลี่ยนผ้าอ้อม</h3>
            
            <div class="form-row">
                <div>
                    <label>จำนวนผ้าอ้อมที่ใช้ (แผ่น):</label>
                    <input type="number" name="diaper_count" min="0" max="50" value="3">
                </div>
                <div>
                    <label>จำนวนแผ่นเสริมที่ใช้ (แผ่น):</label>
                    <input type="number" name="diaper_pad_count" min="0" max="50" value="2">
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🚿 ทำความสะอาดร่างกาย</h3>
            
            <label>อาบน้ำ:</label>
            <div class="radio-group">
                <label><input type="radio" name="bathing" value="self" checked> อาบน้ำได้เอง</label>
                <label><input type="radio" name="bathing" value="assisted"> ช่วยอาบให้</label>
                <label><input type="radio" name="bathing" value="wipe"> เช็ดตัว</label>
            </div>
            
            <label>ทำความสะอาดช่องปาก:</label>
            <div class="radio-group">
                <label><input type="radio" name="oral_care" value="done" checked> ทำแล้ว</label>
                <label><input type="radio" name="oral_care" value="not_done"> ยังไม่ได้ทำ</label>
            </div>
            
            <label>สระผม:</label>
            <div class="radio-group">
                <label><input type="radio" name="hair_wash" value="done" checked> ทำแล้ว</label>
                <label><input type="radio" name="hair_wash" value="not_done"> ยังไม่ได้ทำ</label>
            </div>
            
            <label>กิจกรรมอื่นๆ (เลือกได้หลายข้อ):</label>
            <div class="checkbox-group">
                <label><input type="checkbox" name="other_activities[]" value="haircut" checked> ตัดผม</label>
                <label><input type="checkbox" name="other_activities[]" value="nail_cut" checked> ตัดเล็บ</label>
                <label><input type="checkbox" name="other_activities[]" value="ear_clean"> แคะหู</label>
                <label><input type="checkbox" name="other_activities[]" value="shave"> โกนหนวด</label>
            </div>
        </div>
        
        <label>อื่นๆ ถ้ามี:</label>
        <textarea name="notes" placeholder="รายละเอียดเพิ่มเติม หรือหมายเหตุอื่นๆ">ดูแลสุขอนามัยปกติ ทำความสะอาดครบถ้วน</textarea>
        
        <label>ผู้บันทึก:</label>
        <input type="text" name="recorded_by_name" value="<?= $_SESSION['username'] ?? '' ?>" readonly>
        
        <button type="submit">บันทึกสุขอนามัย</button>
    </form>
    
    <div id="result"></div>

    <script>
    document.getElementById('hygieneForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const resultDiv = document.getElementById('result');
        const submitBtn = this.querySelector('button[type="submit"]');
        
        submitBtn.disabled = true;
        submitBtn.textContent = 'กำลังบันทึก...';
        resultDiv.innerHTML = '<div class="status">กำลังส่งข้อมูล...</div>';
        
        fetch('api/save_hygiene.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            
            if (data.success) {
                resultDiv.innerHTML = '<div class="status success">✓ ' + data.message + '</div>';
                
                // แสดงข้อมูลที่บันทึก
                if (data.data) {
                    resultDiv.innerHTML += '<div class="status">📊 ข้อมูลที่บันทึก:<br>' +
                        'ID: ' + data.data.id + '<br>' +
                        'ผ้าอ้อม: ' + (data.data.diaper_count || 0) + ' แผ่น<br>' +
                        'อาบน้ำ: ' + data.data.bathing + '<br>' +
                        'กิจกรรมอื่นๆ: ' + data.data.other_activities.join(', ') +
                        '</div>';
                }
            } else {
                resultDiv.innerHTML = '<div class="status error">✗ ' + data.message + '</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = '<div class="status error">✗ เกิดข้อผิดพลาด: ' + error.message + '</div>';
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'บันทึกสุขอนามัย';
        });
    });
    </script>
</body>
</html>