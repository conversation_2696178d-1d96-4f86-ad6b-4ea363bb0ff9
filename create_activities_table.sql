-- Create activities_records table
USE aivora_db;

CREATE TABLE IF NOT EXISTS activities_records (
  id int(11) NOT NULL AUTO_INCREMENT,
  elderly_id int(11) NOT NULL,
  date date NOT NULL,
  activity_types text NOT NULL COMMENT 'JSON array of selected activities',
  cooperation_level text DEFAULT NULL COMMENT 'JSON array of cooperation levels',
  notes text DEFAULT NULL,
  images text DEFAULT NULL COMMENT 'JSON array of image paths',
  recorded_by int(11) DEFAULT NULL,
  recorded_datetime datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KE<PERSON> elderly_id (elderly_id),
  KEY date (date),
  CONSTRAINT activities_elderly_fk FOREIGN KEY (elderly_id) REFERENCES elderly (id) ON DELETE CASCADE,
  CONSTRAINT activities_user_fk FOREIGN KEY (recorded_by) REFERENCES users (id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;