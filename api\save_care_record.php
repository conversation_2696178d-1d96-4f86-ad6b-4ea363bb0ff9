<?php
declare(strict_types=1);
define('AIVORA_SECURITY', true);

// Include required files to ensure proper session management
require_once __DIR__ . '/../config/database_simple.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/modern_functions.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/auth.php';

// Start session using the proper session manager
SessionManager::start();

header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

try {
    // Debug session information
    $sessionDebug = [
        'session_id' => session_id(),
        'user_id' => $_SESSION['user_id'] ?? null,
        'last_activity' => $_SESSION['last_activity'] ?? null,
        'current_time' => time(),
        'isLoggedIn_result' => null
    ];
    
    // Test isLoggedIn function
    try {
        $sessionDebug['isLoggedIn_result'] = isLoggedIn();
    } catch (Exception $e) {
        $sessionDebug['isLoggedIn_error'] = $e->getMessage();
    }
    
    if (!isLoggedIn()) {
        http_response_code(401);
        echo json_encode([
            'success' => false, 
            'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน',
            'debug' => $sessionDebug,
            'solution' => 'กรุณาเข้าสู่ระบบใหม่หรือรีเฟรชหน้าเว็บ'
        ]);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed. Use POST.']);
        exit;
    }

    $currentUser = getCurrentUser();
    $facilityId = $currentUser['facility_id'];

    if (!isset($_POST['care_type']) || !isset($_POST['elderly_id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ครบถ้วน: ต้องระบุประเภทการดูแลและรหัสผู้สูงอายุ']);
        exit;
    }

    $careType = trim($_POST['care_type']);
    $elderlyId = (int)$_POST['elderly_id'];

    if (!validateElderlyAccess($elderlyId, $facilityId, $conn)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึงข้อมูลผู้สูงอายุคนนี้']);
        exit;
    }

    $result = null;
    switch ($careType) {
        case 'line_notifications': $result = saveLineNotification($conn, $_POST, $currentUser); break;
        case 'vital_signs': $result = saveVitalSigns($conn, $_POST, $currentUser); break;
        case 'position_turning': $result = savePositionTurning($conn, $_POST, $currentUser); break;
        case 'pressure_ulcers': $result = savePressureUlcers($conn, $_POST, $currentUser); break;
        case 'weight_height': $result = saveWeightHeight($conn, $_POST, $currentUser); break;
        case 'incident_reports': $result = saveIncidentReport($conn, $_POST, $currentUser); break;
        case 'medication_records': $result = saveMedicationRecord($conn, $_POST, $currentUser); break;
        case 'symptom_records': $result = saveSymptomRecord($conn, $_POST, $currentUser); break;
        case 'excretion_records': $result = saveExcretionRecord($conn, $_POST, $currentUser); break;
        case 'hygiene_records': $result = saveHygieneRecord($conn, $_POST, $currentUser); break;
        case 'feeding_records': $result = saveFeedingRecord($conn, $_POST, $currentUser); break;
        case 'activity_records': $result = saveActivityRecord($conn, $_POST, $currentUser); break;
        case 'sputum_records': $result = saveSputumRecord($conn, $_POST, $currentUser); break;
        case 'mental_state_records': $result = saveMentalStateRecord($conn, $_POST, $currentUser); break;
        case 'sleep_records': $result = saveSleepRecord($conn, $_POST, $currentUser); break;
        default: http_response_code(400); echo json_encode(['success' => false, 'message' => 'ประเภทการดูแลไม่ถูกต้อง: ' . $careType]); exit;
    }

    if ($result['success']) {
        http_response_code(201);
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }

} catch (Exception $e) {
    $errorDetails = [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
        'post_data' => $_POST,
        'session_data' => [
            'user_id' => $_SESSION['user_id'] ?? 'not set',
            'user_role' => $_SESSION['user_role'] ?? 'not set',
            'facility_id' => $_SESSION['facility_id'] ?? 'not set'
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    error_log("Care Record API Error: " . json_encode($errorDetails, JSON_UNESCAPED_UNICODE));
    
    // Also log to separate debug file
    file_put_contents(__DIR__ . '/../logs/debug_errors.log', 
        "[" . date('Y-m-d H:i:s') . "] API ERROR: " . json_encode($errorDetails, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", 
        FILE_APPEND
    );
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'เกิดข้อผิดพลาดภายในระบบ กรุณาลองใหม่อีกครั้ง',
        'debug' => $errorDetails // Include debug info for development
    ]);
}

function saveLineNotification($conn, $data, $user) {
    $sql = "INSERT INTO care_line_notifications (elderly_id, user_id, facility_id, notification_type, message, recipient_info, delivery_status, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
    
    $notificationType = $data['notification_type'] ?? 'daily_report';
    $message = trim($data['message'] ?? '');
    $recipientInfo = json_encode(['name' => $data['recipient_name'] ?? '', 'phone' => $data['recipient_phone'] ?? '']);
    $deliveryStatus = 'pending';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiisssss', $data['elderly_id'], $user['id'], $user['facility_id'], $notificationType, $message, $recipientInfo, $deliveryStatus, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกการแจ้งเตือน LINE สำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveVitalSigns($conn, $data, $user) {
    $sql = "INSERT INTO care_vital_signs (elderly_id, user_id, facility_id, temperature, blood_pressure_systolic, blood_pressure_diastolic, heart_rate, respiratory_rate, oxygen_saturation, weight, height, pain_scale, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
    
    $temperature = !empty($data['temperature']) ? (float)$data['temperature'] : null;
    $bpSystolic = !empty($data['bp_systolic']) ? (int)$data['bp_systolic'] : null;
    $bpDiastolic = !empty($data['bp_diastolic']) ? (int)$data['bp_diastolic'] : null;
    $heartRate = !empty($data['heart_rate']) ? (int)$data['heart_rate'] : null;
    $respRate = !empty($data['respiratory_rate']) ? (int)$data['respiratory_rate'] : null;
    $oxygenSat = !empty($data['oxygen_saturation']) ? (float)$data['oxygen_saturation'] : null;
    $weight = !empty($data['weight']) ? (float)$data['weight'] : null;
    $height = !empty($data['height']) ? (float)$data['height'] : null;
    $painScale = !empty($data['pain_scale']) ? (int)$data['pain_scale'] : null;
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiidiiiiiddis', $data['elderly_id'], $user['id'], $user['facility_id'], $temperature, $bpSystolic, $bpDiastolic, $heartRate, $respRate, $oxygenSat, $weight, $height, $painScale, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกสัญญาณชีพสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function savePositionTurning($conn, $data, $user) {
    $sql = "INSERT INTO care_turning_records (elderly_id, user_id, facility_id, turn_time, position_from, position_to, skin_condition, comfort_level, assistance_needed, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
    
    $turnTime = $data['turn_time'] ?? date('H:i:s');
    $positionFrom = $data['position_from'] ?? 'supine';
    $positionTo = $data['position_to'] ?? 'left_side';
    $skinCondition = $data['skin_condition'] ?? 'normal';
    $comfortLevel = $data['comfort_level'] ?? 'comfortable';
    $assistanceNeeded = $data['assistance_needed'] ?? 'minimal';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiissssssss', $data['elderly_id'], $user['id'], $user['facility_id'], $turnTime, $positionFrom, $positionTo, $skinCondition, $comfortLevel, $assistanceNeeded, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกการพลิกตัวสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function savePressureUlcers($conn, $data, $user) {
    $sql = "INSERT INTO care_pressure_ulcers (elderly_id, user_id, facility_id, ulcer_location, ulcer_stage, ulcer_size, ulcer_condition, treatment_applied, wound_care_products, pain_level, drainage, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
    
    $location = trim($data['ulcer_location'] ?? '');
    $stage = $data['ulcer_stage'] ?? 'stage_1';
    $size = trim($data['ulcer_size'] ?? '');
    $condition = $data['ulcer_condition'] ?? 'stable';
    $treatment = trim($data['treatment_applied'] ?? '');
    $products = trim($data['wound_care_products'] ?? '');
    $painLevel = !empty($data['pain_level']) ? (int)$data['pain_level'] : null;
    $drainage = $data['drainage'] ?? 'none';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiissssssisss', $data['elderly_id'], $user['id'], $user['facility_id'], $location, $stage, $size, $condition, $treatment, $products, $painLevel, $drainage, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกแผลกดทับสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveWeightHeight($conn, $data, $user) {
    // ตรวจสอบว่าเป็นโหมดแก้ไขหรือสร้างใหม่
    $formMode = $data['form_mode'] ?? 'create';
    $recordId = !empty($data['record_id']) ? (int)$data['record_id'] : 0;
    
    $weight = !empty($data['weight']) ? (float)$data['weight'] : null;
    $height = !empty($data['height']) ? (float)$data['height'] : null;
    $bmi = null;
    if ($weight && $height) {
        $heightM = $height / 100;
        $bmi = round($weight / ($heightM * $heightM), 2);
    }
    $method = $data['measurement_method'] ?? 'standing_scale';
    $measurementDate = $data['measurement_date'] ?? date('Y-m-d');
    $notes = trim($data['notes'] ?? '');

    if ($formMode === 'edit' && $recordId > 0) {
        // โหมดแก้ไข - อัปเดตข้อมูลเดิม (ลบเงื่อนไข user_id เพื่อให้ admin แก้ไขได้)
        $sql = "UPDATE care_weight_height SET weight = ?, height = ?, bmi = ?, measurement_method = ?, measurement_date = ?, notes = ?, recorded_at = NOW() WHERE id = ? AND elderly_id = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
        
        $stmt->bind_param('dddsssii', $weight, $height, $bmi, $method, $measurementDate, $notes, $recordId, $data['elderly_id']);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                return ['success' => true, 'message' => 'อัปเดตข้อมูลน้ำหนักและส่วนสูงสำเร็จ', 'record_id' => $recordId, 'calculated_bmi' => $bmi, 'mode' => 'updated'];
            } else {
                return ['success' => false, 'message' => 'ไม่พบข้อมูลที่ต้องการแก้ไข หรือข้อมูลไม่เปลี่ยนแปลง'];
            }
        } else {
            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลได้: ' . $stmt->error];
        }
    } else {
        // โหมดสร้างใหม่
        $sql = "INSERT INTO care_weight_height (elderly_id, user_id, facility_id, weight, height, bmi, measurement_method, measurement_date, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
        
        $stmt->bind_param('iiidddsss', $data['elderly_id'], $user['id'], $user['facility_id'], $weight, $height, $bmi, $method, $measurementDate, $notes);

        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'บันทึกน้ำหนักและส่วนสูงสำเร็จ', 'record_id' => $conn->insert_id, 'calculated_bmi' => $bmi, 'mode' => 'created'];
        } else {
            return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
        }
    }
}

function saveIncidentReport($conn, $data, $user) {
    $sql = "INSERT INTO care_incident_reports (elderly_id, user_id, facility_id, incident_type, severity, incident_description, location, witnesses, action_taken, injury_sustained, medical_attention, family_notified, incident_date, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
    
    $incidentType = $data['incident_type'] ?? 'other';
    $severity = $data['severity'] ?? 'minor';
    $description = trim($data['incident_description'] ?? '');
    $location = trim($data['location'] ?? '');
    $witnesses = trim($data['witnesses'] ?? '');
    $actionTaken = trim($data['action_taken'] ?? '');
    $injury = $data['injury_sustained'] ?? 'none';
    $medicalAttention = isset($data['medical_attention']) ? 1 : 0;
    $familyNotified = isset($data['family_notified']) ? 1 : 0;
    $incidentDate = $data['incident_date'] ?? date('Y-m-d H:i:s');
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiisssssssiiss', $data['elderly_id'], $user['id'], $user['facility_id'], $incidentType, $severity, $description, $location, $witnesses, $actionTaken, $injury, $medicalAttention, $familyNotified, $incidentDate, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกรายงานเหตุการณ์สำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveMedicationRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_medication_records (elderly_id, user_id, facility_id, medication_name, dosage, route, frequency, administration_time, administered_by, medication_status, reason_if_not_given, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
    
    $medicationName = trim($data['medication_name'] ?? '');
    $dosage = trim($data['dosage'] ?? '');
    $route = $data['route'] ?? 'oral';
    $frequency = trim($data['frequency'] ?? '');
    $adminTime = $data['administration_time'] ?? date('Y-m-d H:i:s');
    $adminBy = trim($data['administered_by'] ?? $user['name']);
    $status = $data['medication_status'] ?? 'given';
    $reason = trim($data['reason_if_not_given'] ?? '');
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiissssssss', $data['elderly_id'], $user['id'], $user['facility_id'], $medicationName, $dosage, $route, $frequency, $adminTime, $adminBy, $status, $reason, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกการให้ยาสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveSymptomRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_symptom_records (elderly_id, user_id, facility_id, symptom_category, symptom_description, severity, onset_time, duration_hours, assessment_findings, interventions_applied, response_to_interventions, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed'];
    
    $category = $data['symptom_category'] ?? 'other';
    $description = trim($data['symptom_description'] ?? '');
    $severity = $data['severity'] ?? 'mild';
    $onsetTime = $data['onset_time'] ?? null;
    $duration = !empty($data['duration_hours']) ? (int)$data['duration_hours'] : null;
    $findings = trim($data['assessment_findings'] ?? '');
    $interventions = trim($data['interventions_applied'] ?? '');
    $response = $data['response_to_interventions'] ?? 'no_change';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiissssisss', $data['elderly_id'], $user['id'], $user['facility_id'], $category, $description, $severity, $onsetTime, $duration, $findings, $interventions, $response, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกอาการสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveExcretionRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_excretion_records (elderly_id, user_id, facility_id, bowel_movement, bowel_consistency, bowel_color, bowel_amount, urination, urine_color, urine_amount, frequency_today, assistance_needed, continence_status, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed';
    
    $bowelMovement = isset($data['bowel_movement']) ? 1 : 0;
    $bowelConsistency = $data['bowel_consistency'] ?? 'none';
    $bowelColor = $data['bowel_color'] ?? 'brown';
    $bowelAmount = $data['bowel_amount'] ?? 'medium';
    $urination = isset($data['urination']) ? 1 : 0;
    $urineColor = $data['urine_color'] ?? 'pale_yellow';
    $urineAmount = $data['urine_amount'] ?? 'medium';
    $frequency = !empty($data['frequency_today']) ? (int)$data['frequency_today'] : 0;
    $assistance = $data['assistance_needed'] ?? 'independent';
    $continence = $data['continence_status'] ?? 'continent';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiiisssississs', $data['elderly_id'], $user['id'], $user['facility_id'], $bowelMovement, $bowelConsistency, $bowelColor, $bowelAmount, $urination, $urineColor, $urineAmount, $frequency, $assistance, $continence, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกการขับถ่ายสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveHygieneRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_hygiene_records (elderly_id, user_id, facility_id, bath_type, bath_assistance, oral_care, oral_care_type, hair_care, hair_care_type, nail_care, nail_care_type, skin_care, skin_care_products, assistance_level_overall, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed';
    
    $bathType = $data['bath_type'] ?? 'none';
    $bathAssistance = $data['bath_assistance'] ?? 'minimal';
    $oralCare = isset($data['oral_care']) ? 1 : 0;
    $oralCareType = $data['oral_care_type'] ?? 'none';
    $hairCare = isset($data['hair_care']) ? 1 : 0;
    $hairCareType = $data['hair_care_type'] ?? 'none';
    $nailCare = isset($data['nail_care']) ? 1 : 0;
    $nailCareType = $data['nail_care_type'] ?? 'none';
    $skinCare = isset($data['skin_care']) ? 1 : 0;
    $skinProducts = trim($data['skin_care_products'] ?? '');
    $assistanceOverall = $data['assistance_level_overall'] ?? 'minimal';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiisssissississs', $data['elderly_id'], $user['id'], $user['facility_id'], $bathType, $bathAssistance, $oralCare, $oralCareType, $hairCare, $hairCareType, $nailCare, $nailCareType, $skinCare, $skinProducts, $assistanceOverall, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกสุขอนามัยสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveFeedingRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_feeding_records (elderly_id, user_id, facility_id, meal_type, food_intake_percentage, fluid_intake_ml, feeding_method, appetite, difficulty_swallowing, difficulty_chewing, food_preferences, assistance_needed, meal_duration_minutes, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed';
    
    $mealType = $data['meal_type'] ?? 'other';
    $foodIntake = !empty($data['food_intake_percentage']) ? (int)$data['food_intake_percentage'] : null;
    $fluidIntake = !empty($data['fluid_intake_ml']) ? (int)$data['fluid_intake_ml'] : 0;
    $feedingMethod = $data['feeding_method'] ?? 'self_feeding';
    $appetite = $data['appetite'] ?? 'good';
    $swallowingDiff = isset($data['difficulty_swallowing']) ? 1 : 0;
    $chewingDiff = isset($data['difficulty_chewing']) ? 1 : 0;
    $preferences = trim($data['food_preferences'] ?? '');
    $assistance = $data['assistance_needed'] ?? 'none';
    $duration = !empty($data['meal_duration_minutes']) ? (int)$data['meal_duration_minutes'] : null;
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiisiissiissis', $data['elderly_id'], $user['id'], $user['facility_id'], $mealType, $foodIntake, $fluidIntake, $feedingMethod, $appetite, $swallowingDiff, $chewingDiff, $preferences, $assistance, $duration, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกการทาน/ฟีดอาหารสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveActivityRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_activity_records (elderly_id, user_id, facility_id, activity_type, activity_name, duration_minutes, participation_level, mood_during, mood_after, assistance_needed, achievement_level, social_interaction, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed';
    
    $activityType = $data['activity_type'] ?? 'other';
    $activityName = trim($data['activity_name'] ?? '');
    $duration = !empty($data['duration_minutes']) ? (int)$data['duration_minutes'] : 0;
    $participation = $data['participation_level'] ?? 'full';
    $moodDuring = $data['mood_during'] ?? 'content';
    $moodAfter = $data['mood_after'] ?? 'content';
    $assistance = $data['assistance_needed'] ?? 'minimal';
    $achievement = $data['achievement_level'] ?? 'met';
    $socialInteraction = $data['social_interaction'] ?? 'good';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiississssssss', $data['elderly_id'], $user['id'], $user['facility_id'], $activityType, $activityName, $duration, $participation, $moodDuring, $moodAfter, $assistance, $achievement, $socialInteraction, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกกิจกรรมสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveSputumRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_sputum_records (elderly_id, user_id, facility_id, sputum_amount, sputum_color, sputum_consistency, cough_frequency, cough_type, breathing_difficulty, oxygen_needed, oxygen_level_percent, chest_sounds, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed';
    
    $sputumAmount = $data['sputum_amount'] ?? 'none';
    $sputumColor = $data['sputum_color'] ?? 'clear';
    $sputumConsistency = $data['sputum_consistency'] ?? 'thin';
    $coughFreq = $data['cough_frequency'] ?? 'none';
    $coughType = $data['cough_type'] ?? 'dry';
    $breathingDiff = $data['breathing_difficulty'] ?? 'none';
    $oxygenNeeded = isset($data['oxygen_needed']) ? 1 : 0;
    $oxygenLevel = !empty($data['oxygen_level_percent']) ? (float)$data['oxygen_level_percent'] : null;
    $chestSounds = $data['chest_sounds'] ?? 'clear';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiisssssdidss', $data['elderly_id'], $user['id'], $user['facility_id'], $sputumAmount, $sputumColor, $sputumConsistency, $coughFreq, $coughType, $breathingDiff, $oxygenNeeded, $oxygenLevel, $chestSounds, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกคุณสมบัติของเสมหะสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveMentalStateRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_mental_state_records (elderly_id, user_id, facility_id, mood, anxiety_level, cognitive_function, orientation_person, orientation_place, orientation_time, social_interaction, sleep_quality, behavioral_changes, communication_ability, memory_function, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed';
    
    $mood = $data['mood'] ?? 'neutral';
    $anxietyLevel = $data['anxiety_level'] ?? 'none';
    $cognitiveFunction = $data['cognitive_function'] ?? 'alert';
    $orientPerson = isset($data['orientation_person']) ? 1 : 1;
    $orientPlace = isset($data['orientation_place']) ? 1 : 1;
    $orientTime = isset($data['orientation_time']) ? 1 : 1;
    $socialInteraction = $data['social_interaction'] ?? 'good';
    $sleepQuality = $data['sleep_quality'] ?? 'good';
    $behavioralChanges = trim($data['behavioral_changes'] ?? '');
    $communication = $data['communication_ability'] ?? 'good';
    $memory = $data['memory_function'] ?? 'good';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiisssiiisissss', $data['elderly_id'], $user['id'], $user['facility_id'], $mood, $anxietyLevel, $cognitiveFunction, $orientPerson, $orientPlace, $orientTime, $socialInteraction, $sleepQuality, $behavioralChanges, $communication, $memory, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกสภาวะจิตใจ/อารมณ์สำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function saveSleepRecord($conn, $data, $user) {
    $sql = "INSERT INTO care_sleep_records (elderly_id, user_id, facility_id, sleep_date, sleep_time, wake_time, sleep_duration_hours, sleep_quality, night_wakings, difficulty_falling_asleep, difficulty_staying_asleep, sleep_position, sleep_aids_used, nap_during_day, nap_duration_minutes, sleep_environment, notes, recorded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return ['success' => false, 'message' => 'Database prepare failed';
    
    $sleepDate = $data['sleep_date'] ?? date('Y-m-d');
    $sleepTime = $data['sleep_time'] ?? null;
    $wakeTime = $data['wake_time'] ?? null;
    $duration = !empty($data['sleep_duration_hours']) ? (float)$data['sleep_duration_hours'] : null;
    $quality = $data['sleep_quality'] ?? 'good';
    $wakings = !empty($data['night_wakings']) ? (int)$data['night_wakings'] : 0;
    $diffFalling = isset($data['difficulty_falling_asleep']) ? 1 : 0;
    $diffStaying = isset($data['difficulty_staying_asleep']) ? 1 : 0;
    $position = $data['sleep_position'] ?? 'side';
    $sleepAids = trim($data['sleep_aids_used'] ?? '');
    $napDuring = isset($data['nap_during_day']) ? 1 : 0;
    $napDuration = !empty($data['nap_duration_minutes']) ? (int)$data['nap_duration_minutes'] : 0;
    $environment = $data['sleep_environment'] ?? 'comfortable';
    $notes = trim($data['notes'] ?? '');

    $stmt->bind_param('iiisssdsiissisis', $data['elderly_id'], $user['id'], $user['facility_id'], $sleepDate, $sleepTime, $wakeTime, $duration, $quality, $wakings, $diffFalling, $diffStaying, $position, $sleepAids, $napDuring, $napDuration, $environment, $notes);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'บันทึกการนอนหลับสำเร็จ', 'record_id' => $conn->insert_id];
    } else {
        return ['success' => false, 'message' => 'ไม่สามารถบันทึกข้อมูลได้: ' . $stmt->error];
    }
}

function validateElderlyAccess($elderlyId, $facilityId, $conn) {
    // Admin users can access all elderly records (facility_id can be null for admins)
    if ($facilityId === null) {
        // For admin, just check if elderly exists
        $sql = "SELECT id FROM elderly WHERE id = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) return false;
        $stmt->bind_param('i', $elderlyId);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();
        return $result->num_rows > 0;
    }
    
    // For non-admin users, check both elderly ID and facility ID
    $sql = "SELECT id FROM elderly WHERE id = ? AND facility_id = ?";
    $stmt = $conn->prepare($sql);
    if (!$stmt) return false;
    $stmt->bind_param('ii', $elderlyId, $facilityId);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
    return $result->num_rows > 0;
}