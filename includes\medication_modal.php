<!-- Medication Recording Modal -->
<div id="medicationModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <div>
                <h2>💊 บันทึกการให้ยา</h2>
                <div id="medicationSummary" class="medication-summary">
                    <span id="summaryText">กำลังโหลด...</span>
                </div>
            </div>
            <span class="close" onclick="closeMedicationModal()">&times;</span>
        </div>
        <form id="medicationForm" method="POST">
            <input type="hidden" id="medication_elderly_id" name="elderly_id" value="">
            
            <!-- Today's Medications Section -->
            <div id="todayMedicationsSection" class="today-medications-section" style="display: none;">
                <h3>📋 การให้ยาในวันนี้</h3>
                <div id="todayMedicationsList" class="today-medications-list">
                    <!-- จะถูกโหลดด้วย JavaScript -->
                </div>
            </div>

            <div class="form-group">
                <label for="medication_date">วันที่ให้ยา:</label>
                <input type="date" id="medication_date" name="medication_date" required>
            </div>

            <div class="form-group">
                <label for="medication_name">ชื่อยา:</label>
                <input type="text" id="medication_name" name="medication_name" placeholder="ระบุชื่อยาที่ให้" required>
            </div>

            <div class="form-group">
                <label for="medication_dosage">ขนาดยา:</label>
                <input type="text" id="medication_dosage" name="medication_dosage" placeholder="เช่น 500mg, 1 เม็ด" required>
            </div>

            <div class="form-group">
                <label>เวลาที่ให้ยา:</label>
                <div class="time-period-grid">
                    <div class="time-period-card">
                        <input type="checkbox" id="morning" name="time_periods[]" value="morning">
                        <label for="morning" class="time-period-label">
                            <div class="time-icon">🌅</div>
                            <div class="time-text">เช้า</div>
                            <div class="time-range">(06:00-12:00)</div>
                        </label>
                        <input type="time" id="morning_time" name="morning_time" class="time-input" style="display: none;">
                    </div>
                    
                    <div class="time-period-card">
                        <input type="checkbox" id="afternoon" name="time_periods[]" value="afternoon">
                        <label for="afternoon" class="time-period-label">
                            <div class="time-icon">☀️</div>
                            <div class="time-text">กลางวัน</div>
                            <div class="time-range">(12:00-18:00)</div>
                        </label>
                        <input type="time" id="afternoon_time" name="afternoon_time" class="time-input" style="display: none;">
                    </div>
                    
                    <div class="time-period-card">
                        <input type="checkbox" id="evening" name="time_periods[]" value="evening">
                        <label for="evening" class="time-period-label">
                            <div class="time-icon">🌆</div>
                            <div class="time-text">เย็น</div>
                            <div class="time-range">(18:00-22:00)</div>
                        </label>
                        <input type="time" id="evening_time" name="evening_time" class="time-input" style="display: none;">
                    </div>
                    
                    <div class="time-period-card">
                        <input type="checkbox" id="bedtime" name="time_periods[]" value="bedtime">
                        <label for="bedtime" class="time-period-label">
                            <div class="time-icon">🌙</div>
                            <div class="time-text">ก่อนนอน</div>
                            <div class="time-range">(22:00-06:00)</div>
                        </label>
                        <input type="time" id="bedtime_time" name="bedtime_time" class="time-input" style="display: none;">
                    </div>
                    
                    <div class="time-period-card special">
                        <input type="checkbox" id="as_needed" name="time_periods[]" value="as_needed">
                        <label for="as_needed" class="time-period-label">
                            <div class="time-icon">⚡</div>
                            <div class="time-text">ตามความต้องการ</div>
                            <div class="time-range">(PRN)</div>
                        </label>
                        <input type="time" id="as_needed_time" name="as_needed_time" class="time-input" style="display: none;">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="medication_route">วิธีการให้:</label>
                <select id="medication_route" name="medication_route" required>
                    <option value="">เลือกวิธีการให้ยา</option>
                    <option value="oral">รับประทาน (Oral)</option>
                    <option value="injection">ฉีด (Injection)</option>
                    <option value="topical">ทาผิวหนัง (Topical)</option>
                    <option value="inhaled">สูดดม (Inhaled)</option>
                    <option value="sublingual">ใต้ลิ้น (Sublingual)</option>
                    <option value="rectal">ทางทวารหนัก (Rectal)</option>
                    <option value="other">อื่นๆ</option>
                </select>
            </div>

            <div class="form-group">
                <label for="medication_notes">หมายเหตุ:</label>
                <textarea id="medication_notes" name="medication_notes" rows="3" placeholder="หมายเหตุเพิ่มเติม เช่น ผลข้างเคียง, การตอบสนองของผู้ป่วย"></textarea>
            </div>

            <div class="form-group">
                <label for="given_by">ผู้ให้ยา:</label>
                <input type="text" id="given_by" name="given_by" value="<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>" readonly>
            </div>

            <div class="modal-actions">
                <button type="button" onclick="closeMedicationModal()" class="btn-cancel">ยกเลิก</button>
                <button type="submit" class="btn-save">บันทึกการให้ยา</button>
            </div>
        </form>
    </div>
</div>

<style>
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.medication-summary {
    font-size: 0.9rem;
    margin-top: 5px;
    opacity: 0.9;
}

.today-medications-section {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border: 1px solid rgba(156, 39, 176, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.today-medications-section h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.today-medications-list {
    display: grid;
    gap: 10px;
}

.medication-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.medication-item:hover {
    border-color: #9C27B0;
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.1);
}

.medication-info {
    flex: 1;
}

.medication-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.medication-details {
    font-size: 0.85rem;
    color: #6c757d;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.medication-time {
    background: #9C27B0;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
}

.medication-period {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    white-space: nowrap;
}

.no-medications {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-style: italic;
}

.medication-summary-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-number {
    font-weight: 700;
    color: #9C27B0;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    color: white;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.close:hover {
    background-color: rgba(255,255,255,0.2);
}

.modal form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Sarabun', sans-serif;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #9C27B0;
    box-shadow: 0 0 0 3px rgba(156, 39, 176, 0.1);
}

.time-period-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.time-period-card {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.time-period-card:hover {
    border-color: #9C27B0;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.15);
}

.time-period-card.special {
    border-color: #FF9800;
}

.time-period-card.special:hover {
    border-color: #F57C00;
}

.time-period-card input[type="checkbox"] {
    display: none;
}

.time-period-card input[type="checkbox"]:checked + .time-period-label {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
    color: white;
}

.time-period-card.special input[type="checkbox"]:checked + .time-period-label {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    color: white;
}

.time-period-label {
    display: block;
    text-align: center;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.time-icon {
    font-size: 2rem;
    margin-bottom: 8px;
}

.time-text {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.time-range {
    font-size: 0.8rem;
    opacity: 0.8;
}

.time-input {
    margin-top: 10px;
    width: 100% !important;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.btn-cancel, .btn-save {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Sarabun', sans-serif;
}

.btn-cancel {
    background-color: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.btn-save {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
    color: white;
}

.btn-save:hover {
    background: linear-gradient(135deg, #8E24AA 0%, #6A1B9A 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .time-period-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .btn-cancel, .btn-save {
        width: 100%;
    }
}
</style>