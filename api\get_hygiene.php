<?php
// get_hygiene.php - API for retrieving hygiene records
define('AIVORA_SECURITY', true);

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Include session helper
require_once __DIR__ . '/session_helper.php';
require_once __DIR__ . '/../config/database.php';

// Initialize session with same settings as main app
initializeAPISession();

// Check GET method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Check authentication and permissions
checkAuthentication();
checkPermissions(['admin', 'facility_admin', 'staff']);

// Get current user info
$currentUser = getCurrentUser();

try {
    // Get parameters
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    
    // Validate parameters
    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }
    
    // Limit the limit to reasonable bounds
    $limit = min(max($limit, 1), 50);
    $offset = max($offset, 0);
    
    // Check if elderly exists and user has access
    $isAdmin = $currentUser['user_role'] === 'admin';
    $facility_check = "";
    $params_check = [$elderly_id];
    $types_check = "i";

    if (!$isAdmin) {
        $facility_check = " AND facility_id = ?";
        $params_check[] = $currentUser['facility_id'];
        $types_check = "ii";
    }

    $sql_check = "SELECT id, facility_id FROM elderly WHERE id = ?" . $facility_check;
    $stmt_check = $conn->prepare($sql_check);
    
    if (!$stmt_check) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล']);
        exit();
    }

    $stmt_check->bind_param($types_check, ...$params_check);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง']);
        exit();
    }

    // Check if care_hygiene table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'care_hygiene'");
    if ($table_check->num_rows == 0) {
        echo json_encode([
            'success' => true,
            'message' => 'ยังไม่มีข้อมูลการบันทึกสุขอนามัย',
            'data' => [
                'records' => [],
                'summary' => [
                    'total_records' => 0,
                    'recent_bathing' => null,
                    'today_diaper_count' => 0
                ]
            ]
        ]);
        exit();
    }
    
    // Get hygiene records
    $sql = "SELECT h.*, u.name as recorded_by_user_name 
            FROM care_hygiene h
            LEFT JOIN users u ON h.recorded_by = u.id
            WHERE h.elderly_id = ?
            ORDER BY h.record_date DESC, h.created_at DESC
            LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iii", $elderly_id, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $records = [];
    while ($row = $result->fetch_assoc()) {
        // Parse other_activities if it's JSON or comma-separated
        $other_activities = [];
        if (!empty($row['other_activities'])) {
            $decoded = json_decode($row['other_activities'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $other_activities = $decoded;
            } else {
                $other_activities = explode(',', $row['other_activities']);
            }
        }
        
        // Parse images if it's JSON
        $images = [];
        if (!empty($row['images'])) {
            $decoded = json_decode($row['images'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $images = $decoded;
            }
        }
        
        // Convert bathing type to Thai
        $bathing_thai = match($row['bathing']) {
            'self' => 'อาบน้ำได้เอง',
            'assisted' => 'ช่วยอาบให้',
            'wipe' => 'เช็ดตัว',
            default => $row['bathing']
        };
        
        // Convert care status to Thai
        $oral_care_thai = $row['oral_care'] === 'done' ? 'ทำแล้ว' : 'ยังไม่ได้ทำ';
        $hair_wash_thai = $row['hair_wash'] === 'done' ? 'ทำแล้ว' : 'ยังไม่ได้ทำ';
        
        // Convert other activities to Thai
        $activity_map = [
            'haircut' => 'ตัดผม',
            'nail_cut' => 'ตัดเล็บ',
            'ear_clean' => 'แคะหู',
            'shave' => 'โกนหนวด'
        ];
        
        $other_activities_thai = array_map(function($activity) use ($activity_map) {
            return $activity_map[$activity] ?? $activity;
        }, $other_activities);
        
        $records[] = [
            'id' => $row['id'],
            'elderly_id' => $row['elderly_id'],
            'record_date' => $row['record_date'],
            'date_thai' => date('j/n/Y', strtotime($row['record_date'])),
            'diaper_count' => $row['diaper_count'],
            'diaper_pad_count' => $row['diaper_pad_count'],
            'bathing' => $row['bathing'],
            'bathing_thai' => $bathing_thai,
            'oral_care' => $row['oral_care'],
            'oral_care_thai' => $oral_care_thai,
            'hair_wash' => $row['hair_wash'],
            'hair_wash_thai' => $hair_wash_thai,
            'other_activities' => $other_activities,
            'other_activities_thai' => $other_activities_thai,
            'notes' => $row['notes'],
            'images' => $images,
            'recorded_by' => $row['recorded_by'],
            'recorded_by_name' => $row['recorded_by_name'],
            'recorded_by_user_name' => $row['recorded_by_user_name'],
            'created_at' => $row['created_at'],
            'created_at_thai' => date('j/n/Y H:i', strtotime($row['created_at']))
        ];
    }
    
    // Get summary statistics
    $today = date('Y-m-d');
    
    // Count total records
    $count_sql = "SELECT COUNT(*) as total FROM care_hygiene WHERE elderly_id = ?";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param("i", $elderly_id);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total_records = $count_result->fetch_assoc()['total'];
    
    // Get today's diaper count
    $today_sql = "SELECT SUM(diaper_count) as today_diaper_count, 
                         SUM(diaper_pad_count) as today_pad_count
                  FROM care_hygiene 
                  WHERE elderly_id = ? AND record_date = ?";
    $today_stmt = $conn->prepare($today_sql);
    $today_stmt->bind_param("is", $elderly_id, $today);
    $today_stmt->execute();
    $today_result = $today_stmt->get_result();
    $today_data = $today_result->fetch_assoc();
    
    // Get most recent bathing type
    $recent_bathing_sql = "SELECT bathing FROM care_hygiene 
                          WHERE elderly_id = ? AND bathing IS NOT NULL
                          ORDER BY record_date DESC, created_at DESC LIMIT 1";
    $recent_stmt = $conn->prepare($recent_bathing_sql);
    $recent_stmt->bind_param("i", $elderly_id);
    $recent_stmt->execute();
    $recent_result = $recent_stmt->get_result();
    $recent_bathing = $recent_result->num_rows > 0 ? $recent_result->fetch_assoc()['bathing'] : null;
    
    $summary = [
        'total_records' => (int)$total_records,
        'today_diaper_count' => (int)($today_data['today_diaper_count'] ?? 0),
        'today_pad_count' => (int)($today_data['today_pad_count'] ?? 0),
        'recent_bathing' => $recent_bathing,
        'recent_bathing_thai' => $recent_bathing ? match($recent_bathing) {
            'self' => 'อาบน้ำได้เอง',
            'assisted' => 'ช่วยอาบให้',
            'wipe' => 'เช็ดตัว',
            default => $recent_bathing
        } : null
    ];
    
    echo json_encode([
        'success' => true,
        'message' => 'ดึงข้อมูลสุขอนามัยสำเร็จ',
        'data' => [
            'records' => $records,
            'summary' => $summary,
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => $total_records
            ]
        ]
    ]);

} catch (Exception $e) {
    error_log("Error in get_hygiene.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดระบบ: ' . $e->getMessage()]);
}

$conn->close();
?>