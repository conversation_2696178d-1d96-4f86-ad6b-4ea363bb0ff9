<?php
// fix_medication_session.php - แก้ไขปัญหา session สำหรับ medication

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>แก้ไข Session สำหรับ Medication System</h1>\n";

// ตรวจสอบว่ามีการ login หรือไม่
if (!isset($_SESSION['user_id'])) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h2>ยังไม่ได้เข้าสู่ระบบ</h2>\n";
    echo "<p>กรุณาเข้าสู่ระบบก่อนทดสอบ Medication System</p>\n";
    echo "<p><a href='index.php'>ไปหน้าเข้าสู่ระบบ</a></p>\n";
    
    // สร้าง session ทดสอบสำหรับการพัฒนา
    if (isset($_GET['create_dev_session'])) {
        echo "<h3>สร้าง Session ทดสอบ</h3>\n";
        
        // ตรวจสอบว่ามีผู้ใช้ในระบบหรือไม่
        try {
            require_once __DIR__ . '/config/database.php';
            
            $user_check = $conn->query("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
            if ($user_check && $user_check->num_rows > 0) {
                $user = $user_check->fetch_assoc();
                
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['facility_id'] = $user['facility_id'];
                $_SESSION['last_activity'] = time();
                
                echo "<p style='color: green;'>✓ สร้าง session จากผู้ใช้ " . htmlspecialchars($user['username']) . " แล้ว</p>\n";
                echo "<script>setTimeout(() => window.location.reload(), 1000);</script>\n";
            } else {
                echo "<p style='color: red;'>ไม่พบผู้ใช้ในระบบ</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>\n";
        }
    } else {
        echo "<p><a href='?create_dev_session=1' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>สร้าง Session ทดสอบ (สำหรับการพัฒนา)</a></p>\n";
    }
    echo "</div>\n";
} else {
    // มี session แล้ว - ทดสอบ medication
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h2>✓ พบ Session ผู้ใช้</h2>\n";
    echo "<p><strong>User ID:</strong> " . htmlspecialchars($_SESSION['user_id']) . "</p>\n";
    echo "<p><strong>Username:</strong> " . htmlspecialchars($_SESSION['username'] ?? 'N/A') . "</p>\n";
    echo "<p><strong>Role:</strong> " . htmlspecialchars($_SESSION['user_role'] ?? 'N/A') . "</p>\n";
    echo "<p><strong>Facility ID:</strong> " . htmlspecialchars($_SESSION['facility_id'] ?? 'N/A') . "</p>\n";
    echo "</div>\n";
    
    // ทดสอบ medication API
    echo "<h2>ทดสอบ Medication API</h2>\n";
    
    if (isset($_POST['test_medication'])) {
        echo "<h3>ผลการทดสอบ:</h3>\n";
        
        // ข้อมูลทดสอบ
        $data = [
            'elderly_id' => $_POST['elderly_id'],
            'medication_date' => $_POST['medication_date'],
            'medication_name' => $_POST['medication_name'],
            'medication_dosage' => $_POST['medication_dosage'],
            'medication_route' => $_POST['medication_route'],
            'time_periods' => ['morning'],
            'morning_time' => '08:00',
            'medication_notes' => 'ทดสอบผ่าน fix script',
            'given_by' => $_SESSION['username'] ?? 'Unknown'
        ];
        
        // เรียก API ด้วย file_get_contents
        $postdata = http_build_query($data);
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Cookie: ' . session_name() . '=' . session_id()
                ],
                'content' => $postdata
            ]
        ]);
        
        $url = 'http://localhost:8080/aivora/api/save_medication.php';
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            echo "<p style='color: red;'>ไม่สามารถเชื่อมต่อ API ได้</p>\n";
        } else {
            echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>\n";
            echo "<h4>Response จาก API:</h4>\n";
            echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
            echo "</div>\n";
            
            // แสดงข้อมูลที่ส่งไป
            echo "<div style='background: #e7f3ff; border: 1px solid #b3d4fc; padding: 15px; border-radius: 5px; margin-top: 10px;'>\n";
            echo "<h4>ข้อมูลที่ส่งไป:</h4>\n";
            echo "<pre>" . htmlspecialchars(print_r($data, true)) . "</pre>\n";
            echo "</div>\n";
        }
    }
    
    // ฟอร์มทดสอบ
    echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;'>\n";
    echo "<h3>ฟอร์มทดสอบ Medication API</h3>\n";
    
    // ดึงรายชื่อผู้สูงอายุ
    echo "<div style='margin-bottom: 15px;'>\n";
    echo "<label for='elderly_id'>เลือกผู้สูงอายุ:</label><br>\n";
    echo "<select name='elderly_id' id='elderly_id' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>\n";
    
    try {
        require_once __DIR__ . '/config/database.php';
        $elderly_sql = "SELECT id, first_name, last_name FROM elderly ORDER BY first_name LIMIT 10";
        $elderly_result = $conn->query($elderly_sql);
        
        if ($elderly_result && $elderly_result->num_rows > 0) {
            while ($elderly = $elderly_result->fetch_assoc()) {
                echo "<option value='" . $elderly['id'] . "'>" . 
                     htmlspecialchars($elderly['first_name'] . ' ' . $elderly['last_name']) . 
                     " (ID: " . $elderly['id'] . ")</option>\n";
            }
        } else {
            echo "<option value='1'>ทดสอบ (ID: 1)</option>\n";
        }
    } catch (Exception $e) {
        echo "<option value='1'>ทดสอบ (ID: 1)</option>\n";
    }
    
    echo "</select>\n";
    echo "</div>\n";
    
    echo "<div style='margin-bottom: 15px;'>\n";
    echo "<label for='medication_date'>วันที่:</label><br>\n";
    echo "<input type='date' name='medication_date' id='medication_date' value='" . date('Y-m-d') . "' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>\n";
    echo "</div>\n";
    
    echo "<div style='margin-bottom: 15px;'>\n";
    echo "<label for='medication_name'>ชื่อยา:</label><br>\n";
    echo "<input type='text' name='medication_name' id='medication_name' value='Paracetamol Test' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>\n";
    echo "</div>\n";
    
    echo "<div style='margin-bottom: 15px;'>\n";
    echo "<label for='medication_dosage'>ขนาดยา:</label><br>\n";
    echo "<input type='text' name='medication_dosage' id='medication_dosage' value='500mg' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>\n";
    echo "</div>\n";
    
    echo "<div style='margin-bottom: 15px;'>\n";
    echo "<label for='medication_route'>วิธีการให้:</label><br>\n";
    echo "<select name='medication_route' id='medication_route' required style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;'>\n";
    echo "<option value='oral'>รับประทาน (Oral)</option>\n";
    echo "<option value='injection'>ฉีด (Injection)</option>\n";
    echo "<option value='topical'>ทาผิวหนัง (Topical)</option>\n";
    echo "</select>\n";
    echo "</div>\n";
    
    echo "<button type='submit' name='test_medication' value='1' style='background: #9C27B0; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer;'>ทดสอบ API</button>\n";
    echo "</form>\n";
}

// ตรวจสอบฐานข้อมูล
echo "<hr>\n";
echo "<h2>ตรวจสอบฐานข้อมูล</h2>\n";

try {
    require_once __DIR__ . '/config/database.php';
    
    if ($conn) {
        echo "<p style='color: green;'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
        
        // ตรวจสอบตาราง medication_records
        $check_med_table = $conn->query("SHOW TABLES LIKE 'medication_records'");
        if ($check_med_table && $check_med_table->num_rows > 0) {
            echo "<p style='color: green;'>✓ ตาราง medication_records มีอยู่แล้ว</p>\n";
            
            // นับข้อมูล
            $count_med = $conn->query("SELECT COUNT(*) as total FROM medication_records");
            if ($count_med) {
                $med_count = $count_med->fetch_assoc()['total'];
                echo "<p>จำนวนข้อมูลการให้ยา: <strong>$med_count</strong> รายการ</p>\n";
                
                // แสดงข้อมูลล่าสุด
                if ($med_count > 0) {
                    echo "<h4>ข้อมูลล่าสุด 3 รายการ:</h4>\n";
                    $recent = $conn->query("
                        SELECT mr.*, e.first_name, e.last_name 
                        FROM medication_records mr 
                        LEFT JOIN elderly e ON mr.elderly_id = e.id 
                        ORDER BY mr.recorded_datetime DESC 
                        LIMIT 3
                    ");
                    
                    if ($recent && $recent->num_rows > 0) {
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
                        echo "<tr><th>ผู้สูงอายุ</th><th>ยา</th><th>ขนาด</th><th>เวลา</th><th>บันทึกเมื่อ</th></tr>\n";
                        while ($row = $recent->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['medication_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['medication_dosage']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['time_period'] . ' - ' . $row['administration_time']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['recorded_datetime']) . "</td>";
                            echo "</tr>\n";
                        }
                        echo "</table>\n";
                    }
                }
            }
        } else {
            echo "<p style='color: orange;'>! ตาราง medication_records ยังไม่มี จะถูกสร้างอัตโนมัติ</p>\n";
        }
        
        // ตรวจสอบผู้สูงอายุ
        $elderly_count = $conn->query("SELECT COUNT(*) as total FROM elderly");
        if ($elderly_count) {
            $e_count = $elderly_count->fetch_assoc()['total'];
            echo "<p>จำนวนผู้สูงอายุ: <strong>$e_count</strong> คน</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>\n";
}

// ลิงก์เครื่องมือ
echo "<hr>\n";
echo "<h3>เครื่องมือช่วยเหลือ:</h3>\n";
echo "<p><a href='debug_medication_session.php'>Debug Session Detail</a></p>\n";
echo "<p><a href='create_medication_table.php'>สร้างตาราง Medication</a></p>\n";
echo "<p><a href='check_elderly_data.php'>ตรวจสอบข้อมูลผู้สูงอายุ</a></p>\n";
echo "<p><a href='?page=elderly_detail&id=1'>ทดสอบหน้า Elderly Detail</a></p>\n";
echo "<p><a href='index.php'>กลับหน้าแรก</a></p>\n";
?>