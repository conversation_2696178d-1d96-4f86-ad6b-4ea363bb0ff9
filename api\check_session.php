<?php
declare(strict_types=1);
define("AIVORA_SECURITY", true);

// Include required files to ensure proper session management
require_once __DIR__ . "/../config/database_simple.php";
require_once __DIR__ . "/../includes/functions.php";
require_once __DIR__ . "/../includes/modern_functions.php";
require_once __DIR__ . "/../includes/security.php";
require_once __DIR__ . "/../includes/auth.php";

// Start session using the proper session manager
SessionManager::start();

// Set content type
header("Content-Type: application/json; charset=utf-8");
header("Cache-Control: no-cache, must-revalidate");

try {
    
    // Debug information
    $debug = [
        'session_id' => session_id(),
        'user_id' => $_SESSION['user_id'] ?? null,
        'last_activity' => $_SESSION['last_activity'] ?? null,
        'current_time' => time()
    ];
    
    if (isset($_SESSION['last_activity'])) {
        $debug['session_age'] = time() - $_SESSION['last_activity'];
        $debug['is_expired_by_time'] = (time() - $_SESSION['last_activity']) > 3600;
    }
    
    // Check basic session data first
    $hasBasicSession = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    
    if (!$hasBasicSession) {
        http_response_code(401);
        echo json_encode([
            "success" => false,
            "authenticated" => false,
            "message" => "กรุณาเข้าสู่ระบบก่อนใช้งาน",
            "redirect" => "index.php",
            "debug" => $debug
        ]);
        exit;
    }
    
    // Check if user is logged in using auth system
    if (!isLoggedIn()) {
        http_response_code(401);
        echo json_encode([
            "success" => false,
            "authenticated" => false,
            "message" => "Session validation failed",
            "redirect" => "index.php",
            "debug" => $debug
        ]);
        exit;
    }
    
    // Check if session is expired
    if (isSessionExpired()) {
        http_response_code(401);
        echo json_encode([
            "success" => false,
            "authenticated" => false,
            "expired" => true,
            "message" => "Session หมดอายุ กรุณาเข้าสู่ระบบใหม่",
            "redirect" => "index.php",
            "debug" => $debug
        ]);
        exit;
    }
    
    // Extend session
    extendSession();
    
    // Return user info
    $currentUser = getCurrentUser();
    echo json_encode([
        "success" => true,
        "authenticated" => true,
        "user" => $currentUser,
        "time_remaining" => getSessionTimeRemaining(),
        "message" => "Session valid",
        "debug" => $debug
    ]);
    
} catch (Exception $e) {
    error_log("Session check error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "authenticated" => false,
        "message" => "เกิดข้อผิดพลาดในการตรวจสอบ Session"
    ]);
}
?>
