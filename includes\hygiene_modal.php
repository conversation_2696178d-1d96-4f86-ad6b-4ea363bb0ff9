<!-- Hygiene Recording Modal -->
<div id="hygieneModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <div>
                <h2>🧼 บันทึกสุขอนามัย</h2>
                <div id="hygieneSummary" class="hygiene-summary">
                    <span id="hygieneSummaryText">กำลังโหลด...</span>
                </div>
            </div>
            <span class="close" onclick="closeHygieneModal()">&times;</span>
        </div>
        
        <!-- Previous Hygiene Records Section -->
        <div id="previousHygieneSection" class="previous-hygiene-section" style="display: none;">
            <h3>📝 การบันทึกสุขอนามัยก่อนหน้า</h3>
            <div id="previousHygieneList" class="previous-hygiene-list">
                <!-- จะถูกโหลดด้วย JavaScript -->
            </div>
        </div>
        
        <form id="hygieneForm" method="POST" enctype="multipart/form-data" onsubmit="return false;">
            <input type="hidden" id="hygiene_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="hygiene_date">วันที่บันทึก:</label>
                <input type="date" id="hygiene_date" name="record_date" required>
            </div>

            <!-- หมวดเปลี่ยนผ้าอ้อม -->
            <div class="form-section">
                <h3>👶 เปลี่ยนผ้าอ้อม</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="diaper_count">จำนวนผ้าอ้อมที่ใช้ (แผ่น):</label>
                        <input type="number" id="diaper_count" name="diaper_count" min="0" max="50" placeholder="กี่แผ่น">
                    </div>
                    <div class="form-group">
                        <label for="diaper_pad_count">จำนวนแผ่นเสริมที่ใช้ (แผ่น):</label>
                        <input type="number" id="diaper_pad_count" name="diaper_pad_count" min="0" max="50" placeholder="กี่แผ่น">
                    </div>
                </div>
            </div>

            <!-- หมวดทำความสะอาดร่างกาย -->
            <div class="form-section">
                <h3>🚿 ทำความสะอาดร่างกาย</h3>
                
                <!-- อาบน้ำ -->
                <div class="form-group">
                    <label for="bathing">อาบน้ำ:</label>
                    <div class="bathing-grid">
                        <div class="bathing-card">
                            <input type="radio" id="bathing_self" name="bathing" value="self" checked>
                            <label for="bathing_self" class="bathing-label">
                                <div class="bathing-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">🚿</span>
                                </div>
                                <div class="bathing-text">อาบน้ำได้เอง</div>
                            </label>
                        </div>
                        
                        <div class="bathing-card">
                            <input type="radio" id="bathing_assisted" name="bathing" value="assisted">
                            <label for="bathing_assisted" class="bathing-label">
                                <div class="bathing-icon" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                                    <span class="icon">🤝</span>
                                </div>
                                <div class="bathing-text">ช่วยอาบให้</div>
                            </label>
                        </div>
                        
                        <div class="bathing-card">
                            <input type="radio" id="bathing_wipe" name="bathing" value="wipe">
                            <label for="bathing_wipe" class="bathing-label">
                                <div class="bathing-icon" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);">
                                    <span class="icon">🧽</span>
                                </div>
                                <div class="bathing-text">เช็ดตัว</div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- ทำความสะอาดช่องปาก -->
                <div class="form-group">
                    <label for="oral_care">ทำความสะอาดช่องปาก:</label>
                    <div class="oral-care-grid">
                        <div class="care-card">
                            <input type="radio" id="oral_done" name="oral_care" value="done" checked>
                            <label for="oral_done" class="care-label">
                                <div class="care-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">✅</span>
                                </div>
                                <div class="care-text">ทำแล้ว</div>
                            </label>
                        </div>
                        
                        <div class="care-card">
                            <input type="radio" id="oral_not_done" name="oral_care" value="not_done">
                            <label for="oral_not_done" class="care-label">
                                <div class="care-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">❌</span>
                                </div>
                                <div class="care-text">ยังไม่ได้ทำ</div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- สระผม -->
                <div class="form-group">
                    <label for="hair_wash">สระผม:</label>
                    <div class="hair-wash-grid">
                        <div class="care-card">
                            <input type="radio" id="hair_done" name="hair_wash" value="done" checked>
                            <label for="hair_done" class="care-label">
                                <div class="care-icon" style="background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);">
                                    <span class="icon">✅</span>
                                </div>
                                <div class="care-text">ทำแล้ว</div>
                            </label>
                        </div>
                        
                        <div class="care-card">
                            <input type="radio" id="hair_not_done" name="hair_wash" value="not_done">
                            <label for="hair_not_done" class="care-label">
                                <div class="care-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">❌</span>
                                </div>
                                <div class="care-text">ยังไม่ได้ทำ</div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- กิจกรรมอื่นๆ -->
                <div class="form-group">
                    <label>กิจกรรมอื่นๆ (เลือกได้หลายข้อ):</label>
                    <div class="other-activities-grid">
                        <div class="activity-card">
                            <input type="checkbox" id="activity_haircut" name="other_activities[]" value="haircut">
                            <label for="activity_haircut" class="activity-label">
                                <div class="activity-icon" style="background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);">
                                    <span class="icon">✂️</span>
                                </div>
                                <div class="activity-text">ตัดผม</div>
                            </label>
                        </div>
                        
                        <div class="activity-card">
                            <input type="checkbox" id="activity_nail_cut" name="other_activities[]" value="nail_cut">
                            <label for="activity_nail_cut" class="activity-label">
                                <div class="activity-icon" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);">
                                    <span class="icon">💅</span>
                                </div>
                                <div class="activity-text">ตัดเล็บ</div>
                            </label>
                        </div>
                        
                        <div class="activity-card">
                            <input type="checkbox" id="activity_ear_clean" name="other_activities[]" value="ear_clean">
                            <label for="activity_ear_clean" class="activity-label">
                                <div class="activity-icon" style="background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);">
                                    <span class="icon">👂</span>
                                </div>
                                <div class="activity-text">แคะหู</div>
                            </label>
                        </div>
                        
                        <div class="activity-card">
                            <input type="checkbox" id="activity_shave" name="other_activities[]" value="shave">
                            <label for="activity_shave" class="activity-label">
                                <div class="activity-icon" style="background: linear-gradient(135deg, #795548 0%, #5D4037 100%);">
                                    <span class="icon">🪒</span>
                                </div>
                                <div class="activity-text">โกนหนวด</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="hygiene_notes">อื่นๆ ถ้ามี:</label>
                <textarea id="hygiene_notes" name="notes" rows="3" placeholder="รายละเอียดเพิ่มเติม หรือหมายเหตุอื่นๆ"></textarea>
            </div>

            <div class="form-group">
                <label for="hygiene_images">รูปภาพประกอบ (ไม่บังคับ):</label>
                <input type="file" id="hygiene_images" name="hygiene_images[]" multiple accept="image/*" class="file-input">
                <div class="file-upload-area" onclick="document.getElementById('hygiene_images').click()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">
                        <strong>คลิกเพื่อเลือกรูปภาพ</strong><br>
                        <small>รองรับ JPG, PNG, GIF (สูงสุด 5 รูป)</small>
                    </div>
                </div>
                <div id="hygieneImagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label for="hygiene_recorded_by">ผู้บันทึก:</label>
                <input type="text" id="hygiene_recorded_by" name="recorded_by_name" value="<?php echo htmlspecialchars($_SESSION['username'] ?? ''); ?>" readonly>
            </div>

            <div class="modal-actions">
                <button type="button" onclick="closeHygieneModal()" class="btn-cancel">ยกเลิก</button>
                <button type="submit" class="btn-save">บันทึกสุขอนามัย</button>
            </div>
        </form>
    </div>
</div>

<style>
.hygiene-summary {
    font-size: 0.9rem;
    margin-top: 5px;
    opacity: 0.9;
}

.hygiene-summary-stats {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    font-size: 0.85rem;
}

.hygiene-summary-stats span {
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(255,255,255,0.7);
}

.previous-hygiene-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid rgba(33, 150, 243, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.previous-hygiene-section h3 {
    margin: 0 0 15px 0;
    color: #1565c0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.previous-hygiene-list {
    display: grid;
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.hygiene-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.hygiene-item:hover {
    border-color: #2196F3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
}

.bathing-grid, .oral-care-grid, .hair-wash-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.other-activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.bathing-card, .care-card, .activity-card {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.bathing-card:hover, .care-card:hover, .activity-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.bathing-card input[type="radio"], 
.care-card input[type="radio"],
.activity-card input[type="checkbox"] {
    display: none;
}

.bathing-card input[type="radio"]:checked + .bathing-label,
.care-card input[type="radio"]:checked + .care-label,
.activity-card input[type="checkbox"]:checked + .activity-label {
    background: rgba(33, 150, 243, 0.1);
}

.bathing-card input[type="radio"]:checked + .bathing-label .bathing-icon,
.care-card input[type="radio"]:checked + .care-label .care-icon,
.activity-card input[type="checkbox"]:checked + .activity-label .activity-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.bathing-label, .care-label, .activity-label {
    display: block;
    text-align: center;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.bathing-icon, .care-icon, .activity-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 6px;
    transition: all 0.3s ease;
}

.bathing-icon .icon, .care-icon .icon, .activity-icon .icon {
    font-size: 1.2rem;
    filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
}

.bathing-text, .care-text, .activity-text {
    font-weight: 500;
    font-size: 0.85rem;
    color: var(--text-primary);
    line-height: 1.2;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .bathing-grid, .oral-care-grid, .hair-wash-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .other-activities-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .bathing-icon, .care-icon, .activity-icon {
        width: 30px;
        height: 30px;
    }
    
    .bathing-text, .care-text, .activity-text {
        font-size: 0.75rem;
    }
}
</style>