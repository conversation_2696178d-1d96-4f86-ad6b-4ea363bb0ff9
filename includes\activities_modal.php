<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Activities Modal -->
<div id="activitiesModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>📝 บันทึกกิจกรรม</h3>
            <span class="close" onclick="closeActivitiesModal()">&times;</span>
        </div>
        
        <!-- Today's Records Summary -->
        <div class="modal-summary">
            <div id="activitiesSummaryText" class="summary-text">กำลังโหลด...</div>
        </div>
        
        <!-- Today's Activities Details (shown when exists) -->
        <div id="todayActivitiesDetails" class="today-details-section" style="display: none;">
            <div class="today-details-header">
                <h4>📋 กิจกรรมที่บันทึกวันนี้</h4>
                <button type="button" class="btn-add-more" onclick="toggleActivityForm()">➕ บันทึกเพิ่มเติม</button>
            </div>
            <div id="todayActivitiesContent" class="today-details-content"></div>
        </div>
        
        <form id="activitiesForm" method="post" enctype="multipart/form-data" style="display: block;">
            <input type="hidden" id="activities_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="activities_date">วันที่</label>
                <input type="date" id="activities_date" name="date" required>
            </div>
            
            <div class="form-group">
                <label>หมวดกิจกรรม (เลือกได้หลายรายการ)</label>
                <div class="checkbox-grid activities-grid">
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="วาดรูป">
                        <div class="checkbox-label">🎨 วาดรูป</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ทำบุญ">
                        <div class="checkbox-label">🙏 ทำบุญ</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="อาสาชุมชน">
                        <div class="checkbox-label">🤝 อาสาชุมชน</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ให้ความร่วมมือ">
                        <div class="checkbox-label">🤝 ให้ความร่วมมือ</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="เดินเล่น">
                        <div class="checkbox-label">🚶‍♀️ เดินเล่น</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ดูทีวี">
                        <div class="checkbox-label">📺 ดูทีวี</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="คาราโอเกะ">
                        <div class="checkbox-label">🎤 คาราโอเกะ</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ออกกำลังกาย">
                        <div class="checkbox-label">🏃‍♂️ ออกกำลังกาย</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="อ่านหนังสือ">
                        <div class="checkbox-label">📚 อ่านหนังสือ</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="เล่นเกม">
                        <div class="checkbox-label">🎲 เล่นเกม</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ไทเก๊ก">
                        <div class="checkbox-label">🥋 ไทเก๊ก</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ทำอาหาร">
                        <div class="checkbox-label">👨‍🍳 ทำอาหาร</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="หมากรุก">
                        <div class="checkbox-label">♟️ หมากรุก</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="งานฝีมือ">
                        <div class="checkbox-label">✂️ งานฝีมือ</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="กิจกรรมพิเศษ">
                        <div class="checkbox-label">🎉 กิจกรรมพิเศษ</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ดนตรี/เพลง">
                        <div class="checkbox-label">🎵 ดนตรี/เพลง</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="รำไทย">
                        <div class="checkbox-label">💃 รำไทย</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="ทำสวน">
                        <div class="checkbox-label">🌱 ทำสวน</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="นั่งสมาธิ">
                        <div class="checkbox-label">🧘‍♀️ นั่งสมาธิ</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="จักสาน">
                        <div class="checkbox-label">🧺 จักสาน</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="activity_types[]" value="อื่นๆ">
                        <div class="checkbox-label">➕ อื่นๆ</div>
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label>หมวดการให้ความร่วมมือ</label>
                <div class="checkbox-grid cooperation-grid">
                    <label class="checkbox-card">
                        <input type="checkbox" name="cooperation_level[]" value="ให้ความร่วมมือดี">
                        <div class="checkbox-label">😊 ร่วมมือดี</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="cooperation_level[]" value="ง่วงนอน">
                        <div class="checkbox-label">😴 ง่วงนอน</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="cooperation_level[]" value="เข้าร่วมช่วงแรก">
                        <div class="checkbox-label">⏰ เข้าร่วมช่วงแรก</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="cooperation_level[]" value="ลุกนั่งบ่อย">
                        <div class="checkbox-label">🪑 ลุกนั่งบ่อย</div>
                    </label>
                    <label class="checkbox-card">
                        <input type="checkbox" name="cooperation_level[]" value="ปฏิเสธเข้าร่วม">
                        <div class="checkbox-label">❌ ปฏิเสธเข้าร่วม</div>
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="activities_notes">คำอธิบายเพิ่มเติม (ถ้ามี)</label>
                <textarea id="activities_notes" name="notes" rows="3" placeholder="บันทึกรายละเอียดเพิ่มเติม..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="activities_images">อัพโหลดรูป (สูงสุด 5 รูป)</label>
                <input type="file" id="activities_images" name="images[]" multiple accept="image/*">
                <div id="activitiesImagePreview" class="image-preview"></div>
            </div>
            
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeActivitiesModal()">ยกเลิก</button>
                <button type="submit" class="btn-primary">บันทึกกิจกรรม</button>
            </div>
        </form>
        
        <!-- Previous Activities Section -->
        <div id="previousActivitiesSection" class="previous-records-section" style="display: none;">
            <h4>📝 กิจกรรมล่าสุด</h4>
            <div id="previousActivitiesList" class="previous-records-list"></div>
        </div>
    </div>
</div>

<style>
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 8px;
    margin: 10px 0;
}

.cooperation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 8px;
    margin: 10px 0;
}

.checkbox-card {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    min-height: 40px;
}

.checkbox-card:hover {
    border-color: var(--primary-green);
    background-color: rgba(41, 163, 126, 0.05);
}

.checkbox-card input[type="checkbox"] {
    margin-right: 6px;
    transform: scale(1.1);
    flex-shrink: 0;
}

.checkbox-card input[type="checkbox"]:checked + .checkbox-label {
    color: var(--primary-green);
    font-weight: 600;
}

.checkbox-card:has(input:checked) {
    border-color: var(--primary-green);
    background-color: rgba(41, 163, 126, 0.1);
}

.checkbox-label {
    font-size: 12px;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.activity-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.activity-date-time {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 14px;
}

.activity-activities {
    margin: 8px 0;
    font-size: 14px;
}

.activity-cooperation {
    margin: 8px 0;
    font-size: 13px;
    color: var(--text-secondary);
}

.activity-notes {
    margin-top: 8px;
    font-size: 13px;
    color: var(--text-secondary);
    font-style: italic;
}

.activities-summary-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.activities-summary-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: rgba(41, 163, 126, 0.1);
    border-radius: 15px;
    font-size: 13px;
}

.activities-summary-stats .stat-number {
    font-weight: 600;
    color: var(--primary-green);
}

.btn-detail {
    background: var(--primary-green);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.3s ease;
}

.btn-detail:hover {
    background: var(--dark-green);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.today-details-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e9ecef;
}

.today-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.today-details-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
}

.btn-add-more {
    background: var(--primary-green);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-add-more:hover {
    background: var(--dark-green);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.today-record-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
}

.today-record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.record-time {
    font-size: 12px;
    color: var(--text-secondary);
}

.record-by {
    font-size: 12px;
    color: var(--primary-green);
    font-weight: 500;
}

.record-activities {
    margin: 8px 0;
    font-size: 13px;
}

.record-cooperation {
    margin: 8px 0;
    font-size: 13px;
    color: var(--text-secondary);
}

.record-notes {
    margin: 8px 0;
    font-size: 13px;
    font-style: italic;
    color: #666;
    background: #fafafa;
    padding: 6px 10px;
    border-radius: 4px;
}

.form-toggle-hint {
    text-align: center;
    color: var(--text-secondary);
    font-size: 13px;
    margin: 10px 0;
    padding: 10px;
    background: #fff9e6;
    border-radius: 6px;
    border: 1px solid #ffe082;
}
</style>