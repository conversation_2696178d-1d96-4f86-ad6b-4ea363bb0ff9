<?php
// create_medication_table.php - สคริปต์สำหรับสร้างตาราง medication_records

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

echo "<h1>สร้างตาราง Medication Records</h1>\n";

try {
    // ตรวจสอบการเชื่อมต่อ
    if (!$conn) {
        throw new Exception("ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
    }
    
    echo "<p>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
    
    // อ่านไฟล์ SQL
    $sql_file = __DIR__ . '/create_medication_table.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ไม่พบไฟล์ SQL: " . $sql_file);
    }
    
    $sql = file_get_contents($sql_file);
    echo "<p>✓ อ่านไฟล์ SQL สำเร็จ</p>\n";
    
    // แยก SQL statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        echo "<p>กำลังรัน: " . substr($statement, 0, 50) . "...</p>\n";
        
        if ($conn->query($statement)) {
            echo "<p style='color: green;'>✓ สำเร็จ</p>\n";
        } else {
            echo "<p style='color: red;'>✗ ข้อผิดพลาด: " . $conn->error . "</p>\n";
        }
    }
    
    // ตรวจสอบว่าตารางถูกสร้างแล้ว
    $check_table = $conn->query("SHOW TABLES LIKE 'medication_records'");
    if ($check_table && $check_table->num_rows > 0) {
        echo "<h2 style='color: green;'>✓ ตาราง medication_records ถูกสร้างเรียบร้อยแล้ว</h2>\n";
        
        // แสดงโครงสร้างตาราง
        $describe = $conn->query("DESCRIBE medication_records");
        if ($describe) {
            echo "<h3>โครงสร้างตาราง:</h3>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
            while ($row = $describe->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } else {
        echo "<h2 style='color: red;'>✗ ไม่สามารถสร้างตารางได้</h2>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<br><a href='?page=elderly'>กลับไปหน้าผู้สูงอายุ</a>\n";
?>