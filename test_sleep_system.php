<?php
// ป้องกันการเรียกใช้ไฟล์โดยตรงผ่าน URL
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Mock session for testing
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['username'] = 'test_user';

echo "🌙 Test Sleep System\n";
echo "===================\n";

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

try {
    // ทดสอบ 1: บันทึกข้อมูลการนอนหลับ
    echo "\n1. 📝 ทดสอบการบันทึกข้อมูลการนอนหลับ\n";
    
    $_POST = [
        'elderly_id' => '6',
        'record_date' => date('Y-m-d'),
        'record_time' => '22:30',
        'sleep_quality' => 'นอนหลับสนิทดี',
        'additional_notes' => 'นอนหลับดี ไม่มีปัญหา',
        'recorded_by_name' => 'test_user'
    ];
    
    ob_start();
    include 'api/save_sleep.php';
    $save_response = ob_get_clean();
    
    $save_result = json_decode($save_response, true);
    if ($save_result && $save_result['success']) {
        echo "✅ บันทึกข้อมูลการนอนหลับสำเร็จ\n";
        echo "   ID: " . $save_result['data']['id'] . "\n";
        echo "   คุณภาพการนอน: " . $save_result['data']['sleep_quality'] . "\n";
        $sleep_id = $save_result['data']['id'];
    } else {
        echo "❌ เกิดข้อผิดพลาด: " . ($save_result['message'] ?? 'Unknown error') . "\n";
        exit(1);
    }
    
    // ทดสอบ 2: ดึงข้อมูลการนอนหลับ
    echo "\n2. 📊 ทดสอบการดึงข้อมูลการนอนหลับ\n";
    
    $_GET = [
        'elderly_id' => '6',
        'limit' => '5'
    ];
    
    ob_start();
    include 'api/get_sleep.php';
    $get_response = ob_get_clean();
    
    $get_result = json_decode($get_response, true);
    if ($get_result && $get_result['success']) {
        echo "✅ ดึงข้อมูลการนอนหลับสำเร็จ\n";
        echo "   จำนวนระเบียน: " . count($get_result['data']) . "\n";
        echo "   สถิติทั้งหมด: " . $get_result['stats']['total_records'] . " ครั้ง\n";
        echo "   นอนหลับสนิท: " . $get_result['stats']['excellent_count'] . " ครั้ง\n";
        echo "   มีปัญหา: " . ($get_result['stats']['poor_count'] + $get_result['stats']['bad_count']) . " ครั้ง\n";
        
        if (!empty($get_result['data'])) {
            $latest = $get_result['data'][0];
            echo "   ข้อมูลล่าสุด:\n";
            echo "     - วันที่: " . $latest['record_date_thai'] . "\n";
            echo "     - คุณภาพ: " . $latest['sleep_quality'] . " " . $latest['quality_icon'] . "\n";
            echo "     - ผู้บันทึก: " . $latest['recorded_by_name'] . "\n";
        }
    } else {
        echo "❌ เกิดข้อผิดพลาด: " . ($get_result['message'] ?? 'Unknown error') . "\n";
    }
    
    // ทดสอบ 3: ดึงข้อมูลวันนี้
    echo "\n3. 📅 ทดสอบการดึงข้อมูลวันนี้\n";
    
    $_GET = [
        'elderly_id' => '6',
        'date' => date('Y-m-d')
    ];
    
    ob_start();
    include 'api/get_sleep.php';
    $today_response = ob_get_clean();
    
    $today_result = json_decode($today_response, true);
    if ($today_result && $today_result['success']) {
        if (!empty($today_result['data'])) {
            echo "✅ พบข้อมูลการนอนหลับวันนี้\n";
            $today_record = $today_result['data'][0];
            echo "   คุณภาพ: " . $today_record['sleep_quality'] . "\n";
            echo "   เวลา: " . $today_record['record_time_thai'] . "\n";
        } else {
            echo "ℹ️ ยังไม่มีการบันทึกการนอนหลับวันนี้\n";
        }
    }
    
    // ทดสอบ 4: ทดสอบการอัพเดท (บันทึกซ้ำในวันเดียวกัน)
    echo "\n4. 🔄 ทดสอบการอัพเดทข้อมูล\n";
    
    $_POST = [
        'elderly_id' => '6',
        'record_date' => date('Y-m-d'),
        'record_time' => '23:00',
        'sleep_quality' => 'หลับๆตื่นๆ',
        'additional_notes' => 'ปรับแก้ไขข้อมูล - มีเสียงรบกวน',
        'recorded_by_name' => 'test_user_updated'
    ];
    
    ob_start();
    include 'api/save_sleep.php';
    $update_response = ob_get_clean();
    
    $update_result = json_decode($update_response, true);
    if ($update_result && $update_result['success']) {
        if ($update_result['data']['is_update']) {
            echo "✅ อัพเดทข้อมูลการนอนหลับสำเร็จ\n";
            echo "   คุณภาพใหม่: " . $update_result['data']['sleep_quality'] . "\n";
        } else {
            echo "⚠️ ข้อมูลถูกบันทึกเป็นรายการใหม่\n";
        }
    } else {
        echo "❌ เกิดข้อผิดพลาดในการอัพเดท: " . ($update_result['message'] ?? 'Unknown error') . "\n";
    }
    
    // ทดสอบ 5: ตรวจสอบโครงสร้างตาราง
    echo "\n5. 🗄️ ตรวจสอบโครงสร้างตาราง\n";
    
    $tables = ['care_sleep', 'care_sleep_images'];
    foreach ($tables as $table) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "   $table: $count ระเบียน\n";
        }
    }
    
    echo "\n🎉 การทดสอบระบบการนอนหลับเสร็จสมบูรณ์!\n";
    echo "📊 สรุป:\n";
    echo "   ✅ บันทึกข้อมูล: ผ่าน\n";
    echo "   ✅ ดึงข้อมูล: ผ่าน\n";
    echo "   ✅ อัพเดทข้อมูล: ผ่าน\n";
    echo "   ✅ จำกัดหนึ่งวันต่อหนึ่งรายการ: ผ่าน\n";
    echo "   ✅ สถิติและการวิเคราะห์: ผ่าน\n";
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    exit(1);
}
?>