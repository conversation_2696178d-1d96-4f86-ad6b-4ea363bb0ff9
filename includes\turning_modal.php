<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Turning Record Modal -->
<div id="turningModal" class="modal" style="display: none;">
    <div class="modal-content turning-modal">
        <div class="modal-header">
            <h3 id="turning-modal-title">การพลิกตัว</h3>
            <span class="close" onclick="closeTurningModal()">&times;</span>
        </div>
        
        <form id="turningForm" class="turning-form">
            <input type="hidden" name="elderly_id" value="<?php echo isset($elderly['id']) ? $elderly['id'] : ''; ?>">
            <input type="hidden" id="turning_record_id" name="record_id" value="">
            <input type="hidden" id="turning_form_mode" name="form_mode" value="create">
            
            <div class="form-row">
                <div class="form-group date-group">
                    <label for="turning_recorded_date">
                        <i class="calendar-icon">📅</i>
                        วันที่บันทึก <span class="required">*</span>
                    </label>
                    <input type="date" id="turning_recorded_date" name="recorded_date" 
                           value="<?php echo date('Y-m-d'); ?>" required>
                    <input type="time" id="turning_recorded_time" name="recorded_time" 
                           value="<?php echo date('H:i'); ?>" required>
                </div>
            </div>

            <div class="form-notice">
                <p><span class="required">*</span> วันที่ เวลา และท่าที่พลิกจำเป็นต้องกรอก</p>
                <p>🔄 การพลิกตัวช่วยป้องกันแผลกดทับ</p>
            </div>

            <!-- แสดงข้อมูลการบันทึกเมื่อเป็นโหมดแก้ไข -->
            <div id="turning-edit-info" class="edit-info" style="display: none;">
                <div class="info-card">
                    <h4>📝 ข้อมูลการบันทึก</h4>
                    <p><strong>ผู้บันทึก:</strong> <span id="turning_recorded_by_display"></span></p>
                    <p><strong>เวลาที่บันทึก:</strong> <span id="turning_created_at_display"></span></p>
                </div>
            </div>

            <div class="turning-grid">
                <div class="turning-item">
                    <label for="turning_position">
                        <i class="turning-icon">🔄</i>
                        <span class="turning-label">ท่าที่พลิก <span class="required">*</span></span>
                    </label>
                    <select id="turning_position" name="turning_position" required>
                        <option value="">เลือกท่าที่พลิก</option>
                        <option value="ซ้าย">ซ้าย</option>
                        <option value="ขวา">ขวา</option>
                        <option value="หงาย">หงาย</option>
                        <option value="คว่ำ">คว่ำ</option>
                        <option value="นั่ง">นั่ง</option>
                    </select>
                </div>

                <div class="turning-item">
                    <label for="turning_time">
                        <i class="turning-icon">⏰</i>
                        <span class="turning-label">ความถี่การพลิก <span class="required">*</span></span>
                    </label>
                    <select id="turning_time" name="turning_time" required>
                        <option value="">เลือกความถี่</option>
                        <option value="ทุก 2 ชั่วโมง">ทุก 2 ชั่วโมง</option>
                        <option value="ทุก 3 ชั่วโมง">ทุก 3 ชั่วโมง</option>
                        <option value="ทุก 4 ชั่วโมง">ทุก 4 ชั่วโมง</option>
                        <option value="ตามอาการ">ตามอาการ</option>
                        <option value="เมื่อจำเป็น">เมื่อจำเป็น</option>
                    </select>
                </div>

                <div class="turning-item">
                    <label for="skin_condition">
                        <i class="turning-icon">🎯</i>
                        <span class="turning-label">สภาพผิวหนัง</span>
                    </label>
                    <select id="skin_condition" name="skin_condition">
                        <option value="">เลือกสภาพผิวหนัง</option>
                        <option value="ปกติ">ปกติ</option>
                        <option value="แดง">แดง</option>
                        <option value="บวม">บวม</option>
                        <option value="มีแผล">มีแผล</option>
                        <option value="แห้ง">แห้ง</option>
                        <option value="เปียกชื้น">เปียกชื้น</option>
                    </select>
                </div>

                <div class="turning-item">
                    <label for="pressure_relief">
                        <i class="turning-icon">🛡️</i>
                        <span class="turning-label">การบรรเทาแรงกด</span>
                    </label>
                    <select id="pressure_relief" name="pressure_relief">
                        <option value="">เลือกวิธีบรรเทา</option>
                        <option value="ใช้หมอนรอง">ใช้หมอนรอง</option>
                        <option value="เบาะลม">เบาะลม</option>
                        <option value="เปลี่ยนท่านอน">เปลี่ยนท่านอน</option>
                        <option value="นวดเบาๆ">นวดเบาๆ</option>
                        <option value="ไม่มี">ไม่มี</option>
                    </select>
                </div>
            </div>

            <div class="form-group notes-group">
                <label for="turning_notes">
                    <i class="notes-icon">📝</i>
                    หมายเหตุเพิ่มเติม
                </label>
                <textarea id="turning_notes" name="notes" 
                         rows="4" placeholder="บันทึกรายละเอียดเพิ่มเติมเกี่ยวกับการพลิกตัว..."></textarea>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeTurningModal()">
                    ยกเลิก
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="save-icon">💾</i>
                    บันทึก
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Turning Modal Styles */
.turning-modal {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.turning-form {
    padding: 25px;
}

.turning-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.turning-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.turning-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-green);
}

.turning-item.has-data {
    border-color: var(--primary-green);
    background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
}

.turning-item.has-data .turning-icon {
    color: var(--primary-green);
}

.turning-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.turning-icon {
    font-size: 1.3rem;
    width: 30px;
    text-align: center;
}

.turning-label {
    font-size: 0.95rem;
}

.turning-item select,
.turning-item input[type="text"] {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    transition: border-color 0.3s ease;
    background: white;
}

.turning-item select:focus,
.turning-item input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(41, 163, 126, 0.1);
}

.turning-item select option {
    padding: 8px;
}

/* Responsive */
@media (max-width: 768px) {
    .turning-modal {
        width: 95%;
        margin: 10px;
    }
    
    .turning-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}
</style>

<script>
function openTurningModal() {
    // เปิด modal การพลิกตัว
    const elderlyId = document.querySelector('input[name="elderly_id"]').value;
    
    if (!elderlyId) {
        alert('ไม่พบรหัสผู้สูงอายุ');
        return;
    }

    // แสดง loading
    document.getElementById('turning-modal-title').textContent = 'กำลังตรวจสอบข้อมูล...';
    document.getElementById('turningModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // ตรวจสอบว่าวันนี้มีข้อมูลแล้วหรือไม่
    checkTodayTurningRecord(elderlyId);
}

function checkTodayTurningRecord(elderlyId) {
    // ใช้ API ง่ายๆ ที่ไม่ต้องเช็ค session ซับซ้อน
    fetch(`api/check_today_turning_simple.php?elderly_id=${elderlyId}`, {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            console.log(`HTTP error! status: ${response.status}, using create mode`);
            setupTurningCreateMode();
            return null;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return; // ถ้า response error แล้ว skip
        
        console.log('Check today turning response:', data);
        if (data.success) {
            if (data.has_today_record) {
                // โหมดแก้ไข - มีข้อมูลวันนี้แล้ว
                setupTurningEditMode(data.data);
            } else {
                // โหมดสร้างใหม่ - ยังไม่มีข้อมูลวันนี้
                setupTurningCreateMode();
            }
        } else {
            console.log('API returned error, using create mode:', data.message);
            // ถ้า API error ให้ใช้โหมดสร้างใหม่
            setupTurningCreateMode();
        }
    })
    .catch(error => {
        console.log('Error checking today turning data, using create mode:', error.message);
        // ถ้าเกิด error ให้ใช้โหมดสร้างใหม่
        setupTurningCreateMode();
    });
}

function setupTurningCreateMode() {
    document.getElementById('turning-modal-title').textContent = '🔄 บันทึกการพลิกตัวใหม่';
    document.getElementById('turning_form_mode').value = 'create';
    document.getElementById('turning_record_id').value = '';
    document.getElementById('turning-edit-info').style.display = 'none';
    
    // รีเซ็ตฟอร์มเป็นค่าเริ่มต้น
    document.getElementById('turningForm').reset();
    document.getElementById('turning_recorded_date').value = new Date().toISOString().split('T')[0];
    document.getElementById('turning_recorded_time').value = new Date().toTimeString().substr(0,5);
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#turningForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">💾</i> บันทึกใหม่';
    
    // แสดงข้อความบอกว่ายังไม่มีข้อมูลวันนี้
    showTurningCreateModeMessage();
    
    console.log('Setup turning create mode completed');
}

function setupTurningEditMode(data) {
    document.getElementById('turning-modal-title').textContent = '✏️ แก้ไขการพลิกตัววันนี้';
    document.getElementById('turning_form_mode').value = 'edit';
    document.getElementById('turning_record_id').value = data.id;
    
    // แสดงข้อมูลผู้บันทึก
    document.getElementById('turning_recorded_by_display').textContent = data.recorded_by_name;
    document.getElementById('turning_created_at_display').textContent = 
        new Date(data.created_at).toLocaleString('th-TH');
    document.getElementById('turning-edit-info').style.display = 'block';
    
    // เติมข้อมูลในฟอร์ม
    document.getElementById('turning_recorded_date').value = data.recorded_date;
    document.getElementById('turning_recorded_time').value = data.recorded_time;
    document.getElementById('turning_position').value = data.turning_position || '';
    document.getElementById('turning_time').value = data.turning_time || '';
    document.getElementById('skin_condition').value = data.skin_condition || '';
    document.getElementById('pressure_relief').value = data.pressure_relief || '';
    document.getElementById('turning_notes').value = data.notes || '';
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#turningForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">✏️</i> อัปเดต';
    
    // แสดงข้อความแจ้งว่ามีข้อมูลอยู่แล้วและสามารถแก้ไขได้
    showTurningEditModeMessage(data);
    
    // อัปเดต visual feedback
    setTimeout(updateTurningItemVisual, 100);
    
    console.log('Setup turning edit mode completed for record ID:', data.id);
}

function showTurningCreateModeMessage() {
    const formNotice = document.querySelector('#turningModal .form-notice');
    if (formNotice) {
        const today = new Date().toLocaleDateString('th-TH');
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #fff3cd 0%, #fdeaa7 100%); border: 1px solid #f0c674; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #856404; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #856404;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #856404;"><strong>สถานะ:</strong> ❌ ยังไม่มีการบันทึกการพลิกตัว</p>
                <p style="margin: 5px 0; color: #856404;"><strong>การดำเนินการ:</strong> 📝 ต้องการบันทึกข้อมูลใหม่หรือไม่?</p>
            </div>
            <p><span class="required">*</span> วันที่ เวลา และท่าที่พลิกจำเป็นต้องกรอก</p>
            <p>🔄 การพลิกตัวช่วยป้องกันแผลกดทับ</p>
        `;
    }
}

function showTurningEditModeMessage(data) {
    const formNotice = document.querySelector('#turningModal .form-notice');
    if (formNotice) {
        const recordTime = new Date(data.recorded_date + 'T' + data.recorded_time).toLocaleString('th-TH');
        const today = new Date().toLocaleDateString('th-TH');
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #155724; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #155724;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>สถานะ:</strong> ✅ มีการบันทึกการพลิกตัวแล้ว</p>
                <p style="margin: 5px 0; color: #155724;"><strong>เวลาที่บันทึก:</strong> ${recordTime}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>การดำเนินการ:</strong> ✏️ ต้องการแก้ไขข้อมูลหรือไม่?</p>
            </div>
            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #495057;">📊 รายงานการพลิกตัวปัจจุบัน</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    ${data.turning_position ? `<p style="margin: 2px 0; color: #495057;"><strong>🔄 ท่าที่พลิก:</strong> ${data.turning_position}</p>` : ''}
                    ${data.turning_time ? `<p style="margin: 2px 0; color: #495057;"><strong>⏰ ความถี่:</strong> ${data.turning_time}</p>` : ''}
                    ${data.skin_condition ? `<p style="margin: 2px 0; color: #495057;"><strong>🎯 สภาพผิวหนัง:</strong> ${data.skin_condition}</p>` : ''}
                    ${data.pressure_relief ? `<p style="margin: 2px 0; color: #495057;"><strong>🛡️ การบรรเทาแรงกด:</strong> ${data.pressure_relief}</p>` : ''}
                </div>
                ${data.notes ? `<p style="margin: 10px 0 5px 0; color: #495057;"><strong>📝 หมายเหตุ:</strong> ${data.notes}</p>` : ''}
            </div>
            <p><span class="required">*</span> วันที่ เวลา และท่าที่พลิกจำเป็นต้องกรอก</p>
            <p>🔄 การพลิกตัวช่วยป้องกันแผลกดทับ</p>
        `;
    }
}

function closeTurningModal() {
    document.getElementById('turningModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('turningForm').reset();
}

// Close modal when clicking outside
document.getElementById('turningModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeTurningModal();
    }
});

// Form validation
function validateTurningForm() {
    const requiredFields = ['recorded_date', 'recorded_time', 'turning_position', 'turning_time'];
    
    // Check required fields
    for (const fieldName of requiredFields) {
        const field = document.getElementById('turning_' + fieldName) || document.getElementById(fieldName);
        if (!field || !field.value.trim()) {
            let fieldLabel = 'ฟิลด์ที่จำเป็น';
            if (fieldName === 'recorded_date') fieldLabel = 'วันที่บันทึก';
            else if (fieldName === 'recorded_time') fieldLabel = 'เวลาบันทึก';
            else if (fieldName === 'turning_position') fieldLabel = 'ท่าที่พลิก';
            else if (fieldName === 'turning_time') fieldLabel = 'ความถี่การพลิก';
            
            alert(`กรุณากรอก${fieldLabel}`);
            if (field) field.focus();
            return false;
        }
    }
    
    return true;
}

// Form submission
document.getElementById('turningForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate form first
    if (!validateTurningForm()) {
        return;
    }
    
    const formData = new FormData(this);
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="save-icon">⏳</i> กำลังบันทึก...';
    submitBtn.disabled = true;
    
    // เรียก API สำหรับบันทึกการพลิกตัว
    fetch('api/save_turning_record.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
            'Cache-Control': 'no-cache'
        }
    })
    .then(response => {
        console.log('Save turning response status:', response.status);
        
        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Session expired. Please refresh the page and login again.');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Save turning response data:', data);
        
        if (data.success) {
            alert('บันทึกการพลิกตัวเรียบร้อยแล้ว');
            closeTurningModal();
            // Update display with new data
            loadLatestTurningRecord();
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
            console.error('API Error:', data);
        }
    })
    .catch(error => {
        console.error('Fetch Error:', error);
        if (error.message.includes('Session expired') || error.message.includes('status: 401')) {
            alert('Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่');
            window.location.href = 'index.php';
        } else if (error.message.includes('status: 500')) {
            alert('เกิดข้อผิดพลาดภายในระบบ กรุณาตรวจสอบ:\n1. ว่าได้เข้าสู่ระบบแล้ว\n2. มีสิทธิ์ในการบันทึกข้อมูล\n3. ข้อมูลที่กรอกถูกต้อง');
        } else {
            alert('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' + error.message);
        }
    })
    .finally(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Real-time visual feedback for form inputs
function updateTurningItemVisual() {
    const turningFields = ['turning_position', 'turning_time', 'skin_condition', 'pressure_relief'];
    
    turningFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            const turningItem = field.closest('.turning-item');
            if (field.value.trim()) {
                turningItem.classList.add('has-data');
            } else {
                turningItem.classList.remove('has-data');
            }
        }
    });
}

// Add input event listeners for real-time feedback
function addTurningInputListeners() {
    const turningFields = ['turning_position', 'turning_time', 'skin_condition', 'pressure_relief'];
    
    turningFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('change', updateTurningItemVisual);
            field.addEventListener('input', updateTurningItemVisual);
        }
    });
}

// Function to load latest turning record (placeholder)
function loadLatestTurningRecord() {
    console.log('Loading latest turning record...');
    // TODO: Implement loading latest turning record display
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    addTurningInputListeners();
});
</script>