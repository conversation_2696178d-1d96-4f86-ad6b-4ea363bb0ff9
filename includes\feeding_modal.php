<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Feeding Record Modal -->
<div id="feedingModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>🍽️ บันทึกการทาน/ฟีดอาหาร</h2>
            <span class="close" onclick="closeFeedingModal()">&times;</span>
        </div>
        
        <!-- Summary Section -->
        <div class="summary-section">
            <div class="summary-header">
                <h3>📊 สรุปการบันทึก</h3>
            </div>
            <div id="feedingSummaryText" class="summary-text">กำลังโหลด...</div>
        </div>

        <!-- Previous Records Section -->
        <div id="previousFeedingSection" class="previous-section" style="display: none;">
            <div class="previous-header">
                <h4>📋 การบันทึกล่าสุด</h4>
            </div>
            <div id="previousFeedingList" class="previous-list"></div>
        </div>
        
        <form id="feedingForm" onsubmit="submitFeedingForm(event)" enctype="multipart/form-data">
            <input type="hidden" id="feeding_elderly_id" name="elderly_id" value="">
            
            <div class="form-group">
                <label for="feeding_date">วันที่:</label>
                <input type="date" id="feeding_date" name="feeding_date" required>
            </div>

            <div class="form-group">
                <label for="feeding_time">เวลา:</label>
                <input type="time" id="feeding_time" name="feeding_time" required>
            </div>

            <!-- ทานทางปาก -->
            <div class="feeding-section">
                <h3>🍴 ทานทางปาก</h3>
                
                <div class="form-group">
                    <label>ปริมาณอาหารที่ทาน:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="oral_food_amount[]" value="all"> ทานได้หมด</label>
                        <label><input type="checkbox" name="oral_food_amount[]" value="poor"> ทานไม่ค่อยได้</label>
                        <label><input type="checkbox" name="oral_food_amount[]" value="half"> ทานได้ครึ่งหนึ่ง</label>
                        <label><input type="checkbox" name="oral_food_amount[]" value="snack"> ทานอาหารว่าง</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>สำลัก:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="choking[]" value="no"> ไม่สำลัก</label>
                        <label><input type="checkbox" name="choking[]" value="yes"> สำลัก</label>
                    </div>
                </div>
            </div>

            <!-- ทานทางสายยาง -->
            <div class="feeding-section">
                <h3>🥤 ทานทางสายยาง</h3>
                
                <div class="form-group">
                    <label>ประเภทอาหาร:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="tube_food_type[]" value="liquid"> อาหารเหลว</label>
                        <label><input type="checkbox" name="tube_food_type[]" value="milk"> นม</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>ปริมาณอาหารที่รับได้:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="tube_food_tolerance[]" value="good"> ฟีดรับได้ดี</label>
                        <label><input type="checkbox" name="tube_food_tolerance[]" value="little"> ฟีดรับได้เล็กน้อย</label>
                        <label><input type="checkbox" name="tube_food_tolerance[]" value="none"> ฟีดรับไม่ได้เลย</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>อาหารตกค้างในกระเพาะ:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="gastric_residue[]" value="none"> ไม่มี</label>
                        <label><input type="checkbox" name="gastric_residue[]" value="over_50cc"> เหลือค้างมากกว่า 50cc</label>
                        <label><input type="checkbox" name="gastric_residue[]" value="blood"> สีแดง (มีเลือดปน)</label>
                        <label><input type="checkbox" name="gastric_residue[]" value="bile"> สีเขียว (มีน้ำดีปน)</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="gastric_residue_notes">หมายเหตุอาหารตกค้างในกระเพาะ:</label>
                    <textarea id="gastric_residue_notes" name="gastric_residue_notes" rows="3" placeholder="ระบุรายละเอียดเพิ่มเติม..."></textarea>
                </div>

                <div class="form-group">
                    <label>สถานะการให้อาหาร:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="feeding_status[]" value="completed"> ทานหมด</label>
                        <label><input type="checkbox" name="feeding_status[]" value="incomplete"> ทานไม่หมด</label>
                    </div>
                </div>
            </div>

            <!-- หมายเหตุทั่วไป -->
            <div class="form-group">
                <label for="feeding_notes">หมายเหตุ:</label>
                <textarea id="feeding_notes" name="notes" rows="3" placeholder="หมายเหตุเพิ่มเติม..."></textarea>
            </div>

            <!-- อัพโหลดรูป -->
            <div class="form-group">
                <label for="feeding_images">อัพโหลดรูป:</label>
                <input type="file" id="feeding_images" name="images[]" multiple accept="image/*" onchange="handleFeedingImageUpload()">
                <small class="file-info">สามารถเลือกได้หลายไฟล์ (สูงสุด 5 ไฟล์)</small>
                <div id="feedingImagePreview" class="image-preview-container"></div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeFeedingModal()">ยกเลิก</button>
                <button type="submit" class="btn btn-primary">บันทึกข้อมูลการทาน/ฟีดอาหาร</button>
            </div>
        </form>
    </div>
</div>

<style>
.feeding-section {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.feeding-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #29a37e;
    font-weight: 600;
}

.checkbox-group {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s;
    white-space: nowrap;
    border: 1px solid #e9ecef;
}

.checkbox-group label:hover {
    background-color: rgba(41, 163, 126, 0.1);
    border-color: #29a37e;
}

.checkbox-group label:has(input:checked) {
    background-color: rgba(41, 163, 126, 0.15);
    border-color: #29a37e;
    font-weight: 500;
}

.checkbox-group input[type="checkbox"] {
    margin: 0;
    accent-color: #29a37e;
}

.image-preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.preview-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.preview-item img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
}

.preview-remove {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.preview-remove:hover {
    background: #dc3545;
}

/* Summary and Previous Records Sections */
.summary-section {
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f5e8 100%);
    border-radius: 8px;
    border-left: 4px solid #29a37e;
}

.summary-header h3 {
    margin: 0 0 10px 0;
    color: #29a37e;
    font-size: 1.1rem;
    font-weight: 600;
}

.summary-text {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.4;
}

.previous-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.previous-header h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.previous-list {
    max-height: 200px;
    overflow-y: auto;
}

.feeding-item {
    padding: 12px;
    margin-bottom: 10px;
    background: white;
    border-radius: 6px;
    border-left: 3px solid #29a37e;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.feeding-item:last-child {
    margin-bottom: 0;
}

.feeding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.feeding-date-time {
    font-weight: 600;
    color: #29a37e;
    font-size: 0.9rem;
}

.feeding-details {
    color: #666;
    font-size: 0.85rem;
    line-height: 1.3;
}

.feeding-summary-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.feeding-summary-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: rgba(41, 163, 126, 0.1);
    border-radius: 15px;
    font-size: 0.9rem;
}

.feeding-summary-stats .stat-number {
    font-weight: 700;
    color: #29a37e;
}

/* Responsive design for checkboxes */
@media (max-width: 768px) {
    .checkbox-group {
        flex-direction: column;
        gap: 10px;
    }
    
    .checkbox-group label {
        justify-content: flex-start;
        padding: 10px;
    }
    
    .feeding-summary-stats {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .previous-list {
        max-height: 150px;
    }
}
</style>