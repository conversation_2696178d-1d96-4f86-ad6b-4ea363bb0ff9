<?php
require_once 'config/database.php';

echo "<h3>ตารางทั้งหมดในฐานข้อมูล</h3>";

// แสดงตารางทั้งหมด
$sql = "SHOW TABLES";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<h4>รายชื่อตารางทั้งหมด:</h4>";
    echo "<ul>";
    while($row = $result->fetch_array()) {
        $table_name = $row[0];
        echo "<li>" . htmlspecialchars($table_name);
        
        // ถ้าเป็นตารางที่เกี่ยวกับ incident ให้แสดงโครงสร้าง
        if (strpos(strtolower($table_name), 'incident') !== false) {
            echo " <strong>(เกี่ยวกับ incident)</strong>";
            echo "<br><small>โครงสร้าง:</small>";
            $desc_sql = "DESCRIBE " . $table_name;
            $desc_result = $conn->query($desc_sql);
            if ($desc_result) {
                echo "<ul>";
                while($desc_row = $desc_result->fetch_assoc()) {
                    echo "<li>" . $desc_row['Field'] . " (" . $desc_row['Type'] . ")</li>";
                }
                echo "</ul>";
            }
        }
        echo "</li>";
    }
    echo "</ul>";
} else {
    echo "ไม่พบตารางใดๆ หรือเกิดข้อผิดพลาด: " . $conn->error;
}

// ตรวจสอบเฉพาะตาราง incident_reports ถ้ามี
echo "<hr><h4>ตรวจสอบตาราง incident_reports:</h4>";
$check_incident = "SHOW TABLES LIKE 'incident_reports'";
$result_incident = $conn->query($check_incident);

if ($result_incident && $result_incident->num_rows > 0) {
    echo "✅ พบตาราง incident_reports<br>";
    
    $desc_sql = "DESCRIBE incident_reports";
    $desc_result = $conn->query($desc_sql);
    if ($desc_result) {
        echo "<table border='1' style='border-collapse: collapse; margin-top: 10px;'>";
        echo "<tr style='background-color: #f2f2f2;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while($row = $desc_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "❌ ไม่พบตาราง incident_reports - ต้องสร้างใหม่";
}

$conn->close();
?>