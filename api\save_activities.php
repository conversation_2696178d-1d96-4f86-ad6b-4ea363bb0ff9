<?php
header('Content-Type: application/json; charset=utf-8');
ini_set('display_errors', 0);
error_reporting(0);

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์ (เฉพาะ admin, facility_admin, staff)
if (!in_array($_SESSION['user_role'], ['admin', 'facility_admin', 'staff'])) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'ไม่มีสิทธิ์ในการใช้งานฟังก์ชันนี้'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

if (!isset($conn) || $conn->connect_error) {
    echo json_encode([
        'success' => false,
        'message' => 'ไม่สามารถเชื่อมต่อฐานข้อมูลได้'
    ]);
    exit;
}

try {
    // ตรวจสอบ method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }

    // รับข้อมูลจากฟอร์ม
    $elderly_id = isset($_POST['elderly_id']) ? (int)$_POST['elderly_id'] : 0;
    $date = isset($_POST['date']) ? trim($_POST['date']) : '';
    $activity_types = isset($_POST['activity_types']) && is_array($_POST['activity_types']) ? $_POST['activity_types'] : [];
    $cooperation_level = isset($_POST['cooperation_level']) && is_array($_POST['cooperation_level']) ? $_POST['cooperation_level'] : [];
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : null;
    $recorded_by = $_SESSION['user_id'];

    // Validate input
    if ($elderly_id <= 0) {
        throw new Exception('รหัสผู้สูงอายุไม่ถูกต้อง');
    }

    if (empty($date)) {
        throw new Exception('กรุณาระบุวันที่');
    }

    if (empty($activity_types)) {
        throw new Exception('กรุณาเลือกกิจกรรมอย่างน้อย 1 รายการ');
    }

    // ตรวจสอบว่าผู้สูงอายุอยู่ในสถานพยาบาลเดียวกันหรือไม่ (สำหรับ non-admin)
    if ($_SESSION['user_role'] !== 'admin') {
        $facility_check_sql = "SELECT id FROM elderly WHERE id = ? AND facility_id = ?";
        $facility_stmt = $conn->prepare($facility_check_sql);
        $facility_stmt->bind_param("ii", $elderly_id, $_SESSION['facility_id']);
        $facility_stmt->execute();
        $facility_result = $facility_stmt->get_result();
        
        if ($facility_result->num_rows === 0) {
            throw new Exception('ไม่พบข้อมูลผู้สูงอายุในสถานพยาบาลของคุณ');
        }
        $facility_stmt->close();
    } else {
        // Admin - ตรวจสอบว่ามีผู้สูงอายุนี้จริง
        $elderly_check_sql = "SELECT id FROM elderly WHERE id = ?";
        $elderly_stmt = $conn->prepare($elderly_check_sql);
        $elderly_stmt->bind_param("i", $elderly_id);
        $elderly_stmt->execute();
        $elderly_result = $elderly_stmt->get_result();
        
        if ($elderly_result->num_rows === 0) {
            throw new Exception('ไม่พบข้อมูลผู้สูงอายุ');
        }
        $elderly_stmt->close();
    }

    // ไม่ตรวจสอบว่าวันนี้มีการบันทึกแล้วหรือไม่ - อนุญาตให้บันทึกหลายครั้งต่อวัน
    // เพื่อให้สามารถบันทึกกิจกรรมเพิ่มเติมได้ในแต่ละวัน

    // จัดการอัพโหลดรูปภาพ
    $uploaded_images = [];
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        $upload_dir = __DIR__ . '/../assets/img/activities/' . $elderly_id . '/';
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_file_size = 5 * 1024 * 1024; // 5MB

        for ($i = 0; $i < count($_FILES['images']['name']) && $i < 5; $i++) {
            if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                $file_type = $_FILES['images']['type'][$i];
                $file_size = $_FILES['images']['size'][$i];
                
                if (!in_array($file_type, $allowed_types)) {
                    continue; // ข้ามไฟล์ที่ไม่ใช่รูปภาพ
                }
                
                if ($file_size > $max_file_size) {
                    continue; // ข้ามไฟล์ที่ใหญ่เกินไป
                }

                $file_extension = pathinfo($_FILES['images']['name'][$i], PATHINFO_EXTENSION);
                $filename = 'activity_' . date('Y-m-d_H-i-s') . '_' . ($i + 1) . '.' . $file_extension;
                $file_path = $upload_dir . $filename;

                if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $file_path)) {
                    $uploaded_images[] = 'assets/img/activities/' . $elderly_id . '/' . $filename;
                }
            }
        }
    }

    // เตรียมข้อมูลสำหรับบันทึก
    $activity_types_json = json_encode($activity_types, JSON_UNESCAPED_UNICODE);
    $cooperation_level_json = !empty($cooperation_level) ? json_encode($cooperation_level, JSON_UNESCAPED_UNICODE) : null;
    $images_json = !empty($uploaded_images) ? json_encode($uploaded_images, JSON_UNESCAPED_UNICODE) : null;

    // บันทึกข้อมูลลงฐานข้อมูล
    $insert_sql = "INSERT INTO activities_records 
                   (elderly_id, date, activity_types, cooperation_level, notes, images, recorded_by, recorded_datetime) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $insert_stmt = $conn->prepare($insert_sql);
    $insert_stmt->bind_param("isssssi", 
        $elderly_id, 
        $date, 
        $activity_types_json, 
        $cooperation_level_json, 
        $notes, 
        $images_json, 
        $recorded_by
    );

    if ($insert_stmt->execute()) {
        $activity_id = $conn->insert_id;
        
        echo json_encode([
            'success' => true,
            'message' => 'บันทึกกิจกรรมเรียบร้อยแล้ว',
            'data' => [
                'activity_id' => $activity_id,
                'date' => $date,
                'activity_count' => count($activity_types),
                'cooperation_count' => count($cooperation_level),
                'image_count' => count($uploaded_images)
            ]
        ]);
    } else {
        throw new Exception('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' . $conn->error);
    }

    $insert_stmt->close();

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>