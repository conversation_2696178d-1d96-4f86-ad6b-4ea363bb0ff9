<?php
require_once 'config/database.php';

echo "Adding blood_sugar column to vital_signs table...\n";

$sql = "ALTER TABLE vital_signs ADD COLUMN blood_sugar INT NULL AFTER oxygen_saturation";

if ($conn->query($sql) === TRUE) {
    echo "Column blood_sugar added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column blood_sugar already exists\n";
    } else {
        echo "Error: " . $conn->error . "\n";
    }
}

// Verify the column was added
echo "\nChecking vital_signs table structure:\n";
$result = $conn->query("DESCRIBE vital_signs");
while($row = $result->fetch_assoc()) {
    echo $row['Field'] . " - " . $row['Type'] . "\n";
}
?>