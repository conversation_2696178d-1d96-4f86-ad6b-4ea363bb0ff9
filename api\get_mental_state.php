<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role'])) {
    http_response_code(401);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit();
}

// ตรวจสอบสิทธิ์การเข้าถึง
$allowed_roles = ['admin', 'facility_admin', 'staff'];
if (!in_array($_SESSION['user_role'], $allowed_roles)) {
    http_response_code(403);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'ไม่มีสิทธิ์ในการดูข้อมูลสภาวะจิตใจ'
    ]);
    exit();
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

header('Content-Type: application/json');

try {
    // รับพารามิเตอร์
    $elderly_id = filter_input(INPUT_GET, 'elderly_id', FILTER_VALIDATE_INT);
    $limit = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT) ?: 10;
    $offset = filter_input(INPUT_GET, 'offset', FILTER_VALIDATE_INT) ?: 0;
    $date_from = filter_input(INPUT_GET, 'date_from', FILTER_SANITIZE_STRING);
    $date_to = filter_input(INPUT_GET, 'date_to', FILTER_SANITIZE_STRING);
    
    // ตรวจสอบพารามิเตอร์ที่จำเป็น
    if (!$elderly_id) {
        throw new Exception('กรุณาระบุรหัสผู้สูงอายุ');
    }
    
    // จำกัดจำนวนระเบียนต่อหน้า
    $limit = min($limit, 50);
    
    // ตรวจสอบว่า elderly_id มีอยู่จริงและมีสิทธิ์เข้าถึง
    $check_elderly = "SELECT id, facility_id FROM elderly WHERE id = ?";
    if ($_SESSION['user_role'] !== 'admin') {
        $check_elderly .= " AND facility_id = ?";
    }
    
    $stmt = $conn->prepare($check_elderly);
    if ($_SESSION['user_role'] !== 'admin') {
        $stmt->bind_param("ii", $elderly_id, $_SESSION['facility_id']);
    } else {
        $stmt->bind_param("i", $elderly_id);
    }
    
    $stmt->execute();
    $elderly_result = $stmt->get_result();
    
    if ($elderly_result->num_rows == 0) {
        throw new Exception('ไม่พบข้อมูลผู้สูงอายุหรือไม่มีสิทธิ์เข้าถึง');
    }
    
    // สร้าง SQL query สำหรับดึงข้อมูลสภาวะจิตใจ
    $sql = "SELECT 
                id,
                elderly_id,
                record_date,
                record_time,
                mental_conditions,
                additional_notes,
                recorded_by,
                recorded_by_name,
                recorded_datetime,
                updated_at
            FROM care_mental_state 
            WHERE elderly_id = ?";
    
    $params = [$elderly_id];
    $types = "i";
    
    // เพิ่มเงื่อนไขการกรองตามวันที่
    if (!empty($date_from)) {
        $sql .= " AND record_date >= ?";
        $params[] = $date_from;
        $types .= "s";
    }
    
    if (!empty($date_to)) {
        $sql .= " AND record_date <= ?";
        $params[] = $date_to;
        $types .= "s";
    }
    
    // เรียงลำดับตามวันที่และเวลาล่าสุด
    $sql .= " ORDER BY record_date DESC, record_time DESC, recorded_datetime DESC";
    
    // เพิ่ม limit และ offset
    $sql .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL');
    }
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $records = [];
    while ($row = $result->fetch_assoc()) {
        // จัดรูปแบบข้อมูลก่อนส่ง
        $row['record_date_thai'] = date('d/m/Y', strtotime($row['record_date']));
        $row['record_time_thai'] = !empty($row['record_time']) ? date('H:i', strtotime($row['record_time'])) : '';
        $row['recorded_datetime_thai'] = date('d/m/Y H:i', strtotime($row['recorded_datetime']));
        
        // แปลง mental_conditions เป็น array
        if (!empty($row['mental_conditions'])) {
            $row['mental_conditions_array'] = explode(',', $row['mental_conditions']);
        } else {
            $row['mental_conditions_array'] = [];
        }
        
        $records[] = $row;
    }
    
    // คำนวณสถิติ
    $stats_sql = "SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN record_date = CURDATE() THEN 1 END) as today_records,
                    COUNT(CASE WHEN record_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_records,
                    COUNT(CASE WHEN FIND_IN_SET('ตื่นตัวดี', mental_conditions) THEN 1 END) as alert_count,
                    COUNT(CASE WHEN FIND_IN_SET('อารมณ์แจ่มใส', mental_conditions) THEN 1 END) as happy_count,
                    COUNT(CASE WHEN FIND_IN_SET('ซึมลง', mental_conditions) OR FIND_IN_SET('สับสน', mental_conditions) THEN 1 END) as concerning_count,
                    COUNT(CASE WHEN FIND_IN_SET('มีอาการเพ้อ', mental_conditions) OR FIND_IN_SET('ไม่ตอบสนอง', mental_conditions) THEN 1 END) as critical_count
                  FROM care_mental_state 
                  WHERE elderly_id = ?";
    
    $stats_params = [$elderly_id];
    if (!empty($date_from)) {
        $stats_sql .= " AND record_date >= ?";
        $stats_params[] = $date_from;
    }
    if (!empty($date_to)) {
        $stats_sql .= " AND record_date <= ?";
        $stats_params[] = $date_to;
    }
    
    $stats_stmt = $conn->prepare($stats_sql);
    $stats_stmt->bind_param(str_repeat("s", count($stats_params)), ...$stats_params);
    $stats_stmt->execute();
    $stats_result = $stats_stmt->get_result();
    $stats = $stats_result->fetch_assoc();
    
    // นับจำนวนรวมสำหรับ pagination
    $count_sql = "SELECT COUNT(*) as total FROM care_mental_state WHERE elderly_id = ?";
    $count_params = [$elderly_id];
    $count_types = "i";
    
    if (!empty($date_from)) {
        $count_sql .= " AND record_date >= ?";
        $count_params[] = $date_from;
        $count_types .= "s";
    }
    if (!empty($date_to)) {
        $count_sql .= " AND record_date <= ?";
        $count_params[] = $date_to;
        $count_types .= "s";
    }
    
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($count_types, ...$count_params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total_records = $count_result->fetch_assoc()['total'];
    
    // ส่งผลลัพธ์
    echo json_encode([
        'success' => true,
        'data' => $records,
        'stats' => $stats,
        'pagination' => [
            'total' => (int)$total_records,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total_records
        ],
        'message' => 'ดึงข้อมูลสภาวะจิตใจเรียบร้อยแล้ว'
    ]);
    
} catch (Exception $e) {
    // บันทึก error log
    error_log("[" . date('Y-m-d H:i:s') . "] Error in get_mental_state.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>