<?php
// Simple session check without complex auth system
session_start();
header('Content-Type: application/json');

$response = [
    'session_id' => session_id(),
    'raw_session_data' => $_SESSION,
    'has_user_id' => isset($_SESSION['user_id']),
    'user_id_value' => $_SESSION['user_id'] ?? null,
    'has_last_activity' => isset($_SESSION['last_activity']),
    'last_activity_value' => $_SESSION['last_activity'] ?? null,
    'current_time' => time(),
    'simple_logged_in_check' => isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])
];

if (isset($_SESSION['last_activity'])) {
    $age = time() - $_SESSION['last_activity'];
    $response['session_age_seconds'] = $age;
    $response['session_age_minutes'] = round($age / 60, 2);
    $response['is_expired_1hour'] = $age > 3600;
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>