<?php
/**
 * HYGIENE ENUM TROUBLESHOOTER & MONITORING TOOL
 * 
 * This script provides comprehensive debugging and monitoring capabilities
 * for the hygiene form ENUM truncation issue.
 * 
 * Usage: php hygiene_enum_troubleshooter.php [action]
 * Actions: check, monitor, fix, test
 */

include 'config/database.php';

class HygieneEnumTroubleshooter {
    private $conn;
    private $logFile;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->logFile = __DIR__ . '/logs/hygiene_enum_debug.log';
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] $message" . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        echo $logEntry;
    }
    
    public function checkTableStructure() {
        $this->log("=== CHECKING TABLE STRUCTURE ===");
        
        // Check if table exists
        $tableCheck = $this->conn->query("SHOW TABLES LIKE 'care_hygiene'");
        if ($tableCheck->num_rows == 0) {
            $this->log("❌ ERROR: care_hygiene table does not exist!");
            return false;
        }
        
        // Check table structure
        $result = $this->conn->query("DESCRIBE care_hygiene");
        $enumFields = [];
        
        while ($row = $result->fetch_assoc()) {
            if (in_array($row['Field'], ['bathing', 'oral_care', 'hair_wash'])) {
                $enumFields[$row['Field']] = $row;
                $this->log("✅ {$row['Field']}: {$row['Type']} | Default: {$row['Default']}");
                
                // Validate ENUM values
                if (strpos($row['Type'], 'enum') !== false) {
                    preg_match_all("/'([^']+)'/", $row['Type'], $matches);
                    $allowedValues = $matches[1];
                    $this->log("   Allowed values: " . implode(', ', $allowedValues));
                } else {
                    $this->log("   ❌ WARNING: Field is not ENUM type!");
                }
            }
        }
        
        return $enumFields;
    }
    
    public function checkRecentErrors() {
        $this->log("=== CHECKING RECENT ERRORS ===");
        
        // Check PHP error log
        $phpErrorLog = __DIR__ . '/logs/php_errors.log';
        if (file_exists($phpErrorLog)) {
            $logContent = file_get_contents($phpErrorLog);
            $lines = explode("\n", $logContent);
            $recentErrors = array_slice($lines, -50); // Last 50 lines
            
            $hygieneTruncationErrors = array_filter($recentErrors, function($line) {
                return strpos($line, 'Data truncated') !== false && 
                       (strpos($line, 'bathing') !== false || 
                        strpos($line, 'oral_care') !== false || 
                        strpos($line, 'hair_wash') !== false);
            });
            
            if (!empty($hygieneTruncationErrors)) {
                $this->log("❌ FOUND RECENT TRUNCATION ERRORS:");
                foreach ($hygieneTruncationErrors as $error) {
                    $this->log("   " . trim($error));
                }
            } else {
                $this->log("✅ No recent ENUM truncation errors found");
            }
        } else {
            $this->log("⚠️  PHP error log not found at: $phpErrorLog");
        }
    }
    
    public function testEnumInserts() {
        $this->log("=== TESTING ENUM INSERTS ===");
        
        // Get a valid elderly and facility ID
        $result = $this->conn->query("SELECT elderly.id as elderly_id, elderly.facility_id FROM elderly LIMIT 1");
        if (!$result || $result->num_rows == 0) {
            $this->log("❌ ERROR: No elderly records found for testing");
            return false;
        }
        
        $testData = $result->fetch_assoc();
        $elderlyId = $testData['elderly_id'];
        $facilityId = $testData['facility_id'];
        
        $this->log("Using elderly_id: $elderlyId, facility_id: $facilityId for testing");
        
        // Test problematic values
        $testCases = [
            ['name' => 'BOM_Values', 'bathing' => "\xEF\xBB\xBFself", 'oral_care' => "\xEF\xBB\xBFdone", 'hair_wash' => 'not_done'],
            ['name' => 'Spaced_Values', 'bathing' => ' self ', 'oral_care' => '  done  ', 'hair_wash' => ' not_done '],
            ['name' => 'Case_Issues', 'bathing' => 'SELF', 'oral_care' => 'Done', 'hair_wash' => 'NOT_DONE'],
            ['name' => 'Control_Chars', 'bathing' => "self\x00", 'oral_care' => "done\x01", 'hair_wash' => "not_done\x0A"]
        ];
        
        $successCount = 0;
        foreach ($testCases as $testCase) {
            if ($this->testSingleInsert($elderlyId, $facilityId, $testCase)) {
                $successCount++;
            }
        }
        
        $this->log("Test Results: $successCount/" . count($testCases) . " tests passed");
        return $successCount === count($testCases);
    }
    
    private function testSingleInsert($elderlyId, $facilityId, $testCase) {
        $this->log("Testing: " . $testCase['name']);
        
        // Use the same cleaning function from save_hygiene.php
        $cleanedBathing = $this->cleanEnumValue($testCase['bathing'], ['self', 'assisted', 'wipe'], 'self');
        $cleanedOralCare = $this->cleanEnumValue($testCase['oral_care'], ['done', 'not_done'], 'done');
        $cleanedHairWash = $this->cleanEnumValue($testCase['hair_wash'], ['done', 'not_done'], 'done');
        
        $sql = "INSERT INTO care_hygiene (elderly_id, facility_id, record_date, diaper_count, diaper_pad_count, bathing, oral_care, hair_wash, other_activities, notes, recorded_by, recorded_by_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($sql);
        if (!$stmt) {
            $this->log("❌ Prepare failed: " . $this->conn->error);
            return false;
        }
        
        $recordDate = date('Y-m-d');
        $diaperCount = 0;
        $diaperPadCount = 0;
        $otherActivities = '';
        $notes = 'Test: ' . $testCase['name'];
        $recordedBy = 1;
        $recordedByName = 'System Test';
        
        $stmt->bind_param("iisiissssssis", 
            $elderlyId, $facilityId, $recordDate, $diaperCount, $diaperPadCount,
            $cleanedBathing, $cleanedOralCare, $cleanedHairWash, $otherActivities, $notes, null,
            $recordedBy, $recordedByName
        );
        
        if ($stmt->execute()) {
            $insertId = $this->conn->insert_id;
            $this->log("✅ Success: Record inserted with ID $insertId");
            
            // Clean up test record
            $deleteStmt = $this->conn->prepare("DELETE FROM care_hygiene WHERE id = ?");
            $deleteStmt->bind_param("i", $insertId);
            $deleteStmt->execute();
            
            return true;
        } else {
            $this->log("❌ Execute failed: " . $stmt->error);
            return false;
        }
    }
    
    private function cleanEnumValue($value, $allowedValues, $default) {
        if (!isset($value) || empty($value)) {
            return $default;
        }
        
        // Remove BOM and other invisible characters
        $cleaned = trim($value);
        $cleaned = preg_replace('/[\x00-\x1F\x7F-\xFF]/', '', $cleaned);
        $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'UTF-8');
        $cleaned = filter_var($cleaned, FILTER_SANITIZE_STRING, FILTER_FLAG_STRIP_LOW | FILTER_FLAG_STRIP_HIGH);
        
        // Additional cleaning for common invisible characters
        $cleaned = str_replace(['\u00A0', '\u200B', '\u200C', '\u200D', '\uFEFF'], '', $cleaned);
        $cleaned = preg_replace('/\s+/', '', $cleaned);
        
        // Validate against allowed values (case-insensitive first, then strict)
        $lowerCleaned = strtolower($cleaned);
        foreach ($allowedValues as $allowed) {
            if (strtolower($allowed) === $lowerCleaned) {
                return $allowed;
            }
        }
        
        return $default;
    }
    
    public function monitorRealTimeErrors() {
        $this->log("=== STARTING REAL-TIME ERROR MONITORING ===");
        $this->log("Press Ctrl+C to stop monitoring");
        
        $errorLogPath = __DIR__ . '/logs/php_errors.log';
        if (!file_exists($errorLogPath)) {
            touch($errorLogPath);
        }
        
        $lastSize = filesize($errorLogPath);
        
        while (true) {
            clearstatcache();
            $currentSize = filesize($errorLogPath);
            
            if ($currentSize > $lastSize) {
                $newContent = '';
                $handle = fopen($errorLogPath, 'r');
                fseek($handle, $lastSize);
                while (($line = fgets($handle)) !== false) {
                    $newContent .= $line;
                }
                fclose($handle);
                
                // Check for hygiene-related truncation errors
                if (strpos($newContent, 'Data truncated') !== false && 
                    (strpos($newContent, 'bathing') !== false || 
                     strpos($newContent, 'oral_care') !== false || 
                     strpos($newContent, 'hair_wash') !== false)) {
                    $this->log("🚨 HYGIENE ENUM TRUNCATION ERROR DETECTED:");
                    $this->log($newContent);
                }
                
                $lastSize = $currentSize;
            }
            
            sleep(1); // Check every second
        }
    }
    
    public function generateReport() {
        $this->log("=== GENERATING COMPREHENSIVE REPORT ===");
        
        $report = [];
        $report['timestamp'] = date('Y-m-d H:i:s');
        $report['table_structure'] = $this->checkTableStructure();
        
        // Check for recent successful inserts
        $recentInserts = $this->conn->query("SELECT COUNT(*) as count FROM care_hygiene WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $report['recent_inserts_24h'] = $recentInserts ? $recentInserts->fetch_assoc()['count'] : 0;
        
        // Check for any data anomalies
        $anomalies = $this->conn->query("SELECT 
            COUNT(CASE WHEN bathing NOT IN ('self', 'assisted', 'wipe') THEN 1 END) as invalid_bathing,
            COUNT(CASE WHEN oral_care NOT IN ('done', 'not_done') THEN 1 END) as invalid_oral_care,
            COUNT(CASE WHEN hair_wash NOT IN ('done', 'not_done') THEN 1 END) as invalid_hair_wash
            FROM care_hygiene");
        
        if ($anomalies) {
            $report['data_anomalies'] = $anomalies->fetch_assoc();
        }
        
        $this->log("Report generated and saved to: " . $this->logFile);
        return $report;
    }
}

// Main execution
$action = $argv[1] ?? 'check';
$troubleshooter = new HygieneEnumTroubleshooter($conn);

switch ($action) {
    case 'check':
        $troubleshooter->checkTableStructure();
        $troubleshooter->checkRecentErrors();
        break;
        
    case 'test':
        $troubleshooter->testEnumInserts();
        break;
        
    case 'monitor':
        $troubleshooter->monitorRealTimeErrors();
        break;
        
    case 'report':
        $report = $troubleshooter->generateReport();
        echo json_encode($report, JSON_PRETTY_PRINT);
        break;
        
    default:
        echo "Usage: php hygiene_enum_troubleshooter.php [action]\n";
        echo "Actions:\n";
        echo "  check   - Check table structure and recent errors\n";
        echo "  test    - Test ENUM inserts with problematic values\n";
        echo "  monitor - Real-time error monitoring\n";
        echo "  report  - Generate comprehensive report\n";
}

$conn->close();
?>