<?php
// Test file for health form submission
session_start();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบบันทึกฟอร์มสุขภาพ</title>
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f4f3;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        h1 {
            color: #29a37e;
            margin-bottom: 30px;
        }
        .session-info {
            background: #e8f5f0;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-button {
            background: #29a37e;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #228a66;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background: #f5f5f5;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🏥 ทดสอบระบบบันทึกฟอร์มสุขภาพ AIVORA</h1>
        
        <div class="session-info">
            <h3>📊 สถานะ Session ปัจจุบัน</h3>
            <div id="sessionStatus">กำลังตรวจสอบ...</div>
        </div>
        
        <div class="test-section">
            <h3>🔐 1. ทดสอบการเข้าสู่ระบบ</h3>
            <button class="test-button" onclick="testLogin()">เข้าสู่ระบบด้วย admin</button>
            <button class="test-button" onclick="checkSession()">ตรวจสอบ Session</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>💓 2. ทดสอบบันทึกสัญญาณชีพ</h3>
            <button class="test-button" onclick="testVitalSigns()">บันทึกสัญญาณชีพทดสอบ</button>
            <button class="test-button" onclick="getVitalSigns()">ดึงข้อมูลสัญญาณชีพ</button>
            <div id="vitalResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>💊 3. ทดสอบบันทึกการให้ยา</h3>
            <button class="test-button" onclick="testMedication()">บันทึกการให้ยาทดสอบ</button>
            <button class="test-button" onclick="getMedications()">ดึงข้อมูลการให้ยา</button>
            <div id="medicationResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 4. ทดสอบ API Session Helper</h3>
            <button class="test-button" onclick="testSessionAPI()">ทดสอบ Session API</button>
            <div id="apiResult" class="result"></div>
        </div>
    </div>

    <script>
        // Check session status on page load
        window.onload = function() {
            checkSession();
        };
        
        function checkSession() {
            fetch('api/test_session.php', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('sessionStatus');
                const resultDiv = document.getElementById('loginResult');
                
                if (data.session_data.user_id) {
                    statusDiv.innerHTML = `
                        <div class="success">
                            ✅ เข้าสู่ระบบแล้ว<br>
                            👤 ผู้ใช้: ${data.session_data.username || 'N/A'}<br>
                            🆔 User ID: ${data.session_data.user_id}<br>
                            👥 Role: ${data.session_data.user_role || 'N/A'}<br>
                            🏥 Facility ID: ${data.session_data.facility_id || 'N/A'}<br>
                            ⏱️ เวลาที่เหลือ: ${Math.floor((data.session_data.session_remaining || 0) / 60)} นาที
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="warning">
                            ⚠️ ยังไม่ได้เข้าสู่ระบบ
                        </div>
                    `;
                }
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('sessionStatus').innerHTML = `
                    <div class="error">
                        ❌ ไม่สามารถตรวจสอบ session ได้: ${error.message}
                    </div>
                `;
            });
        }
        
        function testLogin() {
            const formData = new FormData();
            formData.append('username', 'admin');
            formData.append('password', 'admin123');
            
            fetch('index.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(text => {
                // Check if login was successful
                if (text.includes('dashboard') || text.includes('home.php')) {
                    document.getElementById('loginResult').innerHTML = `
                        <div class="success">✅ เข้าสู่ระบบสำเร็จ</div>
                    `;
                    // Refresh session status
                    setTimeout(checkSession, 500);
                } else {
                    document.getElementById('loginResult').innerHTML = `
                        <div class="error">❌ เข้าสู่ระบบไม่สำเร็จ</div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('loginResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            });
        }
        
        function testVitalSigns() {
            const formData = new FormData();
            formData.append('elderly_id', '1');
            formData.append('recorded_date', new Date().toISOString().split('T')[0]);
            formData.append('recorded_time', new Date().toTimeString().split(' ')[0]);
            formData.append('temperature', '36.5');
            formData.append('heart_rate', '72');
            formData.append('blood_pressure_systolic', '120');
            formData.append('blood_pressure_diastolic', '80');
            formData.append('respiratory_rate', '16');
            formData.append('oxygen_saturation', '98');
            formData.append('additional_notes', 'ทดสอบบันทึกสัญญาณชีพ');
            
            fetch('api/save_vital_signs_simple.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('vitalResult');
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ ${data.message}\n\n${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.message}\n\n${JSON.stringify(data, null, 2)}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('vitalResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            });
        }
        
        function getVitalSigns() {
            fetch('api/get_vital_signs.php?elderly_id=1&limit=5', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('vitalResult');
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ ดึงข้อมูลสำเร็จ\n\n${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.message}\n\n${JSON.stringify(data, null, 2)}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('vitalResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            });
        }
        
        function testMedication() {
            const formData = new FormData();
            formData.append('elderly_id', '1');
            formData.append('medication_date', new Date().toISOString().split('T')[0]);
            formData.append('medication_name', 'Paracetamol');
            formData.append('dosage', '500 mg');
            formData.append('route', 'oral');
            formData.append('time_periods[]', 'morning');
            formData.append('morning_time', '08:00');
            formData.append('notes', 'ทดสอบบันทึกการให้ยา');
            
            fetch('api/save_medication.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('medicationResult');
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ ${data.message}\n\n${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.message}\n\n${JSON.stringify(data, null, 2)}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('medicationResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            });
        }
        
        function getMedications() {
            const today = new Date().toISOString().split('T')[0];
            fetch(`api/get_today_medications.php?elderly_id=1&date=${today}`, {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('medicationResult');
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ ดึงข้อมูลสำเร็จ\n\n${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.message}\n\n${JSON.stringify(data, null, 2)}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('medicationResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            });
        }
        
        function testSessionAPI() {
            fetch('api/check_session.php', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('apiResult');
                if (data.authenticated) {
                    resultDiv.innerHTML = `<div class="success">✅ Session API ทำงานปกติ\n\n${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="warning">⚠️ ${data.message || 'Not authenticated'}\n\n${JSON.stringify(data, null, 2)}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('apiResult').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            });
        }
    </script>
</body>
</html>