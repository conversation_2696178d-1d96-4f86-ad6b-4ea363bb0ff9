<?php
// ป้องกันการเรียกใช้ไฟล์โดยตรงผ่าน URL
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/config/database.php';

try {
    // ตรวจสอบว่าตารางมีอยู่หรือไม่
    $result = $conn->query("SHOW TABLES LIKE 'care_mental_state'");
    
    if ($result->num_rows > 0) {
        echo "✅ ตาราง care_mental_state มีอยู่แล้ว\n";
        
        // ตรวจสอบโครงสร้างตาราง
        $structure = $conn->query("DESCRIBE care_mental_state");
        if ($structure) {
            echo "\n📊 โครงสร้างตาราง care_mental_state:\n";
            echo "┌─────────────────────┬──────────────────┬──────────┐\n";
            echo "│ ฟิลด์               │ ประเภท           │ ข้อมูล   │\n";
            echo "├─────────────────────┼──────────────────┼──────────┤\n";
            
            while ($row = $structure->fetch_assoc()) {
                printf("│ %-19s │ %-16s │ %-8s │\n", 
                    $row['Field'], 
                    substr($row['Type'], 0, 16), 
                    $row['Null'] === 'YES' ? 'NULL' : 'NOT NULL'
                );
            }
            echo "└─────────────────────┴──────────────────┴──────────┘\n";
            
            // ตรวจสอบ SET values
            $set_check = $conn->query("SHOW COLUMNS FROM care_mental_state LIKE 'mental_conditions'");
            if ($set_check && $set_check->num_rows > 0) {
                $set_info = $set_check->fetch_assoc();
                echo "\n🎯 ค่าที่อนุญาตใน mental_conditions:\n";
                echo $set_info['Type'] . "\n";
            }
        }
        
    } else {
        echo "❌ ไม่พบตาราง care_mental_state\n";
        echo "🔨 กำลังสร้างตาราง...\n";
        
        // อ่านและรันไฟล์ SQL
        $sql = file_get_contents(__DIR__ . '/config/create_mental_state_table.sql');
        
        if ($sql === false) {
            throw new Exception('ไม่สามารถอ่านไฟล์ SQL ได้');
        }
        
        // รันคำสั่ง SQL
        if ($conn->query($sql) === TRUE) {
            echo "✅ สร้างตาราง care_mental_state สำเร็จ\n";
        } else {
            throw new Exception("เกิดข้อผิดพลาด: " . $conn->error);
        }
    }
    
    // ตรวจสอบตารางเพิ่มเติมสำหรับเก็บรูปภาพ (ถ้าจำเป็น)
    $image_table_check = $conn->query("SHOW TABLES LIKE 'care_mental_state_images'");
    if ($image_table_check->num_rows == 0) {
        echo "\n🖼️ สร้างตารางเก็บรูปภาพ mental state...\n";
        
        $image_sql = "
        CREATE TABLE IF NOT EXISTS care_mental_state_images (
            id INT PRIMARY KEY AUTO_INCREMENT,
            mental_state_id INT NOT NULL,
            image_path VARCHAR(500) NOT NULL,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (mental_state_id) REFERENCES care_mental_state(id) ON DELETE CASCADE,
            INDEX idx_mental_state_id (mental_state_id)
        );";
        
        if ($conn->query($image_sql) === TRUE) {
            echo "✅ สร้างตาราง care_mental_state_images สำเร็จ\n";
        } else {
            echo "⚠️ ไม่สามารถสร้างตารางรูปภาพได้: " . $conn->error . "\n";
        }
    } else {
        echo "✅ ตาราง care_mental_state_images มีอยู่แล้ว\n";
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    exit(1);
}
?>