<?php
define('AIVORA_SECURITY', true);

// Include required files
require_once __DIR__ . '/config/database_simple.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';

// Start session
SessionManager::start();

echo "<h2>Session and Authentication Test</h2>";

// ข้อมูลปัจจุบัน
echo "<h3>Current Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Session Status:</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Status: " . session_status() . "<br>";
echo "Is Logged In: " . (isLoggedIn() ? 'Yes' : 'No') . "<br>";

// ทดสอบการล็อกอิน
if (isset($_POST['test_login'])) {
    // สร้าง test user
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'test_user';
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['facility_name'] = 'Test Facility';
    $_SESSION['last_activity'] = time();
    
    echo "<div style='background: green; color: white; padding: 10px; margin: 10px 0;'>
          Test session created successfully!
          </div>";
    
    // รีเฟรชหน้า
    echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
}

// ทดสอบการล็อกเอาท์
if (isset($_POST['test_logout'])) {
    session_destroy();
    echo "<div style='background: red; color: white; padding: 10px; margin: 10px 0;'>
          Session destroyed!
          </div>";
    
    // รีเฟรชหน้า
    echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
}

// ทดสอบการเรียก API
echo "<h3>Test API Call:</h3>";
if (isLoggedIn()) {
    echo "✅ Ready to test API calls<br>";
    echo "<a href='#' onclick='testWeightHeightAPI()' style='background: blue; color: white; padding: 5px 10px; text-decoration: none;'>Test Weight Height API</a>";
} else {
    echo "❌ Not logged in - cannot test API calls";
}

?>

<form method="post" style="margin: 20px 0;">
    <button type="submit" name="test_login" style="background: green; color: white; padding: 10px; border: none;">
        Create Test Session
    </button>
    <button type="submit" name="test_logout" style="background: red; color: white; padding: 10px; border: none;">
        Destroy Session
    </button>
</form>

<script>
function testWeightHeightAPI() {
    // ทดสอบเรียก check API
    fetch('api/check_today_weight_height_simple.php?elderly_id=1', {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        console.log('API Response:', data);
        alert('API Response: ' + JSON.stringify(data, null, 2));
    })
    .catch(error => {
        console.error('API Error:', error);
        alert('API Error: ' + error.message);
    });
}
</script>