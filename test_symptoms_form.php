<?php
define('AIVORA_SECURITY', true);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';

// ล็อกอินอัตโนมัติสำหรับทดสอบ
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['facility_id'] = 1;
    $_SESSION['last_activity'] = time();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>Test Symptoms Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        form { max-width: 500px; }
        label { display: block; margin: 10px 0 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #29a37e; color: white; border: none; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h2>ทดสอบฟอร์มบันทึกอาการ</h2>
    
    <div class="status">
        <strong>สถานะ Session:</strong> <?= isLoggedIn() ? '✓ ล็อกอินแล้ว' : '✗ ยังไม่ได้ล็อกอิน' ?><br>
        <strong>User ID:</strong> <?= $_SESSION['user_id'] ?? 'ไม่มี' ?><br>
        <strong>Username:</strong> <?= $_SESSION['username'] ?? 'ไม่มี' ?><br>
        <strong>Role:</strong> <?= $_SESSION['user_role'] ?? 'ไม่มี' ?>
    </div>

    <form id="symptomsForm">
        <label>รหัสผู้สูงอายุ:</label>
        <input type="number" name="elderly_id" value="1" required>
        
        <label>วันที่บันทึก:</label>
        <input type="date" name="record_date" value="<?= date('Y-m-d') ?>" required>
        
        <label>เวลา (ไม่บังคับ):</label>
        <input type="time" name="record_time" value="<?= date('H:i') ?>">
        
        <label>อาการที่พบ:</label>
        <textarea name="symptoms" required placeholder="บรรยายอาการที่พบ">ไข้ ปวดหัว อ่อนเพลีย</textarea>
        
        <label>ระดับความรุนแรง:</label>
        <select name="severity" required>
            <option value="mild">เบา</option>
            <option value="moderate">ปานกลาง</option>
            <option value="severe">รุนแรง</option>
            <option value="critical">วิกฤต</option>
        </select>
        
        <label>อุณหภูมิ (°C):</label>
        <input type="number" name="temperature" step="0.1" min="30" max="45" value="37.2">
        
        <label>หมายเหตุ:</label>
        <textarea name="notes" placeholder="รายละเอียดเพิ่มเติม">ให้ยาลดไข้แล้ว</textarea>
        
        <label>ผู้บันทึก:</label>
        <input type="text" name="recorded_by_name" value="<?= $_SESSION['username'] ?? '' ?>" readonly>
        
        <button type="submit">บันทึกอาการ</button>
    </form>
    
    <div id="result"></div>

    <script>
    document.getElementById('symptomsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const resultDiv = document.getElementById('result');
        const submitBtn = this.querySelector('button[type="submit"]');
        
        submitBtn.disabled = true;
        submitBtn.textContent = 'กำลังบันทึก...';
        resultDiv.innerHTML = '<div class="status">กำลังส่งข้อมูล...</div>';
        
        fetch('api/save_symptoms.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            
            if (data.success) {
                resultDiv.innerHTML = '<div class="status success">✓ ' + data.message + '</div>';
            } else {
                resultDiv.innerHTML = '<div class="status error">✗ ' + data.message + '</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = '<div class="status error">✗ เกิดข้อผิดพลาด: ' + error.message + '</div>';
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = 'บันทึกอาการ';
        });
    });
    </script>
</body>
</html>