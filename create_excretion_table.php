<?php
// สร้างตาราง excretion_records สำหรับบันทึกการขับถ่าย
define('AIVORA_SECURITY', true);

require_once 'config/database.php';

// สร้างตาราง excretion_records
$create_table_sql = "
CREATE TABLE IF NOT EXISTS excretion_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    elderly_id INT NOT NULL,
    record_date DATE NOT NULL,
    
    -- ปัสสาวะ
    urine_frequency INT DEFAULT NULL,
    urine_volume INT DEFAULT NULL COMMENT 'ml',
    urine_method ENUM('self', 'catheter', 'other') DEFAULT 'self',
    urine_other_text VARCHAR(255) DEFAULT NULL,
    
    -- อุจจาระ
    stool_frequency INT DEFAULT NULL,
    stool_color VARCHAR(100) DEFAULT NULL,
    stool_consistency ENUM('normal', 'liquid', 'semi_liquid', 'hard') DEFAULT 'normal',
    stool_volume INT DEFAULT NULL COMMENT 'ml',
    
    -- รายละเอียดเพิ่มเติม
    notes TEXT DEFAULT NULL,
    image_path VARCHAR(500) DEFAULT NULL,
    
    -- ข้อมูลการบันทึก
    recorded_by INT NOT NULL,
    recorded_by_name VARCHAR(100) NOT NULL,
    recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_datetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_elderly_date (elderly_id, record_date),
    INDEX idx_urine_method (urine_method),
    INDEX idx_stool_consistency (stool_consistency),
    INDEX idx_recorded_datetime (recorded_datetime),
    INDEX idx_elderly_excretion (elderly_id, record_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
";

try {
    if ($conn->query($create_table_sql)) {
        echo "✅ สร้างตาราง excretion_records สำเร็จ\n";
        
        // ตรวจสอบโครงสร้างตาราง
        $describe = $conn->query("DESCRIBE excretion_records");
        if ($describe) {
            echo "\n📋 โครงสร้างตาราง excretion_records:\n";
            echo str_pad("Field", 25) . str_pad("Type", 35) . str_pad("Null", 8) . "Default\n";
            echo str_repeat("-", 75) . "\n";
            
            while ($row = $describe->fetch_assoc()) {
                echo str_pad($row['Field'], 25) . 
                     str_pad($row['Type'], 35) . 
                     str_pad($row['Null'], 8) . 
                     $row['Default'] . "\n";
            }
        }
        
        // สร้างตัวอย่างข้อมูลสำหรับทดสอบ (ถ้าตารางว่าง)
        $count_check = $conn->query("SELECT COUNT(*) as total FROM excretion_records");
        if ($count_check) {
            $count = $count_check->fetch_assoc()['total'];
            if ($count == 0) {
                echo "\n➕ เพิ่มข้อมูลตัวอย่างสำหรับทดสอบ...\n";
                
                $sample_data = [
                    [1, date('Y-m-d'), 5, 300, 'self', null, 1, 'น้ำตาล', 'normal', 150, 'การขับถ่ายปกติ', 1, 'Admin User'],
                    [1, date('Y-m-d', strtotime('-1 day')), 3, 250, 'catheter', null, 1, 'เหลือง', 'liquid', 100, 'อุจจาระเหลว ต้องติดตาม', 1, 'Admin User']
                ];
                
                $insert_sql = "INSERT INTO excretion_records (elderly_id, record_date, urine_frequency, urine_volume, urine_method, urine_other_text, stool_frequency, stool_color, stool_consistency, stool_volume, notes, recorded_by, recorded_by_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_sql);
                
                foreach ($sample_data as $data) {
                    $stmt->bind_param('isiississsis', ...$data);
                    if ($stmt->execute()) {
                        echo "   ✓ เพิ่มข้อมูลตัวอย่าง: ปัสสาวะ {$data[2]} ครั้ง อุจจาระ {$data[6]} ครั้ง\n";
                    }
                }
            } else {
                echo "\n📊 ตารางมีข้อมูลอยู่แล้ว: $count รายการ\n";
            }
        }
        
    } else {
        echo "❌ เกิดข้อผิดพลาดในการสร้างตาราง: " . $conn->error . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

$conn->close();
echo "\n✅ เสร็จสิ้นการสร้างตาราง excretion_records\n";
?>