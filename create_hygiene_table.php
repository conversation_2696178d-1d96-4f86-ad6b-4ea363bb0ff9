<?php
// สร้างตาราง hygiene_records สำหรับบันทึกสุขอนามัย
define('AIVORA_SECURITY', true);

require_once 'config/database.php';

// สร้างตาราง hygiene_records
$create_table_sql = "
CREATE TABLE IF NOT EXISTS hygiene_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    elderly_id INT NOT NULL,
    record_date DATE NOT NULL,
    
    -- หมวดเปลี่ยนผ้าอ้อม
    diaper_count INT DEFAULT NULL COMMENT 'จำนวนผ้าอ้อม',
    diaper_pad_count INT DEFAULT NULL COMMENT 'จำนวนแผ่นเสริม',
    
    -- หมวดทำความสะอาดร่างกาย
    bathing ENUM('self', 'assisted', 'wipe') DEFAULT 'self' COMMENT 'อาบน้ำ',
    oral_care ENUM('done', 'not_done') DEFAULT 'done' COMMENT 'ทำความสะอาดช่องปาก',
    hair_wash ENUM('done', 'not_done') DEFAULT 'done' COMMENT 'สระผม',
    other_activities TEXT DEFAULT NULL COMMENT 'กิจกรรมอื่นๆ (JSON array)',
    
    -- รายละเอียดเพิ่มเติม
    notes TEXT DEFAULT NULL,
    image_path VARCHAR(500) DEFAULT NULL,
    
    -- ข้อมูลการบันทึก
    recorded_by INT NOT NULL,
    recorded_by_name VARCHAR(100) NOT NULL,
    recorded_datetime DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_datetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_elderly_date (elderly_id, record_date),
    INDEX idx_bathing (bathing),
    INDEX idx_oral_care (oral_care),
    INDEX idx_hair_wash (hair_wash),
    INDEX idx_recorded_datetime (recorded_datetime),
    INDEX idx_elderly_hygiene (elderly_id, record_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
";

try {
    if ($conn->query($create_table_sql)) {
        echo "✅ สร้างตาราง hygiene_records สำเร็จ\n";
        
        // ตรวจสอบโครงสร้างตาราง
        $describe = $conn->query("DESCRIBE hygiene_records");
        if ($describe) {
            echo "\n📋 โครงสร้างตาราง hygiene_records:\n";
            echo str_pad("Field", 25) . str_pad("Type", 35) . str_pad("Null", 8) . "Default\n";
            echo str_repeat("-", 75) . "\n";
            
            while ($row = $describe->fetch_assoc()) {
                echo str_pad($row['Field'], 25) . 
                     str_pad($row['Type'], 35) . 
                     str_pad($row['Null'], 8) . 
                     $row['Default'] . "\n";
            }
        }
        
        // สร้างตัวอย่างข้อมูลสำหรับทดสอบ (ถ้าตารางว่าง)
        $count_check = $conn->query("SELECT COUNT(*) as total FROM hygiene_records");
        if ($count_check) {
            $count = $count_check->fetch_assoc()['total'];
            if ($count == 0) {
                echo "\n➕ เพิ่มข้อมูลตัวอย่างสำหรับทดสอบ...\n";
                
                $sample_data = [
                    [1, date('Y-m-d'), 3, 2, 'self', 'done', 'done', '["nail_cut","haircut"]', 'ดูแลสุขอนามัยปกติ', 1, 'Admin User'],
                    [1, date('Y-m-d', strtotime('-1 day')), 2, 1, 'assisted', 'done', 'not_done', '["nail_cut"]', 'ช่วยอาบน้ำ ยังไม่ได้สระผม', 1, 'Admin User']
                ];
                
                $insert_sql = "INSERT INTO hygiene_records (elderly_id, record_date, diaper_count, diaper_pad_count, bathing, oral_care, hair_wash, other_activities, notes, recorded_by, recorded_by_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_sql);
                
                foreach ($sample_data as $data) {
                    $stmt->bind_param('isiisssssis', ...$data);
                    if ($stmt->execute()) {
                        echo "   ✓ เพิ่มข้อมูลตัวอย่าง: ผ้าอ้อม {$data[2]} แผ่น อาบน้ำ {$data[4]}\n";
                    }
                }
            } else {
                echo "\n📊 ตารางมีข้อมูลอยู่แล้ว: $count รายการ\n";
            }
        }
        
    } else {
        echo "❌ เกิดข้อผิดพลาดในการสร้างตาราง: " . $conn->error . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

$conn->close();
echo "\n✅ เสร็จสิ้นการสร้างตาราง hygiene_records\n";
?>