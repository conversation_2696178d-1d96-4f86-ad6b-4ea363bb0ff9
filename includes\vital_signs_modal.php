<?php
// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}
?>

<!-- Vital Signs Modal -->
<div id="vitalSignsModal" class="modal" style="display: none;">
    <div class="modal-content vital-signs-modal">
        <div class="modal-header">
            <h3 id="modal-title">สัญญาณชีพ</h3>
            <span class="close" onclick="closeVitalSignsModal()">&times;</span>
        </div>
        
        <form id="vitalSignsForm" class="vital-signs-form">
            <input type="hidden" name="elderly_id" value="<?php echo isset($elderly['id']) ? $elderly['id'] : ''; ?>">
            <input type="hidden" id="vital_sign_id" name="vital_sign_id" value="">
            <input type="hidden" id="form_mode" name="form_mode" value="create">
            
            <div class="form-row">
                <div class="form-group date-group">
                    <label for="recorded_date">
                        <i class="calendar-icon">📅</i>
                        วันที่บันทึก <span class="required">*</span>
                    </label>
                    <input type="date" id="recorded_date" name="recorded_date" 
                           value="<?php echo date('Y-m-d'); ?>" required>
                    <input type="time" id="recorded_time" name="recorded_time" 
                           value="<?php echo date('H:i'); ?>" required>
                </div>
            </div>

            <div class="form-notice">
                <p><span class="required">*</span> วันที่และเวลาจำเป็นต้องกรอก</p>
                <p>📌 กรุณากรอกสัญญาณชีพอย่างน้อย 1 รายการ</p>
            </div>

            <!-- แสดงข้อมูลการบันทึกเมื่อเป็นโหมดแก้ไข -->
            <div id="edit-info" class="edit-info" style="display: none;">
                <div class="info-card">
                    <h4>📝 ข้อมูลการบันทึก</h4>
                    <p><strong>ผู้บันทึก:</strong> <span id="recorded_by_display"></span></p>
                    <p><strong>เวลาที่บันทึก:</strong> <span id="created_at_display"></span></p>
                </div>
            </div>

            <div class="vital-signs-grid">
                <div class="vital-item">
                    <label for="temperature">
                        <i class="vital-icon temperature-icon">🌡️</i>
                        <span class="vital-label">อุณหภูมิ</span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="temperature" name="temperature" 
                               step="0.1" min="30" max="45" placeholder="36.5">
                        <span class="input-unit">°C</span>
                    </div>
                </div>

                <div class="vital-item blood-pressure-item">
                    <label>
                        <i class="vital-icon bp-icon">🩸</i>
                        <span class="vital-label">ความดันตัวบน / ความดันตัวล่าง</span>
                    </label>
                    <div class="blood-pressure-group">
                        <div class="bp-input-group">
                            <input type="number" id="blood_pressure_systolic" name="blood_pressure_systolic" 
                                   min="80" max="200" placeholder="120">
                            <small class="bp-label">ตัวบน</small>
                        </div>
                        <span class="bp-separator">/</span>
                        <div class="bp-input-group">
                            <input type="number" id="blood_pressure_diastolic" name="blood_pressure_diastolic" 
                                   min="40" max="120" placeholder="80">
                            <small class="bp-label">ตัวล่าง</small>
                        </div>
                        <span class="input-unit">mmHg</span>
                    </div>
                </div>

                <div class="vital-item">
                    <label for="heart_rate">
                        <i class="vital-icon heart-icon">💓</i>
                        <span class="vital-label">อัตราการเต้นของหัวใจ</span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="heart_rate" name="heart_rate" 
                               min="40" max="200" placeholder="72">
                        <span class="input-unit">bpm</span>
                    </div>
                </div>

                <div class="vital-item">
                    <label for="respiratory_rate">
                        <i class="vital-icon respiratory-icon">🫁</i>
                        <span class="vital-label">อัตราการหายใจ</span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="respiratory_rate" name="respiratory_rate" 
                               min="8" max="40" placeholder="16">
                        <span class="input-unit">ครั้ง/นาที</span>
                    </div>
                </div>

                <div class="vital-item">
                    <label for="oxygen_saturation">
                        <i class="vital-icon oxygen-icon">🌬️</i>
                        <span class="vital-label">ความอิ่มตัวของออกซิเจน</span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="oxygen_saturation" name="oxygen_saturation" 
                               min="70" max="100" placeholder="98">
                        <span class="input-unit">%</span>
                    </div>
                </div>

                <div class="vital-item">
                    <label for="blood_sugar">
                        <i class="vital-icon sugar-icon">🩸</i>
                        <span class="vital-label">ระดับน้ำตาลในเลือด</span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="blood_sugar" name="blood_sugar" 
                               min="50" max="500" placeholder="100">
                        <span class="input-unit">mg/dL</span>
                    </div>
                </div>
            </div>

            <div class="form-group notes-group">
                <label for="additional_notes">
                    <i class="notes-icon">📝</i>
                    บันทึกเพิ่มเติม (ถ้ามี)
                </label>
                <textarea id="additional_notes" name="additional_notes" 
                         rows="4" placeholder="บันทึกข้อมูลเพิ่มเติมเกี่ยวกับสัญญาณชีพ..."></textarea>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeVitalSignsModal()">
                    ยกเลิก
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="save-icon">💾</i>
                    บันทึก
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Vital Signs Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(3px);
}

.vital-signs-modal {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    font-size: 2rem;
    color: white;
    cursor: pointer;
    line-height: 1;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.vital-signs-form {
    padding: 25px;
}

.form-row {
    margin-bottom: 20px;
}

.date-group {
    display: flex;
    gap: 15px;
    align-items: center;
    background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
    padding: 15px;
    border-radius: 12px;
    border: 1px solid rgba(41, 163, 126, 0.2);
}

.date-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--dark-green);
    margin-bottom: 0;
    white-space: nowrap;
}

.date-group input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

.vital-signs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.vital-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.vital-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-green);
}

.vital-item.has-data {
    border-color: var(--primary-green);
    background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
}

.vital-item.has-data .vital-icon {
    color: var(--primary-green);
}

.vital-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.vital-icon {
    font-size: 1.3rem;
    width: 30px;
    text-align: center;
}

.vital-label {
    font-size: 0.95rem;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-group input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(41, 163, 126, 0.1);
}

.input-group input::placeholder {
    color: #bbb;
    opacity: 0.6;
}

.blood-pressure-group input::placeholder {
    color: #bbb;
    opacity: 0.6;
}

.input-unit {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 40px;
}

.blood-pressure-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.bp-input-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.blood-pressure-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
}

.bp-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 4px;
    font-weight: 500;
}

.bp-separator {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-secondary);
}

.notes-group {
    background: linear-gradient(135deg, #f8fffe 0%, #eaf6f3 100%);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid rgba(41, 163, 126, 0.2);
}

.notes-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--dark-green);
}

.notes-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
}

.notes-group textarea:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(41, 163, 126, 0.1);
}

.notes-group textarea::placeholder {
    color: #bbb;
    opacity: 0.6;
}

.date-group input::placeholder {
    color: #bbb;
    opacity: 0.6;
}

.required {
    color: #e74c3c;
    font-weight: bold;
    font-size: 1.1em;
}

.form-notice {
    background: linear-gradient(135deg, #fff3cd 0%, #fdeaa7 100%);
    border: 1px solid #f0c674;
    border-radius: 8px;
    padding: 12px 15px;
    margin: 15px 0 20px 0;
    font-size: 0.9rem;
}

.form-notice p {
    margin: 3px 0;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-notice .required {
    color: #e74c3c;
}

.edit-info {
    margin: 15px 0 20px 0;
}

.info-card {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 15px;
}

.info-card h4 {
    margin: 0 0 10px 0;
    color: #155724;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-card p {
    margin: 5px 0;
    color: #155724;
    font-size: 0.9rem;
}

.info-card strong {
    font-weight: 600;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(41, 163, 126, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(41, 163, 126, 0.4);
}

/* Responsive */
@media (max-width: 768px) {
    .vital-signs-modal {
        width: 95%;
        margin: 10px;
    }
    
    .vital-signs-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .date-group {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
}
</style>

<script>
function openVitalSignsModal() {
    // เปิด modal โดยไม่ต้องเช็ค session ซับซ้อน - ใช้การเช็คง่ายๆ
    const elderlyId = document.querySelector('input[name="elderly_id"]').value;
    
    if (!elderlyId) {
        alert('ไม่พบรหัสผู้สูงอายุ');
        return;
    }

    // แสดง loading
    document.getElementById('modal-title').textContent = 'กำลังตรวจสอบข้อมูล...';
    document.getElementById('vitalSignsModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // ตรวจสอบว่าวันนี้มีข้อมูลแล้วหรือไม่ (ใช้ API ง่ายๆ)
    checkTodayVitalSigns(elderlyId);
}

function checkTodayVitalSigns(elderlyId) {
    // ใช้ API ง่ายๆ ที่ไม่ต้องเช็ค session ซับซ้อน
    fetch(`api/check_today_vital_signs_simple.php?elderly_id=${elderlyId}`, {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            console.log(`HTTP error! status: ${response.status}, using create mode`);
            setupCreateMode();
            return null;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return; // ถ้า response error แล้ว skip
        
        console.log('Check today response:', data);
        if (data.success) {
            if (data.has_today_record) {
                // โหมดแก้ไข - มีข้อมูลวันนี้แล้ว
                setupEditMode(data.data);
            } else {
                // โหมดสร้างใหม่ - ยังไม่มีข้อมูลวันนี้
                setupCreateMode();
            }
        } else {
            console.log('API returned error, using create mode:', data.message);
            // ถ้า API error ให้ใช้โหมดสร้างใหม่
            setupCreateMode();
        }
    })
    .catch(error => {
        console.log('Error checking today data, using create mode:', error.message);
        // ถ้าเกิด error ให้ใช้โหมดสร้างใหม่
        setupCreateMode();
    });
}

// ฟังก์ชันเดิมที่ซับซ้อน (สำหรับอนาคต)
function openVitalSignsModalOld() {
    // ตรวจสอบ session ก่อน
    fetch('api/check_session.php', {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (response.status === 401) {
            alert('Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่');
            window.location.href = 'index.php';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (!data.success || !data.authenticated) {
            alert('กรุณาเข้าสู่ระบบก่อนใช้งาน');
            window.location.href = 'index.php';
            return;
        }
        
        // ตรวจสอบก่อนว่าวันนี้มีการบันทึกแล้วหรือไม่
        const elderlyId = document.querySelector('input[name="elderly_id"]').value;
        
        if (!elderlyId) {
            alert('ไม่พบรหัสผู้สูงอายุ');
            return;
        }

        // แสดง loading
        document.getElementById('modal-title').textContent = 'กำลังตรวจสอบ...';
        document.getElementById('vitalSignsModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';

        // ตรวจสอบว่าวันนี้มีข้อมูลแล้วหรือไม่
        fetch(`api/check_today_vital_signs.php?elderly_id=${elderlyId}`, {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Check today response:', data);
        if (data.success) {
            if (data.has_today_record) {
                // โหมดแก้ไข - มีข้อมูลวันนี้แล้ว
                setupEditMode(data.data);
            } else {
                // โหมดสร้างใหม่ - ยังไม่มีข้อมูลวันนี้
                setupCreateMode();
            }
        } else {
            console.error('API Error:', data);
            alert('เกิดข้อผิดพลาด: ' + data.message + (data.debug ? '\nDebug: ' + data.debug : ''));
            closeVitalSignsModal();
        }
    })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการตรวจสอบข้อมูล: ' + error.message);
            closeVitalSignsModal();
        });
    })
    .catch(error => {
        console.error('Session check error:', error);
        alert('เกิดข้อผิดพลาดในการตรวจสอบ Session กรุณาลองใหม่');
    });
}

function setupCreateMode() {
    document.getElementById('modal-title').textContent = '📝 บันทึกสัญญาณชีพใหม่';
    document.getElementById('form_mode').value = 'create';
    document.getElementById('vital_sign_id').value = '';
    document.getElementById('edit-info').style.display = 'none';
    
    // รีเซ็ตฟอร์มเป็นค่าเริ่มต้น
    document.getElementById('vitalSignsForm').reset();
    document.getElementById('recorded_date').value = new Date().toISOString().split('T')[0];
    document.getElementById('recorded_time').value = new Date().toTimeString().substr(0,5);
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#vitalSignsForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">💾</i> บันทึกใหม่';
    
    // แสดงข้อความบอกว่ายังไม่มีข้อมูลวันนี้
    showCreateModeMessage();
    
    console.log('Setup create mode completed');
}

function showCreateModeMessage() {
    // เพิ่มข้อความแจ้งให้ทราบว่ายังไม่มีข้อมูลวันนี้
    const formNotice = document.querySelector('.form-notice');
    if (formNotice) {
        const today = new Date().toLocaleDateString('th-TH');
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #fff3cd 0%, #fdeaa7 100%); border: 1px solid #f0c674; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #856404; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #856404;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #856404;"><strong>สถานะ:</strong> ❌ ยังไม่มีการบันทึกสัญญาณชีพ</p>
                <p style="margin: 5px 0; color: #856404;"><strong>การดำเนินการ:</strong> 📝 ต้องการบันทึกข้อมูลใหม่หรือไม่?</p>
            </div>
            <p><span class="required">*</span> วันที่และเวลาจำเป็นต้องกรอก</p>
            <p>📌 กรุณากรอกสัญญาณชีพอย่างน้อย 1 รายการ</p>
        `;
    }
}

function setupEditMode(data) {
    document.getElementById('modal-title').textContent = '✏️ แก้ไขสัญญาณชีพวันนี้';
    document.getElementById('form_mode').value = 'edit';
    document.getElementById('vital_sign_id').value = data.id;
    
    // แสดงข้อมูลผู้บันทึก
    document.getElementById('recorded_by_display').textContent = data.recorded_by_name;
    document.getElementById('created_at_display').textContent = 
        new Date(data.created_at).toLocaleString('th-TH');
    document.getElementById('edit-info').style.display = 'block';
    
    // เติมข้อมูลในฟอร์ม
    document.getElementById('recorded_date').value = data.recorded_date;
    document.getElementById('recorded_time').value = data.recorded_time;
    document.getElementById('temperature').value = data.temperature || '';
    document.getElementById('blood_pressure_systolic').value = data.blood_pressure_systolic || '';
    document.getElementById('blood_pressure_diastolic').value = data.blood_pressure_diastolic || '';
    document.getElementById('heart_rate').value = data.heart_rate || '';
    document.getElementById('respiratory_rate').value = data.respiratory_rate || '';
    document.getElementById('oxygen_saturation').value = data.oxygen_saturation || '';
    document.getElementById('blood_sugar').value = data.blood_sugar || '';
    document.getElementById('additional_notes').value = data.additional_notes || '';
    
    // เปลี่ยนข้อความปุ่ม
    const submitBtn = document.querySelector('#vitalSignsForm button[type="submit"]');
    submitBtn.innerHTML = '<i class="save-icon">✏️</i> อัปเดต';
    
    // แสดงข้อความแจ้งว่ามีข้อมูลอยู่แล้วและสามารถแก้ไขได้
    showEditModeMessage(data);
    
    // อัปเดต visual feedback
    setTimeout(updateVitalItemVisual, 100);
    
    console.log('Setup edit mode completed for record ID:', data.id);
}

function showEditModeMessage(data) {
    // เพิ่มข้อความแจ้งให้ทราบว่ามีข้อมูลวันนี้แล้ว
    const formNotice = document.querySelector('.form-notice');
    if (formNotice) {
        const recordTime = new Date(data.recorded_date + 'T' + data.recorded_time).toLocaleString('th-TH');
        const today = new Date().toLocaleDateString('th-TH');
        formNotice.innerHTML = `
            <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #155724; display: flex; align-items: center; gap: 8px;">
                    📋 สถานะการบันทึก
                </h4>
                <p style="margin: 5px 0; color: #155724;"><strong>วันที่:</strong> ${today}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>สถานะ:</strong> ✅ มีการบันทึกสัญญาณชีพแล้ว</p>
                <p style="margin: 5px 0; color: #155724;"><strong>เวลาที่บันทึก:</strong> ${recordTime}</p>
                <p style="margin: 5px 0; color: #155724;"><strong>การดำเนินการ:</strong> ✏️ ต้องการแก้ไขข้อมูลหรือไม่?</p>
            </div>
            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <h4 style="margin: 0 0 10px 0; color: #495057;">📊 รายงานสัญญาณชีพปัจจุบัน</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    ${data.temperature ? `<p style="margin: 2px 0; color: #495057;"><strong>🌡️ อุณหภูมิ:</strong> ${data.temperature}°C</p>` : ''}
                    ${data.heart_rate ? `<p style="margin: 2px 0; color: #495057;"><strong>💓 ชีพจร:</strong> ${data.heart_rate} bpm</p>` : ''}
                    ${data.blood_pressure_systolic && data.blood_pressure_diastolic ? `<p style="margin: 2px 0; color: #495057;"><strong>🩸 ความดัน:</strong> ${data.blood_pressure_systolic}/${data.blood_pressure_diastolic} mmHg</p>` : ''}
                    ${data.respiratory_rate ? `<p style="margin: 2px 0; color: #495057;"><strong>🫁 อัตราหายใจ:</strong> ${data.respiratory_rate} ครั้ง/นาที</p>` : ''}
                    ${data.oxygen_saturation ? `<p style="margin: 2px 0; color: #495057;"><strong>🌬️ ออกซิเจน:</strong> ${data.oxygen_saturation}%</p>` : ''}
                    ${data.blood_sugar ? `<p style="margin: 2px 0; color: #495057;"><strong>🩸 น้ำตาล:</strong> ${data.blood_sugar} mg/dL</p>` : ''}
                </div>
                ${data.additional_notes ? `<p style="margin: 10px 0 5px 0; color: #495057;"><strong>📝 หมายเหตุ:</strong> ${data.additional_notes}</p>` : ''}
            </div>
            <p><span class="required">*</span> วันที่และเวลาจำเป็นต้องกรอก</p>
            <p>📌 กรุณากรอกสัญญาณชีพอย่างน้อย 1 รายการ</p>
        `;
    }
}

function closeVitalSignsModal() {
    document.getElementById('vitalSignsModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('vitalSignsForm').reset();
}

// Close modal when clicking outside
document.getElementById('vitalSignsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVitalSignsModal();
    }
});

// Form validation
function validateVitalSignsForm() {
    const requiredFields = ['recorded_date', 'recorded_time'];
    const vitalSignFields = ['temperature', 'blood_pressure_systolic', 'blood_pressure_diastolic', 
                           'heart_rate', 'respiratory_rate', 'oxygen_saturation', 'blood_sugar'];
    
    // Check required fields
    for (const fieldName of requiredFields) {
        const field = document.getElementById(fieldName);
        if (!field.value.trim()) {
            alert(`กรุณากรอก${fieldName === 'recorded_date' ? 'วันที่' : 'เวลา'}บันทึก`);
            field.focus();
            return false;
        }
    }
    
    // Check at least one vital sign
    let hasVitalSign = false;
    for (const fieldName of vitalSignFields) {
        const field = document.getElementById(fieldName);
        if (field && field.value.trim()) {
            hasVitalSign = true;
            break;
        }
    }
    
    if (!hasVitalSign) {
        alert('กรุณากรอกสัญญาณชีพอย่างน้อย 1 รายการ');
        return false;
    }
    
    // Validate blood pressure - if one is filled, both should be filled
    const systolic = document.getElementById('blood_pressure_systolic').value;
    const diastolic = document.getElementById('blood_pressure_diastolic').value;
    if ((systolic && !diastolic) || (!systolic && diastolic)) {
        alert('กรุณากรอกความดันโลหิตทั้งค่าต่ำสุดและสูงสุด');
        return false;
    }
    
    return true;
}

// Form submission
document.getElementById('vitalSignsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate form first
    if (!validateVitalSignsForm()) {
        return;
    }
    
    const formData = new FormData(this);
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="save-icon">⏳</i> กำลังบันทึก...';
    submitBtn.disabled = true;
    
    // เรียก API สำหรับบันทึกสัญญาณชีพ - ลองทั้ง 2 endpoint
    const apiEndpoint = 'api/save_vital_signs_simple.php';
    fetch(apiEndpoint, {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
            'Cache-Control': 'no-cache'
        }
    })
    .then(response => {
        console.log('Save response status:', response.status);
        
        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Session expired. Please refresh the page and login again.');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Save response data:', data);
        
        if (data.success) {
            alert('บันทึกสัญญาณชีพเรียบร้อยแล้ว');
            closeVitalSignsModal();
            // Update display with new data
            loadLatestVitalSigns();
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
            console.error('API Error:', data);
        }
    })
    .catch(error => {
        console.error('Main API Error:', error);
        console.log('Trying fallback debug API...');
        
        // Try fallback debug API
        return fetch('api/save_vital_signs_debug.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin',
            headers: {
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            console.log('Debug API response status:', response.status);
            if (!response.ok) {
                throw new Error(`Debug API failed with status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Debug API response data:', data);
            if (data.success) {
                alert('บันทึกสัญญาณชีพเรียบร้อยแล้ว (ใช้ Debug API)');
                closeVitalSignsModal();
                loadLatestVitalSigns();
            } else {
                throw new Error(data.message + (data.debug ? '\nDebug: ' + JSON.stringify(data.debug) : ''));
            }
        })
        .catch(debugError => {
            console.error('Both APIs failed:', debugError);
            if (error.message.includes('Session expired') || error.message.includes('status: 401')) {
                alert('Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่');
                window.location.href = 'index.php';
            } else if (error.message.includes('status: 500')) {
                alert('เกิดข้อผิดพลาดภายในระบบ กรุณาตรวจสอบ:\n1. ว่าได้เข้าสู่ระบบแล้ว\n2. มีสิทธิ์ในการบันทึกข้อมูล\n3. ข้อมูลที่กรอกถูกต้อง\n\nError details: ' + debugError.message);
            } else {
                alert('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' + debugError.message + '\n\nกรุณาตรวจสอบ:\n1. การเชื่อมต่อฐานข้อมูล\n2. ข้อมูล session\n3. ข้อมูลที่กรอก');
            }
        });
    })
    .finally(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Function to load latest vital signs
function loadLatestVitalSigns() {
    const elderlyIdInput = document.querySelector('input[name="elderly_id"]');
    if (!elderlyIdInput) {
        console.log('Elderly ID input not found');
        return;
    }
    
    const elderlyId = elderlyIdInput.value;
    if (!elderlyId || elderlyId <= 0) {
        console.log('Invalid elderly ID:', elderlyId);
        return;
    }
    
    fetch(`api/get_vital_signs.php?elderly_id=${elderlyId}&limit=1`, {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Cache-Control': 'no-cache'
        }
    })
    .then(response => {
        if (response.status === 401) {
            console.warn('Authentication required for loading vital signs');
            return { success: false, message: 'Authentication required' };
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.data && data.data.length > 0) {
            const latest = data.data[0];
            
            // Update display
            document.getElementById('latest-vital-date').textContent = 
                new Date(latest.recorded_date + 'T' + latest.recorded_time).toLocaleDateString('th-TH');
            
            document.getElementById('heart-rate-display').textContent = 
                latest.heart_rate ? latest.heart_rate + ' bpm' : '-';
                
            document.getElementById('blood-pressure-display').textContent = 
                (latest.blood_pressure_systolic && latest.blood_pressure_diastolic) ? 
                latest.blood_pressure_systolic + '/' + latest.blood_pressure_diastolic + ' mmHg' : '-';
                
            document.getElementById('respiratory-rate-display').textContent = 
                latest.respiratory_rate ? latest.respiratory_rate + ' ครั้ง/นาที' : '-';
                
            document.getElementById('temperature-display').textContent = 
                latest.temperature ? latest.temperature + '°C' : '-';
                
            document.getElementById('oxygen-saturation-display').textContent = 
                latest.oxygen_saturation ? latest.oxygen_saturation + '%' : '-';
        } else if (!data.success) {
            console.log('No vital signs data or auth required:', data.message);
        }
    })
    .catch(error => {
        console.error('Error loading vital signs:', error);
    });
}

// Real-time visual feedback for form inputs
function updateVitalItemVisual() {
    const vitalSignFields = ['temperature', 'blood_pressure_systolic', 'blood_pressure_diastolic', 
                           'heart_rate', 'respiratory_rate', 'oxygen_saturation', 'blood_sugar'];
    
    vitalSignFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            const vitalItem = field.closest('.vital-item');
            if (field.value.trim()) {
                vitalItem.classList.add('has-data');
            } else {
                vitalItem.classList.remove('has-data');
            }
        }
    });
}

// Add input event listeners for real-time feedback
function addInputListeners() {
    const vitalSignFields = ['temperature', 'blood_pressure_systolic', 'blood_pressure_diastolic', 
                           'heart_rate', 'respiratory_rate', 'oxygen_saturation', 'blood_sugar'];
    
    vitalSignFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('input', updateVitalItemVisual);
            field.addEventListener('blur', updateVitalItemVisual);
        }
    });
}

// Load vital signs when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for the page to fully load
    setTimeout(() => {
        loadLatestVitalSigns();
        addInputListeners();
    }, 500);
});
</script>