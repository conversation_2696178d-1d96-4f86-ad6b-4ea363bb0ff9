<?php
/**
 * Unified Session helper for API endpoints
 * Matches exactly with main application session configuration
 */

if (!defined('AIVORA_SECURITY')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ไม่อนุญาตให้เข้าถึงไฟล์นี้โดยตรง');
}

/**
 * Initialize session with EXACT same settings as main app
 */
function initializeAPISession() {
    if (session_status() === PHP_SESSION_NONE) {
        // EXACT same configuration as SecurityManager::secureSession()
        ini_set('session.cookie_httponly', '1');
        ini_set('session.cookie_secure', (isset($_SERVER['HTTPS']) ? '1' : '0'));
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.use_strict_mode', '1');
        ini_set('session.cookie_lifetime', '0');
        ini_set('session.gc_maxlifetime', '3600'); // 1 hour
        ini_set('session.name', 'AIVORA_SESSION'); // MUST match security.php
        
        session_start();
    }
}

/**
 * Check if user is authenticated - UNIFIED with auth.php logic
 */
function checkAuthentication() {
    if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] === null || $_SESSION['user_id'] === '') {
        http_response_code(401);
        echo json_encode([
            'success' => false, 
            'message' => 'Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่',
            'error_code' => 'NOT_AUTHENTICATED'
        ]);
        exit();
    }
    
    // Use EXACT same session expiry logic as auth.php SessionManager
    if (isset($_SESSION['last_activity'])) {
        $session_lifetime = 3600; // 1 hour - matches SessionManager::SESSION_LIFETIME
        $time_since_last_activity = time() - $_SESSION['last_activity'];
        
        if ($time_since_last_activity > $session_lifetime) {
            // Session expired - destroy session exactly like SessionManager
            session_unset();
            session_destroy();
            
            http_response_code(401);
            echo json_encode([
                'success' => false, 
                'message' => 'Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่',
                'error_code' => 'SESSION_EXPIRED'
            ]);
            exit();
        }
    } else {
        // If no last_activity, treat as expired
        http_response_code(401);
        echo json_encode([
            'success' => false, 
            'message' => 'Session หมดอายุ หรือไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบใหม่',
            'error_code' => 'SESSION_EXPIRED'
        ]);
        exit();
    }
    
    // Update last activity time to extend session (matches auth.php behavior)
    $_SESSION['last_activity'] = time();
}

/**
 * Check user role permissions
 */
function checkPermissions($allowed_roles = ['admin', 'facility_admin', 'staff']) {
    if (!isset($_SESSION['user_role']) || !in_array($_SESSION['user_role'], $allowed_roles)) {
        http_response_code(403);
        echo json_encode([
            'success' => false, 
            'message' => 'ไม่มีสิทธิ์ในการเข้าถึงข้อมูล',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ]);
        exit();
    }
}

/**
 * Get current user info - UNIFIED with auth.php SessionManager
 */
function getCurrentUser() {
    return [
        'user_id' => $_SESSION['user_id'] ?? null,
        'username' => $_SESSION['username'] ?? null,
        'user_name' => $_SESSION['user_name'] ?? null, // Matches SessionManager::getCurrentUser()
        'user_role' => $_SESSION['user_role'] ?? null,
        'facility_id' => $_SESSION['facility_id'] ?? null,
        'facility_name' => $_SESSION['facility_name'] ?? null,
    ];
}
?>