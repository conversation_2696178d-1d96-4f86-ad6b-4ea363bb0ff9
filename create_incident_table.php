<?php
require_once 'config/database.php';

echo "<h3>สร้างตาราง incident_reports</h3>";

try {
    // ลบตารางเก่าถ้ามี (ระวัง: จะลบข้อมูลทั้งหมด)
    $drop_sql = "DROP TABLE IF EXISTS incident_reports";
    if ($conn->query($drop_sql) === TRUE) {
        echo "<p>✅ ลบตารางเก่าเรียบร้อย (ถ้ามี)</p>";
    }
    
    // สร้างตารางใหม่
    $create_sql = "CREATE TABLE incident_reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        elderly_id INT NOT NULL,
        user_id INT NOT NULL,
        incident_datetime DATETIME NOT NULL,
        incident_type ENUM('fall', 'medication_error', 'injury', 'behavioral', 'wandering', 'choking', 'seizure', 'allergic_reaction', 'equipment_malfunction', 'visitor_related', 'staff_related', 'other') NOT NULL,
        severity ENUM('minor', 'moderate', 'serious', 'critical', 'fatal') NOT NULL DEFAULT 'minor',
        incident_description TEXT NOT NULL,
        immediate_action_taken TEXT NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_elderly_id (elderly_id),
        INDEX idx_incident_datetime (incident_datetime),
        INDEX idx_incident_type (incident_type),
        INDEX idx_severity (severity)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($create_sql) === TRUE) {
        echo "<p>✅ สร้างตาราง incident_reports สำเร็จ</p>";
        
        // แสดงโครงสร้างตารางที่สร้างขึ้น
        echo "<h4>โครงสร้างตารางที่สร้างขึ้น:</h4>";
        $desc_sql = "DESCRIBE incident_reports";
        $desc_result = $conn->query($desc_sql);
        
        if ($desc_result) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background-color: #f2f2f2;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while($row = $desc_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "<p><strong>✅ ตารางพร้อมใช้งานแล้ว!</strong></p>";
        echo "<p>สามารถใช้งานฟอร์มรายงานเหตุการณ์ได้แล้ว</p>";
        
    } else {
        echo "<p>❌ เกิดข้อผิดพลาดในการสร้างตาราง: " . $conn->error . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
}

$conn->close();
?>