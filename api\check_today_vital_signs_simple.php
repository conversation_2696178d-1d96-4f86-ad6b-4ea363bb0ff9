<?php
// API ง่ายๆ สำหรับตรวจสอบสัญญาณชีพวันนี้ โดยไม่ต้องเช็ค session ซับซ้อน
define('AIVORA_SECURITY', true);
header('Content-Type: application/json; charset=utf-8');

// เริ่ม session แบบง่าย
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตรวจสอบการเข้าสู่ระบบแบบง่าย
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false, 
        'message' => 'กรุณาเข้าสู่ระบบใหม่',
        'has_today_record' => false,
        'mode' => 'create'
    ]);
    exit();
}

// อัปเดต session activity
$_SESSION['last_activity'] = time();

try {
    // เชื่อมต่อฐานข้อมูล
    require_once __DIR__ . '/../config/database.php';
    
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้: ' . (isset($conn) ? $conn->connect_error : 'Connection not established'));
    }
    
    // รับพารามิเตอร์
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;

    if ($elderly_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรหัสผู้สูงอายุ']);
        exit();
    }

    // ตรวจสอบว่าตาราง elderly_vital_signs มีอยู่หรือไม่
    $table_check = $conn->query("SHOW TABLES LIKE 'elderly_vital_signs'");
    if ($table_check->num_rows == 0) {
        // ยังไม่มีตาราง - ส่งกลับว่าไม่มีข้อมูล
        echo json_encode([
            'success' => true,
            'has_today_record' => false,
            'mode' => 'create',
            'message' => 'ยังไม่มีตารางข้อมูล - สร้างใหม่'
        ]);
        exit();
    }

    // ตรวจสอบว่าวันนี้มีการบันทึกแล้วหรือไม่ - ใช้ตาราง elderly_vital_signs
    $today = date('Y-m-d');
    $sql = "SELECT vs.*, COALESCE(u.name, u.username, 'ระบบ') as user_name 
            FROM elderly_vital_signs vs 
            LEFT JOIN users u ON vs.recorded_by = u.id 
            WHERE vs.elderly_id = ? AND vs.recorded_date = ?
            ORDER BY vs.recorded_time DESC 
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL']);
        exit();
    }

    $stmt->bind_param("is", $elderly_id, $today);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // มีข้อมูลวันนี้แล้ว - แสดงข้อมูลสำหรับการแก้ไข
        $vital_signs = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'has_today_record' => true,
            'mode' => 'edit',
            'data' => [
                'id' => $vital_signs['id'],
                'recorded_date' => $vital_signs['recorded_date'],
                'recorded_time' => $vital_signs['recorded_time'],
                'temperature' => $vital_signs['temperature'],
                'blood_pressure_systolic' => $vital_signs['blood_pressure_systolic'],
                'blood_pressure_diastolic' => $vital_signs['blood_pressure_diastolic'],
                'heart_rate' => $vital_signs['heart_rate'],
                'respiratory_rate' => $vital_signs['respiratory_rate'],
                'oxygen_saturation' => $vital_signs['oxygen_saturation'],
                'blood_sugar' => $vital_signs['blood_sugar'],
                'additional_notes' => $vital_signs['additional_notes'],
                'recorded_by_name' => $vital_signs['user_name'],
                'created_at' => $vital_signs['created_at']
            ]
        ]);
    } else {
        // ยังไม่มีข้อมูลวันนี้ - โหมดเพิ่มใหม่
        echo json_encode([
            'success' => true,
            'has_today_record' => false,
            'mode' => 'create'
        ]);
    }

    $stmt->close();

} catch (Exception $e) {
    error_log("Error in check_today_vital_signs_simple.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'เกิดข้อผิดพลาดระบบ: ' . $e->getMessage(),
        'mode' => 'create' // ถ้า error ให้ใช้โหมดสร้างใหม่
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>