<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// เริ่ม session และตรวจสอบการเข้าสู่ระบบ
session_start();

// ตรวจสอบว่าผู้ใช้เข้าสู่ระบบแล้วหรือไม่
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'กรุณาเข้าสู่ระบบ']);
    exit;
}

try {
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../includes/functions.php';

    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Invalid request method');
    }

    // ตรวจสอบข้อมูลที่จำเป็น
    $elderly_id = isset($_GET['elderly_id']) ? (int)$_GET['elderly_id'] : 0;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

    if (!$elderly_id) {
        throw new Exception('ไม่พบ elderly_id');
    }

    // ตรวจสอบสิทธิ์การเข้าถึงผู้สูงอายุ
    $user_role = $_SESSION['user_role'];
    if ($user_role !== 'admin') {
        // ตรวจสอบว่าผู้สูงอายุอยู่ในสถานพยาบาลเดียวกันหรือไม่
        $check_sql = "SELECT id FROM elderly WHERE id = ? AND facility_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("ii", $elderly_id, $_SESSION['facility_id']);
        $check_stmt->execute();
        
        if ($check_stmt->get_result()->num_rows === 0) {
            throw new Exception('ไม่มีสิทธิ์เข้าถึงข้อมูลผู้สูงอายุนี้');
        }
    }

    // ดึงข้อมูล feeding records
    $sql = "SELECT 
                f.*,
                u.name as recorded_by_name,
                DATE_FORMAT(f.feeding_date, '%d/%m/%Y') as date_thai,
                DATE_FORMAT(f.feeding_time, '%H:%i') as time_thai,
                DATE_FORMAT(f.recorded_datetime, '%d/%m/%Y %H:%i') as recorded_datetime_thai
            FROM feeding_detailed_records f
            LEFT JOIN users u ON f.recorded_by = u.id
            WHERE f.elderly_id = ?
            ORDER BY f.feeding_date DESC, f.feeding_time DESC
            LIMIT ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('เกิดข้อผิดพลาดในการเตรียมคำสั่ง SQL: ' . $conn->error);
    }

    $stmt->bind_param("ii", $elderly_id, $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $records = [];
    while ($row = $result->fetch_assoc()) {
        // แปลง JSON data กลับเป็น array
        $row['oral_food_amount'] = $row['oral_food_amount'] ? json_decode($row['oral_food_amount'], true) : [];
        $row['choking'] = $row['choking'] ? json_decode($row['choking'], true) : [];
        $row['tube_food_type'] = $row['tube_food_type'] ? json_decode($row['tube_food_type'], true) : [];
        $row['tube_food_tolerance'] = $row['tube_food_tolerance'] ? json_decode($row['tube_food_tolerance'], true) : [];
        $row['gastric_residue'] = $row['gastric_residue'] ? json_decode($row['gastric_residue'], true) : [];
        $row['feeding_status'] = $row['feeding_status'] ? json_decode($row['feeding_status'], true) : [];
        $row['images'] = $row['images'] ? json_decode($row['images'], true) : [];
        
        // แปลงค่าเป็นภาษาไทย
        $row['oral_food_amount_thai'] = translateFeedingOptions($row['oral_food_amount'], 'oral_food_amount');
        $row['choking_thai'] = translateFeedingOptions($row['choking'], 'choking');
        $row['tube_food_type_thai'] = translateFeedingOptions($row['tube_food_type'], 'tube_food_type');
        $row['tube_food_tolerance_thai'] = translateFeedingOptions($row['tube_food_tolerance'], 'tube_food_tolerance');
        $row['gastric_residue_thai'] = translateFeedingOptions($row['gastric_residue'], 'gastric_residue');
        $row['feeding_status_thai'] = translateFeedingOptions($row['feeding_status'], 'feeding_status');
        
        $records[] = $row;
    }

    // คำนวณสถิติ
    $summary = [
        'total_records' => count($records),
        'today_count' => 0,
        'this_week_count' => 0
    ];

    $today = date('Y-m-d');
    $week_ago = date('Y-m-d', strtotime('-7 days'));

    foreach ($records as $record) {
        if ($record['feeding_date'] === $today) {
            $summary['today_count']++;
        }
        if ($record['feeding_date'] >= $week_ago) {
            $summary['this_week_count']++;
        }
    }

    // ส่งผลลัพธ์กลับ
    echo json_encode([
        'success' => true,
        'message' => 'ดึงข้อมูลการทาน/ฟีดอาหารเรียบร้อยแล้ว',
        'data' => [
            'records' => $records,
            'summary' => $summary
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => basename(__FILE__),
            'line' => __LINE__ - 5,
            'error' => $e->getMessage()
        ]
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

function translateFeedingOptions($options, $type) {
    if (empty($options) || !is_array($options)) {
        return [];
    }
    
    $translations = [
        'oral_food_amount' => [
            'all' => 'ทานได้หมด',
            'poor' => 'ทานไม่ค่อยได้',
            'half' => 'ทานได้ครึ่งหนึ่ง',
            'snack' => 'ทานอาหารว่าง'
        ],
        'choking' => [
            'no' => 'ไม่สำลัก',
            'yes' => 'สำลัก'
        ],
        'tube_food_type' => [
            'liquid' => 'อาหารเหลว',
            'milk' => 'นม'
        ],
        'tube_food_tolerance' => [
            'good' => 'ฟีดรับได้ดี',
            'little' => 'ฟีดรับได้เล็กน้อย',
            'none' => 'ฟีดรับไม่ได้เลย'
        ],
        'gastric_residue' => [
            'none' => 'ไม่มี',
            'over_50cc' => 'เหลือค้างมากกว่า 50cc',
            'blood' => 'สีแดง (มีเลือดปน)',
            'bile' => 'สีเขียว (มีน้ำดีปน)'
        ],
        'feeding_status' => [
            'completed' => 'ทานหมด',
            'incomplete' => 'ทานไม่หมด'
        ]
    ];
    
    $result = [];
    foreach ($options as $option) {
        if (isset($translations[$type][$option])) {
            $result[] = $translations[$type][$option];
        }
    }
    
    return $result;
}
?>