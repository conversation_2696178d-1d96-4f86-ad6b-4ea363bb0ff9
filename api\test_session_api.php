<?php
/**
 * Test API endpoint to verify session authentication works
 */

define('AIVORA_SECURITY', true);

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Include session helper
require_once __DIR__ . '/session_helper.php';

// Initialize session with unified settings
initializeAPISession();

try {
    // Test authentication
    checkAuthentication();
    checkPermissions(['admin', 'facility_admin', 'staff']);
    
    // Get current user info
    $currentUser = getCurrentUser();
    
    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Session authentication test successful!',
        'data' => [
            'user_id' => $currentUser['user_id'],
            'user_name' => $currentUser['user_name'],
            'user_role' => $currentUser['user_role'],
            'facility_id' => $currentUser['facility_id'],
            'session_id' => session_id(),
            'session_name' => session_name(),
            'last_activity' => $_SESSION['last_activity'] ?? null,
            'time_now' => time(),
            'session_remaining' => isset($_SESSION['last_activity']) ? 3600 - (time() - $_SESSION['last_activity']) : 0
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'session_status' => session_status(),
            'session_id' => session_id(),
            'session_name' => session_name(),
            'user_id_set' => isset($_SESSION['user_id']),
            'last_activity_set' => isset($_SESSION['last_activity']),
            'session_data_keys' => array_keys($_SESSION ?? [])
        ]
    ]);
}
?>