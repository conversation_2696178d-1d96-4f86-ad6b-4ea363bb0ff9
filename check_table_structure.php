<?php
// check_table_structure.php - ตรวจสอบโครงสร้างตาราง

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

echo "<h1>ตรวจสอบโครงสร้างตาราง</h1>\n";

try {
    require_once __DIR__ . '/config/database.php';
    
    if (!$conn) {
        throw new Exception("ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
    }
    
    echo "<p style='color: green;'>✓ เชื่อมต่อฐานข้อมูลสำเร็จ</p>\n";
    
    // ตารางที่ต้องตรวจสอบ
    $tables = ['elderly', 'users', 'facilities', 'medication_records'];
    
    foreach ($tables as $table) {
        echo "<hr>\n";
        echo "<h2>ตาราง: $table</h2>\n";
        
        // ตรวจสอบว่าตารางมีอยู่หรือไม่
        $check_table = $conn->query("SHOW TABLES LIKE '$table'");
        
        if ($check_table && $check_table->num_rows > 0) {
            echo "<p style='color: green;'>✓ ตาราง $table มีอยู่</p>\n";
            
            // แสดงโครงสร้างตาราง
            $describe = $conn->query("DESCRIBE $table");
            if ($describe) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
                echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
                
                while ($row = $describe->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td><strong>" . htmlspecialchars($row['Field']) . "</strong></td>";
                    echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                    echo "</tr>\n";
                }
                echo "</table>\n";
                
                // แสดงข้อมูลตัวอย่างสำหรับตาราง elderly และ users
                if ($table === 'elderly') {
                    $sample = $conn->query("SELECT * FROM $table LIMIT 3");
                    if ($sample && $sample->num_rows > 0) {
                        echo "<h4>ข้อมูลตัวอย่าง (3 รายการแรก):</h4>\n";
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
                        
                        // Header
                        $first_row = $sample->fetch_assoc();
                        echo "<tr style='background: #e7f3ff;'>";
                        foreach (array_keys($first_row) as $column) {
                            echo "<th>" . htmlspecialchars($column) . "</th>";
                        }
                        echo "</tr>\n";
                        
                        // Data
                        $sample->data_seek(0); // Reset pointer
                        while ($row = $sample->fetch_assoc()) {
                            echo "<tr>";
                            foreach ($row as $value) {
                                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                            }
                            echo "</tr>\n";
                        }
                        echo "</table>\n";
                    }
                }
                
                if ($table === 'users') {
                    $user_sample = $conn->query("SELECT id, username, name, role, facility_id, status FROM $table LIMIT 3");
                    if ($user_sample && $user_sample->num_rows > 0) {
                        echo "<h4>ข้อมูลผู้ใช้ตัวอย่าง:</h4>\n";
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>\n";
                        echo "<tr style='background: #e7f3ff;'><th>ID</th><th>Username</th><th>Name</th><th>Role</th><th>Facility ID</th><th>Status</th></tr>\n";
                        
                        while ($row = $user_sample->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['username']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['role']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['facility_id'] ?? 'NULL') . "</td>";
                            echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                            echo "</tr>\n";
                        }
                        echo "</table>\n";
                    }
                }
                
            } else {
                echo "<p style='color: red;'>ไม่สามารถอ่านโครงสร้างตาราง $table ได้</p>\n";
            }
            
            // นับจำนวนข้อมูล
            $count_result = $conn->query("SELECT COUNT(*) as total FROM $table");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['total'];
                echo "<p><strong>จำนวนข้อมูล:</strong> $count รายการ</p>\n";
            }
            
        } else {
            echo "<p style='color: orange;'>! ตาราง $table ไม่มีในฐานข้อมูล</p>\n";
        }
    }
    
    // ตรวจสอบ Foreign Keys
    echo "<hr>\n";
    echo "<h2>ตรวจสอบ Foreign Keys</h2>\n";
    
    $fk_query = "
    SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
    FROM 
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE 
        REFERENCED_TABLE_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ";
    
    $fk_result = $conn->query($fk_query);
    if ($fk_result && $fk_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'><th>Table</th><th>Column</th><th>References</th><th>Constraint</th></tr>\n";
        
        while ($fk = $fk_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($fk['TABLE_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($fk['COLUMN_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($fk['REFERENCED_TABLE_NAME'] . '.' . $fk['REFERENCED_COLUMN_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($fk['CONSTRAINT_NAME']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>ไม่พบ Foreign Keys ในฐานข้อมูล</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<h2>สรุปและข้อเสนอแนะ</h2>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>หากพบปัญหา Foreign Key:</h3>\n";
echo "<ol>\n";
echo "<li>ตรวจสอบชื่อคอลัมน์ใน elderly table</li>\n";
echo "<li>อัปเดต medication_records table ให้ตรงกับโครงสร้างที่มีอยู่</li>\n";
echo "<li>สร้างตารางใหม่หากจำเป็น</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<hr>\n";
echo "<h3>เครื่องมือ:</h3>\n";
echo "<p><a href='fix_medication_table.php'>แก้ไขตาราง Medication Records</a></p>\n";
echo "<p><a href='fix_medication_session.php'>กลับไปทดสอบ Medication</a></p>\n";
echo "<p><a href='?page=elderly'>กลับหน้าผู้สูงอายุ</a></p>\n";
?>