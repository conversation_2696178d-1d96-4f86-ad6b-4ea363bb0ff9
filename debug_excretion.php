<?php
define('AIVORA_SECURITY', true);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/config/database.php';

// ล็อกอินอัตโนมัติสำหรับทดสอบ
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['facility_id'] = 1;
$_SESSION['last_activity'] = time();

echo "🔍 Debug การบันทึกการขับถ่าย\n";
echo "================================\n";

// จำลองข้อมูลที่จะส่ง
$elderly_id = 1;
$record_date = date('Y-m-d');
$urine_frequency = 5;
$urine_volume = 300;
$urine_method = 'self';
$stool_frequency = 1;
$stool_color = 'น้ำตาล';
$stool_consistency = 'normal';
$stool_volume = 150;
$notes = 'ทดสอบการบันทึก';

echo "📊 ข้อมูลที่จะบันทึก:\n";
echo "elderly_id: $elderly_id\n";
echo "urine_frequency: $urine_frequency\n";
echo "stool_frequency: $stool_frequency\n";
echo "stool_consistency: $stool_consistency\n";
echo "notes: $notes\n\n";

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if ($conn->connect_error) {
    die("❌ เชื่อมต่อฐานข้อมูลไม่ได้: " . $conn->connect_error);
}
echo "✅ เชื่อมต่อฐานข้อมูลสำเร็จ\n";

// ตรวจสอบว่าผู้สูงอายุมีอยู่
$elderly_check = $conn->prepare("SELECT id, facility_id FROM elderly WHERE id = ?");
$elderly_check->bind_param('i', $elderly_id);
$elderly_check->execute();
$elderly_result = $elderly_check->get_result();

if ($elderly_result->num_rows === 0) {
    die("❌ ไม่พบข้อมูลผู้สูงอายุ ID: $elderly_id\n");
}
echo "✅ พบข้อมูลผู้สูงอายุ ID: $elderly_id\n";

// แปลงข้อมูลให้เข้ากับโครงสร้างตารางที่มีอยู่
$facility_id = $_SESSION['facility_id'] ?? 1;
$bowel_movement = ($stool_frequency > 0) ? 1 : 0;
$bowel_consistency = $stool_consistency;
$bowel_color = $stool_color ?: 'ปกติ';
$bowel_amount = $stool_volume ? $stool_volume . ' ml' : 'ปกติ';
$urination = ($urine_frequency > 0) ? 1 : 0;
$urine_color = 'ปกติ';
$urine_amount = $urine_volume ? $urine_volume . ' ml' : 'ปกติ';
$frequency_today = max($urine_frequency ?: 0, $stool_frequency ?: 0);
$assistance_needed = ($urine_method === 'catheter') ? 'ต้องการความช่วยเหลือ' : 'ช่วยเหลือตัวเองได้';
$continence_status = ($urine_method === 'self') ? 'ควบคุมได้' : 'ควบคุมไม่ได้';

echo "\n📝 ข้อมูลที่แปลงแล้วสำหรับ database:\n";
echo "elderly_id: $elderly_id (int)\n";
echo "user_id: {$_SESSION['user_id']} (int)\n";
echo "facility_id: $facility_id (int)\n";
echo "bowel_movement: $bowel_movement (int)\n";
echo "bowel_consistency: $bowel_consistency (string)\n";
echo "bowel_color: $bowel_color (string)\n";
echo "bowel_amount: $bowel_amount (string)\n";
echo "urination: $urination (int)\n";
echo "urine_color: $urine_color (string)\n";
echo "urine_amount: $urine_amount (string)\n";
echo "frequency_today: $frequency_today (int)\n";
echo "assistance_needed: $assistance_needed (string)\n";
echo "continence_status: $continence_status (string)\n";
echo "notes: $notes (string)\n";

// SQL statement
$insert_sql = "
INSERT INTO care_excretion_records 
(elderly_id, user_id, facility_id, bowel_movement, bowel_consistency, bowel_color, bowel_amount, 
 urination, urine_color, urine_amount, frequency_today, assistance_needed, continence_status, notes) 
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
";

echo "\n📄 SQL Statement:\n$insert_sql\n";

$insert_stmt = $conn->prepare($insert_sql);

if (!$insert_stmt) {
    die("❌ เตรียม statement ไม่ได้: " . $conn->error . "\n");
}
echo "✅ เตรียม statement สำเร็จ\n";

// ตรวจสอบจำนวน parameters
echo "\n🔢 จำนวน parameters ที่ต้องการ: 14\n";
echo "Parameter types: iiiisssississs\n";

// Bind parameters
$bind_result = $insert_stmt->bind_param(
    'iiiisssississs',
    $elderly_id,           // int -> i
    $_SESSION['user_id'],  // int -> i  
    $facility_id,          // int -> i
    $bowel_movement,       // int -> i
    $bowel_consistency,    // string -> s
    $bowel_color,          // string -> s
    $bowel_amount,         // string -> s
    $urination,            // int -> i
    $urine_color,          // string -> s
    $urine_amount,         // string -> s
    $frequency_today,      // int -> i
    $assistance_needed,    // string -> s
    $continence_status,    // string -> s
    $notes                 // string -> s
);

if (!$bind_result) {
    die("❌ bind_param ไม่สำเร็จ: " . $insert_stmt->error . "\n");
}
echo "✅ bind_param สำเร็จ\n";

// Execute
$execute_result = $insert_stmt->execute();

if (!$execute_result) {
    die("❌ execute ไม่สำเร็จ: " . $insert_stmt->error . "\n");
}

$new_record_id = $conn->insert_id;
echo "✅ บันทึกข้อมูลสำเร็จ! ID: $new_record_id\n";

// ตรวจสอบข้อมูลที่บันทึก
$check_sql = "SELECT * FROM care_excretion_records WHERE id = ?";
$check_stmt = $conn->prepare($check_sql);
$check_stmt->bind_param('i', $new_record_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
$saved_data = $check_result->fetch_assoc();

echo "\n📋 ข้อมูลที่บันทึกแล้ว:\n";
print_r($saved_data);

$conn->close();
echo "\n🎉 การทดสอบเสร็จสิ้น\n";
?>