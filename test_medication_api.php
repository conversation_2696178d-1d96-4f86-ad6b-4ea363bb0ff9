<?php
// test_medication_api.php - ทดสอบ API การบันทึกยา

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

// เริ่ม session
session_start();

// จำลองการเข้าสู่ระบบ (สำหรับการทดสอบ)
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_role'] = 'admin';
    $_SESSION['username'] = 'admin';
    $_SESSION['facility_id'] = 1;
}

echo "<h1>ทดสอบ API การบันทึกยา</h1>\n";

// ตรวจสอบว่ามีการส่งข้อมูลมาหรือไม่
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>ผลการทดสอบ:</h2>\n";
    
    // เรียก API ด้วย cURL
    $url = 'http://localhost:8080/aivora/api/save_medication.php';
    
    $postData = $_POST;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    curl_setopt($ch, CURLOPT_HEADER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>\n";
    echo "<p><strong>Response:</strong></p>\n";
    echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
    
    // แสดงข้อมูลที่ส่งไป
    echo "<h3>ข้อมูลที่ส่งไป:</h3>\n";
    echo "<pre>" . htmlspecialchars(print_r($postData, true)) . "</pre>\n";
    
} else {
    echo "<h2>ฟอร์มทดสอบ:</h2>\n";
}
?>

<form method="POST" style="max-width: 600px;">
    <div style="margin-bottom: 15px;">
        <label for="elderly_id">ID ผู้สูงอายุ:</label><br>
        <input type="number" id="elderly_id" name="elderly_id" value="1" required>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="medication_date">วันที่ให้ยา:</label><br>
        <input type="date" id="medication_date" name="medication_date" value="<?php echo date('Y-m-d'); ?>" required>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="medication_name">ชื่อยา:</label><br>
        <input type="text" id="medication_name" name="medication_name" value="Paracetamol" required>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="medication_dosage">ขนาดยา:</label><br>
        <input type="text" id="medication_dosage" name="medication_dosage" value="500mg" required>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="medication_route">วิธีการให้:</label><br>
        <select id="medication_route" name="medication_route" required>
            <option value="oral" selected>รับประทาน (Oral)</option>
            <option value="injection">ฉีด (Injection)</option>
            <option value="topical">ทาผิวหนัง (Topical)</option>
            <option value="inhaled">สูดดม (Inhaled)</option>
            <option value="sublingual">ใต้ลิ้น (Sublingual)</option>
            <option value="rectal">ทางทวารหนัก (Rectal)</option>
            <option value="other">อื่นๆ</option>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>เวลาที่ให้ยา:</label><br>
        <input type="checkbox" name="time_periods[]" value="morning" checked> เช้า
        <input type="time" name="morning_time" value="08:00"><br>
        
        <input type="checkbox" name="time_periods[]" value="evening" checked> เย็น
        <input type="time" name="evening_time" value="19:00"><br>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="medication_notes">หมายเหตุ:</label><br>
        <textarea id="medication_notes" name="medication_notes" rows="3">ให้หลังอาหาร</textarea>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="given_by">ผู้ให้ยา:</label><br>
        <input type="text" id="given_by" name="given_by" value="พยาบาลทดสอบ" required>
    </div>
    
    <button type="submit" style="background: #9C27B0; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        ทดสอบบันทึกยา
    </button>
</form>

<hr>
<h3>ตรวจสอบข้อมูลในฐานข้อมูล:</h3>
<?php
try {
    require_once __DIR__ . '/config/database.php';
    
    // ตรวจสอบว่าตารางมีอยู่หรือไม่
    $check_table = $conn->query("SHOW TABLES LIKE 'medication_records'");
    if ($check_table && $check_table->num_rows > 0) {
        echo "<p style='color: green;'>✓ ตาราง medication_records มีอยู่แล้ว</p>\n";
        
        // นับจำนวนข้อมูล
        $count_result = $conn->query("SELECT COUNT(*) as total FROM medication_records");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['total'];
            echo "<p>จำนวนข้อมูลในตาราง: <strong>$count</strong> รายการ</p>\n";
            
            // แสดงข้อมูล 5 รายการล่าสุด
            if ($count > 0) {
                echo "<h4>ข้อมูล 5 รายการล่าสุด:</h4>\n";
                $recent_data = $conn->query("
                    SELECT mr.*, e.first_name, e.last_name 
                    FROM medication_records mr 
                    LEFT JOIN elderly e ON mr.elderly_id = e.id 
                    ORDER BY mr.recorded_datetime DESC 
                    LIMIT 5
                ");
                
                if ($recent_data && $recent_data->num_rows > 0) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
                    echo "<tr><th>ID</th><th>ผู้สูงอายุ</th><th>ยา</th><th>ขนาด</th><th>ช่วงเวลา</th><th>เวลา</th><th>วันที่บันทึก</th></tr>\n";
                    
                    while ($row = $recent_data->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['medication_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['medication_dosage']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['time_period']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['administration_time']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['recorded_datetime']) . "</td>";
                        echo "</tr>\n";
                    }
                    echo "</table>\n";
                }
            }
        }
    } else {
        echo "<p style='color: red;'>✗ ตาราง medication_records ยังไม่มีในฐานข้อมูล</p>\n";
        echo "<p><a href='create_medication_table.php'>คลิกที่นี่เพื่อสร้างตาราง</a></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<hr>
<p><a href="?page=elderly">กลับไปหน้าผู้สูงอายุ</a></p>