<?php
// get_excretion.php - API สำหรับดึงข้อมูลการขับถ่ายที่บันทึกไว้

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

header('Content-Type: application/json; charset=utf-8');

// เริ่ม session และโหลด auth system
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// โหลด auth functions และ functions.php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์การเข้าถึง
if (!hasPermission('elderly')) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'คุณไม่มีสิทธิ์ในการดูข้อมูลการขับถ่าย'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

try {
    // รับพารามิเตอร์
    $elderly_id = (int)($_GET['elderly_id'] ?? 0);
    $limit = (int)($_GET['limit'] ?? 10);
    $limit = min($limit, 50); // จำกัดสูงสุด 50 รายการ

    if (!$elderly_id) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณาระบุรหัสผู้สูงอายุ'
        ]);
        exit;
    }

    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและอยู่ในสถานพยาบาลเดียวกัน
    $elderly_check_sql = "SELECT id, facility_id FROM elderly WHERE id = ?";
    $elderly_check_params = [$elderly_id];
    
    if ($_SESSION['user_role'] !== 'admin') {
        $elderly_check_sql .= " AND facility_id = ?";
        $elderly_check_params[] = $_SESSION['facility_id'];
    }
    
    $elderly_check_stmt = $conn->prepare($elderly_check_sql);
    $elderly_check_stmt->bind_param(str_repeat('i', count($elderly_check_params)), ...$elderly_check_params);
    $elderly_check_stmt->execute();
    $elderly_result = $elderly_check_stmt->get_result();
    
    if ($elderly_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือคุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้'
        ]);
        exit;
    }

    // ดึงข้อมูลการขับถ่าย (ใช้ตาราง care_excretion_records)
    $records_sql = "
    SELECT 
        id,
        DATE(recorded_at) as date,
        TIME(recorded_at) as time,
        bowel_movement,
        bowel_consistency,
        bowel_color,
        bowel_amount,
        urination,
        urine_color,
        urine_amount,
        frequency_today,
        assistance_needed,
        continence_status,
        notes,
        recorded_at
    FROM care_excretion_records 
    WHERE elderly_id = ? 
    ORDER BY recorded_at DESC 
    LIMIT ?
    ";
    
    $records_stmt = $conn->prepare($records_sql);
    $records_stmt->bind_param('ii', $elderly_id, $limit);
    $records_stmt->execute();
    $records_result = $records_stmt->get_result();
    
    $records = [];
    while ($row = $records_result->fetch_assoc()) {
        // แปลงข้อมูลให้เข้ากับ format ที่ frontend ต้องการ
        $records[] = [
            'id' => $row['id'],
            'date' => $row['date'],
            'time' => $row['time'],
            'datetime' => $row['recorded_at'],
            'urine_frequency' => $row['frequency_today'] ?: 0,
            'urine_method' => $row['continence_status'] === 'ควบคุมได้' ? 'self' : 'catheter',
            'stool_frequency' => $row['bowel_movement'] ? 1 : 0,
            'stool_consistency' => $row['bowel_consistency'] ?: 'normal',
            'stool_color' => $row['bowel_color'] ?: 'น้ำตาล',
            'notes' => $row['notes']
        ];
    }

    // ดึงสถิติสรุป
    $today = date('Y-m-d');
    $summary_sql = "
    SELECT 
        COUNT(*) as total_records,
        SUM(CASE WHEN DATE(recorded_at) = ? THEN frequency_today ELSE 0 END) as today_urine,
        SUM(CASE WHEN DATE(recorded_at) = ? AND bowel_movement = 1 THEN 1 ELSE 0 END) as today_stool,
        AVG(frequency_today) as avg_frequency
    FROM care_excretion_records 
    WHERE elderly_id = ?
    ";
    
    $summary_stmt = $conn->prepare($summary_sql);
    $summary_stmt->bind_param('ssi', $today, $today, $elderly_id);
    $summary_stmt->execute();
    $summary_result = $summary_stmt->get_result();
    $summary = $summary_result->fetch_assoc();

    echo json_encode([
        'success' => true,
        'message' => 'ดึงข้อมูลการขับถ่ายสำเร็จ',
        'data' => [
            'records' => $records,
            'summary' => [
                'total_records' => (int)$summary['total_records'],
                'today_urine' => (int)($summary['today_urine'] ?: 0),
                'today_stool' => (int)($summary['today_stool'] ?: 0),
                'avg_frequency' => round($summary['avg_frequency'] ?: 0, 1)
            ]
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_excretion.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในการดึงข้อมูล: ' . $e->getMessage()
    ]);
}

$conn->close();
?>