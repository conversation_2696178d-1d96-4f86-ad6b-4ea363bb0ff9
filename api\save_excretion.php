<?php
// save_excretion.php - API สำหรับบันทึกการขับถ่าย

// ป้องกันการเข้าถึงไฟล์โดยตรง
if (!defined('AIVORA_SECURITY')) {
    define('AIVORA_SECURITY', true);
}

header('Content-Type: application/json; charset=utf-8');

// เริ่ม session และโหลด auth system
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// โหลด auth functions และ functions.php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Only POST requests are accepted.'
    ]);
    exit;
}

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'กรุณาเข้าสู่ระบบก่อนใช้งาน'
    ]);
    exit;
}

// ตรวจสอบสิทธิ์การเข้าถึง
if (!hasPermission('elderly')) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'คุณไม่มีสิทธิ์ในการบันทึกข้อมูลการขับถ่าย'
    ]);
    exit;
}

// เชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/../config/database.php';

try {
    // รับข้อมูลจากฟอร์ม
    $elderly_id = (int)($_POST['elderly_id'] ?? 0);
    $record_date = trim($_POST['record_date'] ?? '');
    
    // ข้อมูลปัสสาวะ
    $urine_frequency = intval($_POST['urine_frequency'] ?? 0) ?: null;
    $urine_volume = intval($_POST['urine_volume'] ?? 0) ?: null;
    $urine_method = trim($_POST['urine_method'] ?? 'self');
    $urine_other_text = trim($_POST['urine_other_text'] ?? '') ?: null;
    
    // ข้อมูลอุจจาระ  
    $stool_frequency = intval($_POST['stool_frequency'] ?? 0) ?: null;
    $stool_color = trim($_POST['stool_color'] ?? '') ?: null;
    $stool_consistency = trim($_POST['stool_consistency'] ?? 'normal');
    $stool_volume = intval($_POST['stool_volume'] ?? 0) ?: null;
    
    // ข้อมูลเพิ่มเติม
    $notes = trim($_POST['notes'] ?? '') ?: null;
    $recorded_by_name = trim($_POST['recorded_by_name'] ?? '') ?: ($_SESSION['username'] ?? 'Unknown');

    // ตรวจสอบข้อมูลที่จำเป็น
    if (!$elderly_id || !$record_date) {
        echo json_encode([
            'success' => false,
            'message' => 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน (รหัสผู้สูงอายุ และวันที่)',
            'debug' => [
                'elderly_id' => $elderly_id,
                'record_date' => $record_date
            ]
        ]);
        exit;
    }

    // ตรวจสอบค่าที่อนุญาต
    $allowed_urine_methods = ['self', 'catheter', 'other'];
    if (!in_array($urine_method, $allowed_urine_methods)) {
        $urine_method = 'self';
    }
    
    $allowed_stool_consistency = ['normal', 'liquid', 'semi_liquid', 'hard'];
    if (!in_array($stool_consistency, $allowed_stool_consistency)) {
        $stool_consistency = 'normal';
    }

    // ตรวจสอบว่าผู้สูงอายุมีอยู่จริงและอยู่ในสถานพยาบาลเดียวกัน
    $elderly_check_sql = "SELECT id, facility_id FROM elderly WHERE id = ?";
    $elderly_check_params = [$elderly_id];
    
    if ($_SESSION['user_role'] !== 'admin') {
        $elderly_check_sql .= " AND facility_id = ?";
        $elderly_check_params[] = $_SESSION['facility_id'];
    }
    
    $elderly_check_stmt = $conn->prepare($elderly_check_sql);
    $elderly_check_stmt->bind_param(str_repeat('i', count($elderly_check_params)), ...$elderly_check_params);
    $elderly_check_stmt->execute();
    $elderly_result = $elderly_check_stmt->get_result();
    
    if ($elderly_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลผู้สูงอายุหรือคุณไม่มีสิทธิ์เข้าถึงข้อมูลนี้'
        ]);
        exit;
    }

    // จัดการอัพโหลดรูปภาพ
    $image_paths = [];
    if (isset($_FILES['excretion_images']) && !empty($_FILES['excretion_images']['tmp_name'][0])) {
        $upload_dir = __DIR__ . "/../assets/img/excretion/{$elderly_id}/";
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_files = 5;
        $max_file_size = 5 * 1024 * 1024; // 5MB
        
        for ($i = 0; $i < min(count($_FILES['excretion_images']['tmp_name']), $max_files); $i++) {
            if ($_FILES['excretion_images']['error'][$i] === UPLOAD_ERR_OK) {
                $file_tmp = $_FILES['excretion_images']['tmp_name'][$i];
                $file_type = $_FILES['excretion_images']['type'][$i];
                $file_size = $_FILES['excretion_images']['size'][$i];
                
                // ตรวจสอบประเภทไฟล์
                if (!in_array($file_type, $allowed_types)) {
                    continue;
                }
                
                // ตรวจสอบขนาดไฟล์
                if ($file_size > $max_file_size) {
                    continue;
                }
                
                // สร้างชื่อไฟล์ใหม่
                $file_extension = pathinfo($_FILES['excretion_images']['name'][$i], PATHINFO_EXTENSION);
                $new_filename = 'excretion_' . date('YmdHis') . '_' . $i . '.' . $file_extension;
                $file_path = $upload_dir . $new_filename;
                
                // อัพโหลดไฟล์
                if (move_uploaded_file($file_tmp, $file_path)) {
                    $image_paths[] = "assets/img/excretion/{$elderly_id}/" . $new_filename;
                }
            }
        }
    }
    
    $image_path_string = !empty($image_paths) ? implode(',', $image_paths) : null;

    // เริ่ม transaction
    $conn->begin_transaction();

    try {
        // ใช้ตาราง care_excretion_records ที่มีอยู่แล้ว
        $insert_sql = "
        INSERT INTO care_excretion_records 
        (elderly_id, user_id, facility_id, bowel_movement, bowel_consistency, bowel_color, bowel_amount, 
         urination, urine_color, urine_amount, frequency_today, assistance_needed, continence_status, notes) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";
        
        $insert_stmt = $conn->prepare($insert_sql);
        
        // แปลงข้อมูลให้เข้ากับโครงสร้างตารางที่มีอยู่
        $facility_id = $_SESSION['facility_id'] ?? 1;
        $bowel_movement = ($stool_frequency > 0) ? 1 : 0;
        $bowel_consistency = $stool_consistency;
        $bowel_color = $stool_color ?: 'ปกติ';
        $bowel_amount = $stool_volume ? $stool_volume . ' ml' : 'ปกติ';
        $urination = ($urine_frequency > 0) ? 1 : 0;
        $urine_color = 'ปกติ'; // จะใช้ค่าเริ่มต้น
        $urine_amount = $urine_volume ? $urine_volume . ' ml' : 'ปกติ';
        $frequency_today = max($urine_frequency ?: 0, $stool_frequency ?: 0);
        $assistance_needed = ($urine_method === 'catheter') ? 'ต้องการความช่วยเหลือ' : 'ช่วยเหลือตัวเองได้';
        $continence_status = ($urine_method === 'self') ? 'ควบคุมได้' : 'ควบคุมไม่ได้';
        
        $insert_stmt->bind_param(
            'iiiisssississs',
            $elderly_id,           // int -> i
            $_SESSION['user_id'],  // int -> i  
            $facility_id,          // int -> i
            $bowel_movement,       // int -> i
            $bowel_consistency,    // string -> s
            $bowel_color,          // string -> s
            $bowel_amount,         // string -> s
            $urination,            // int -> i
            $urine_color,          // string -> s
            $urine_amount,         // string -> s
            $frequency_today,      // int -> i
            $assistance_needed,    // string -> s
            $continence_status,    // string -> s
            $notes                 // string -> s
        );
        
        if (!$insert_stmt->execute()) {
            throw new Exception('ไม่สามารถบันทึกข้อมูลการขับถ่ายได้: ' . $insert_stmt->error);
        }
        
        $new_record_id = $conn->insert_id;
        
        // commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'บันทึกการขับถ่ายเรียบร้อยแล้ว',
            'data' => [
                'id' => $new_record_id,
                'elderly_id' => $elderly_id,
                'record_date' => $record_date,
                'urine_frequency' => $urine_frequency,
                'stool_frequency' => $stool_frequency,
                'image_count' => count($image_paths)
            ]
        ]);
        
    } catch (Exception $e) {
        // rollback transaction
        $conn->rollback();
        
        // ลบไฟล์ที่อัพโหลดไปแล้วถ้าเกิดข้อผิดพลาด
        foreach ($image_paths as $path) {
            $full_path = __DIR__ . '/../' . $path;
            if (file_exists($full_path)) {
                unlink($full_path);
            }
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Error in save_excretion.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>