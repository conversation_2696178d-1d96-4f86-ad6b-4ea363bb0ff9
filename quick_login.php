<?php
define('AIVORA_SECURITY', true);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';

$message = '';

if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    if ($username && $password) {
        $result = loginUser($username, $password);
        
        if ($result['status'] === 'success') {
            header('Location: ?page=elderly_detail&id=1');
            exit;
        } else {
            $message = $result['message'];
        }
    } else {
        $message = 'กรุณากรอกชื่อผู้ใช้และรหัสผ่าน';
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Quick Login</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; }
        input { width: 100%; padding: 10px; margin: 5px 0; }
        button { width: 100%; padding: 10px; background: #29a37e; color: white; border: none; cursor: pointer; }
        .error { color: red; }
    </style>
</head>
<body>
    <h2>Quick Login for Testing</h2>
    
    <?php if ($message): ?>
        <p class="error"><?= htmlspecialchars($message) ?></p>
    <?php endif; ?>
    
    <form method="post">
        <input type="text" name="username" placeholder="Username" required>
        <input type="password" name="password" placeholder="Password" required>
        <button type="submit">Login</button>
    </form>
    
    <p><small>Current session status: <?= isLoggedIn() ? 'Logged In' : 'Not Logged In' ?></small></p>
    
    <?php if (isLoggedIn()): ?>
        <p><a href="?page=elderly_detail&id=1">Go to Elderly Detail</a></p>
    <?php endif; ?>
</body>
</html>