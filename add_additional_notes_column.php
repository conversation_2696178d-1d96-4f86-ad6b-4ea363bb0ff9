<?php
require_once 'config/database.php';

echo "Adding additional_notes column to vital_signs table...\n";

$sql = "ALTER TABLE vital_signs ADD COLUMN additional_notes TEXT NULL AFTER blood_sugar";

if ($conn->query($sql) === TRUE) {
    echo "Column additional_notes added successfully\n";
} else {
    if (strpos($conn->error, "Duplicate column name") !== false) {
        echo "Column additional_notes already exists\n";
    } else {
        echo "Error: " . $conn->error . "\n";
    }
}

// Verify the column was added
echo "\nChecking vital_signs table structure:\n";
$result = $conn->query("DESCRIBE vital_signs");
while($row = $result->fetch_assoc()) {
    echo $row['Field'] . " - " . $row['Type'] . "\n";
}
?>